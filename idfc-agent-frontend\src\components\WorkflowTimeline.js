import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  User, 
  FileText, 
  Shield, 
  Activity,
  Calendar,
  MessageSquare
} from 'lucide-react';
import { workflowAPI } from '../services/api';
import { toast } from 'react-toastify';

const WorkflowTimeline = ({ agentId, showActions = false }) => {
  const [workflowSummary, setWorkflowSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchWorkflowSummary();
  }, [agentId]);

  const fetchWorkflowSummary = async () => {
    try {
      setLoading(true);
      const summary = await workflowAPI.getWorkflowSummary(agentId);
      setWorkflowSummary(summary);
    } catch (error) {
      console.error('Error fetching workflow summary:', error);
      toast.error('Failed to load workflow information');
    } finally {
      setLoading(false);
    }
  };

  const handleAutoProgress = async () => {
    try {
      setProcessing(true);
      await workflowAPI.autoProgressWorkflow(agentId);
      toast.success('Workflow auto-progressed successfully');
      await fetchWorkflowSummary();
    } catch (error) {
      console.error('Error auto-progressing workflow:', error);
      toast.error('Failed to auto-progress workflow');
    } finally {
      setProcessing(false);
    }
  };

  const getStepIcon = (step) => {
    if (step.isCompleted) {
      return <CheckCircle className="w-6 h-6 text-green-500" />;
    } else if (step.isCurrent) {
      return <Clock className="w-6 h-6 text-blue-500" />;
    } else {
      return <div className="w-6 h-6 rounded-full border-2 border-gray-300 bg-white" />;
    }
  };

  const getStatusIcon = (status) => {
    const iconMap = {
      0: User,        // Pending
      1: FileText,    // UnderReview
      2: Shield,      // Approved
      4: Activity,    // Active
      3: AlertCircle, // Rejected
      5: Clock,       // Inactive
      6: AlertCircle  // Suspended
    };
    
    const IconComponent = iconMap[status] || User;
    return <IconComponent className="w-4 h-4" />;
  };

  const formatDuration = (timeSpan) => {
    if (!timeSpan) return 'N/A';
    
    const totalHours = Math.floor(timeSpan.totalHours || 0);
    const days = Math.floor(totalHours / 24);
    const hours = totalHours % 24;
    
    if (days > 0) {
      return `${days}d ${hours}h`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return '< 1h';
    }
  };

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex items-center space-x-4">
              <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
              <div className="flex-1 h-4 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!workflowSummary) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">Unable to load workflow information</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Onboarding Progress</h3>
        {showActions && (
          <button
            onClick={handleAutoProgress}
            disabled={processing}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            {processing ? 'Processing...' : 'Auto Progress'}
          </button>
        )}
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            Progress: {workflowSummary.completedSteps} of {workflowSummary.totalSteps} steps
          </span>
          <span className="text-sm text-gray-500">
            {Math.round(workflowSummary.progressPercentage)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${workflowSummary.progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Current Status */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-center space-x-3">
          {getStatusIcon(workflowSummary.currentStatus)}
          <div>
            <h4 className="font-medium text-blue-900">
              Current Status: {workflowSummary.currentStepName}
            </h4>
            <p className="text-sm text-blue-700">
              Time in current step: {formatDuration(workflowSummary.totalTimeSpent)}
            </p>
            {workflowSummary.estimatedRemainingTime && (
              <p className="text-sm text-blue-700">
                Estimated remaining: {formatDuration(workflowSummary.estimatedRemainingTime)}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Blocked Status */}
      {workflowSummary.isBlocked && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <div>
              <h4 className="font-medium text-red-900">Workflow Blocked</h4>
              <p className="text-sm text-red-700">{workflowSummary.blockedReason}</p>
            </div>
          </div>
        </div>
      )}

      {/* Timeline Steps */}
      <div className="space-y-6">
        {workflowSummary.steps.map((step, index) => (
          <div key={step.status} className="relative">
            {/* Connector Line */}
            {index < workflowSummary.steps.length - 1 && (
              <div className="absolute left-3 top-8 w-0.5 h-12 bg-gray-200"></div>
            )}
            
            <div className="flex items-start space-x-4">
              {/* Step Icon */}
              <div className="flex-shrink-0">
                {getStepIcon(step)}
              </div>
              
              {/* Step Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className={`font-medium ${
                    step.isCompleted ? 'text-green-700' : 
                    step.isCurrent ? 'text-blue-700' : 
                    'text-gray-500'
                  }`}>
                    {step.name}
                  </h4>
                  
                  {step.completedAt && (
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(step.completedAt).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
                
                {/* Step Requirements */}
                {step.requirements.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-600 mb-1">Requirements:</p>
                    <ul className="text-sm text-gray-500 space-y-1">
                      {step.requirements.map((req, reqIndex) => (
                        <li key={reqIndex} className="flex items-center space-x-2">
                          <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                          <span>{req}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {/* Completed By */}
                {step.completedBy && (
                  <div className="mt-2 flex items-center space-x-2 text-sm text-gray-500">
                    <User className="w-4 h-4" />
                    <span>Completed by: {step.completedBy}</span>
                  </div>
                )}
                
                {/* Manual Review Badge */}
                {step.requiresManualReview && (
                  <div className="mt-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      <MessageSquare className="w-3 h-3 mr-1" />
                      Manual Review Required
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Total Time:</span>
            <span className="ml-2 font-medium">{formatDuration(workflowSummary.totalTimeSpent)}</span>
          </div>
          <div>
            <span className="text-gray-500">Completion:</span>
            <span className="ml-2 font-medium">{Math.round(workflowSummary.progressPercentage)}%</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkflowTimeline;
