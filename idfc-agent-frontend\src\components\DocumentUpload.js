import React, { useState, useEffect } from 'react';
import { documentAPI } from '../services/api';
import { toast } from 'react-toastify';

const DocumentUpload = ({ agentId, onUploadComplete }) => {
  const [documents, setDocuments] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState({});

  const documentTypes = [
    { value: 0, label: 'Aadhar Card', required: true },
    { value: 1, label: 'PAN Card', required: true },
    { value: 2, label: 'Photo', required: true },
    { value: 3, label: 'Bank Passbook', required: true },
    { value: 4, label: 'Address Proof', required: false },
    { value: 5, label: 'Business Registration', required: false },
    { value: 6, label: 'GST Certificate', required: false },
    { value: 7, label: 'Other', required: false }
  ];

  const statusColors = {
    'Pending': 'bg-yellow-100 text-yellow-800',
    'UnderReview': 'bg-blue-100 text-blue-800',
    'Approved': 'bg-green-100 text-green-800',
    'Rejected': 'bg-red-100 text-red-800',
    'ResubmissionRequired': 'bg-orange-100 text-orange-800'
  };

  useEffect(() => {
    fetchDocuments();
  }, [agentId]);

  const fetchDocuments = async () => {
    try {
      const data = await documentAPI.getByAgent(agentId);
      setDocuments(data);
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast.error('Failed to fetch documents');
    }
  };

  const handleFileSelect = (documentType, file) => {
    setSelectedFiles(prev => ({
      ...prev,
      [documentType]: file
    }));
  };

  const handleUpload = async (documentType) => {
    const file = selectedFiles[documentType];
    if (!file) {
      toast.error('Please select a file to upload');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size cannot exceed 10MB');
      return;
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Only JPEG, PNG, and PDF files are allowed');
      return;
    }

    try {
      setUploading(true);
      await documentAPI.upload(agentId, file, documentType);
      toast.success('Document uploaded successfully');
      
      // Clear selected file
      setSelectedFiles(prev => ({
        ...prev,
        [documentType]: null
      }));
      
      // Refresh documents list
      await fetchDocuments();
      
      if (onUploadComplete) {
        onUploadComplete();
      }
    } catch (error) {
      console.error('Error uploading document:', error);
      toast.error('Failed to upload document');
    } finally {
      setUploading(false);
    }
  };

  const handleDownload = async (documentId, fileName) => {
    try {
      const blob = await documentAPI.download(documentId);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Document downloaded successfully');
    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error('Failed to download document');
    }
  };

  const getDocumentByType = (documentType) => {
    return documents.find(doc => doc.documentType === documentType);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-6">Document Upload</h3>
          
          <div className="space-y-6">
            {documentTypes.map((docType) => {
              const existingDoc = getDocumentByType(docType.value);
              const selectedFile = selectedFiles[docType.value];
              
              return (
                <div key={docType.value} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-sm font-medium text-gray-900">
                        {docType.label}
                      </h4>
                      {docType.required && (
                        <span className="text-red-500 text-sm">*</span>
                      )}
                    </div>
                    
                    {existingDoc && (
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColors[existingDoc.status] || 'bg-gray-100 text-gray-800'}`}>
                        {existingDoc.status}
                      </span>
                    )}
                  </div>
                  
                  {existingDoc ? (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {existingDoc.fileName}
                          </p>
                          <p className="text-xs text-gray-500">
                            {existingDoc.fileSize} • Uploaded {formatDate(existingDoc.uploadedAt)}
                          </p>
                          {existingDoc.reviewComments && (
                            <p className="text-xs text-red-600 mt-1">
                              <strong>Comments:</strong> {existingDoc.reviewComments}
                            </p>
                          )}
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleDownload(existingDoc.documentId, existingDoc.fileName)}
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            Download
                          </button>
                        </div>
                      </div>
                      
                      {(existingDoc.status === 'Rejected' || existingDoc.status === 'ResubmissionRequired') && (
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            Upload New Document
                          </label>
                          <div className="flex space-x-2">
                            <input
                              type="file"
                              accept=".jpg,.jpeg,.png,.pdf"
                              onChange={(e) => handleFileSelect(docType.value, e.target.files[0])}
                              className="flex-1 text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                            />
                            <button
                              onClick={() => handleUpload(docType.value)}
                              disabled={!selectedFile || uploading}
                              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {uploading ? 'Uploading...' : 'Upload'}
                            </button>
                          </div>
                          {selectedFile && (
                            <p className="text-xs text-gray-500">
                              Selected: {selectedFile.name} ({formatFileSize(selectedFile.size)})
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">
                        Select Document
                      </label>
                      <div className="flex space-x-2">
                        <input
                          type="file"
                          accept=".jpg,.jpeg,.png,.pdf"
                          onChange={(e) => handleFileSelect(docType.value, e.target.files[0])}
                          className="flex-1 text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        />
                        <button
                          onClick={() => handleUpload(docType.value)}
                          disabled={!selectedFile || uploading}
                          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {uploading ? 'Uploading...' : 'Upload'}
                        </button>
                      </div>
                      {selectedFile && (
                        <p className="text-xs text-gray-500">
                          Selected: {selectedFile.name} ({formatFileSize(selectedFile.size)})
                        </p>
                      )}
                    </div>
                  )}
                  
                  <div className="mt-2 text-xs text-gray-500">
                    Accepted formats: JPEG, PNG, PDF (Max size: 10MB)
                  </div>
                </div>
              );
            })}
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  Document Upload Guidelines
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <ul className="list-disc list-inside space-y-1">
                    <li>Documents marked with * are required for application approval</li>
                    <li>Ensure all documents are clear and readable</li>
                    <li>File size should not exceed 10MB</li>
                    <li>Accepted formats: JPEG, PNG, PDF</li>
                    <li>You can re-upload documents if they are rejected</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentUpload;
