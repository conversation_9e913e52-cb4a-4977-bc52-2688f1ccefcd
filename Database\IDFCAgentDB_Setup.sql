-- =============================================
-- IDFC Agent Onboarding Database Setup Script
-- =============================================
-- This script creates the complete database schema for the IDFC Agent Onboarding system
-- Compatible with SQL Server LocalDB and SQL Server Express

USE master;
GO

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'IDFCAgentDB')
BEGIN
    CREATE DATABASE IDFCAgentDB;
    PRINT 'Database IDFCAgentDB created successfully.';
END
ELSE
BEGIN
    PRINT 'Database IDFCAgentDB already exists.';
END
GO

USE IDFCAgentDB;
GO

-- =============================================
-- Drop existing tables if they exist (for clean setup)
-- =============================================
IF OBJECT_ID('dbo.AgentStatusHistories', 'U') IS NOT NULL DROP TABLE dbo.AgentStatusHistories;
IF OBJECT_ID('dbo.AgentDocuments', 'U') IS NOT NULL DROP TABLE dbo.AgentDocuments;
IF OBJECT_ID('dbo.Users', 'U') IS NOT NULL DROP TABLE dbo.Users;
IF OBJECT_ID('dbo.Agents', 'U') IS NOT NULL DROP TABLE dbo.Agents;
IF OBJECT_ID('dbo.WorkflowSteps', 'U') IS NOT NULL DROP TABLE dbo.WorkflowSteps;
IF OBJECT_ID('dbo.DocumentTypes', 'U') IS NOT NULL DROP TABLE dbo.DocumentTypes;
IF OBJECT_ID('dbo.AgentStatuses', 'U') IS NOT NULL DROP TABLE dbo.AgentStatuses;
IF OBJECT_ID('dbo.UserRoles', 'U') IS NOT NULL DROP TABLE dbo.UserRoles;
GO

-- =============================================
-- Create Reference Tables for Enums
-- =============================================

-- User Roles Reference Table
CREATE TABLE dbo.UserRoles (
    RoleId INT PRIMARY KEY,
    RoleName NVARCHAR(50) NOT NULL UNIQUE,
    Description NVARCHAR(255)
);

INSERT INTO dbo.UserRoles (RoleId, RoleName, Description) VALUES
(0, 'Agent', 'FASTag agent user'),
(1, 'Reviewer', 'Application reviewer'),
(2, 'Admin', 'System administrator'),
(3, 'SuperAdmin', 'Super administrator with full access');

-- Agent Status Reference Table
CREATE TABLE dbo.AgentStatuses (
    StatusId INT PRIMARY KEY,
    StatusName NVARCHAR(50) NOT NULL UNIQUE,
    Description NVARCHAR(255),
    IsActive BIT DEFAULT 1
);

INSERT INTO dbo.AgentStatuses (StatusId, StatusName, Description) VALUES
(0, 'Pending', 'Application submitted, awaiting initial review'),
(1, 'UnderReview', 'Application and documents under review'),
(2, 'Approved', 'Application approved, pending activation'),
(3, 'Rejected', 'Application rejected'),
(4, 'Active', 'Agent is active and operational'),
(5, 'Inactive', 'Agent temporarily inactive'),
(6, 'Suspended', 'Agent suspended');

-- Document Types Reference Table
CREATE TABLE dbo.DocumentTypes (
    DocumentTypeId INT PRIMARY KEY,
    TypeName NVARCHAR(50) NOT NULL UNIQUE,
    Description NVARCHAR(255),
    IsRequired BIT DEFAULT 0,
    MaxFileSizeMB INT DEFAULT 5
);

INSERT INTO dbo.DocumentTypes (DocumentTypeId, TypeName, Description, IsRequired, MaxFileSizeMB) VALUES
(0, 'AadharCard', 'Aadhar Card document', 1, 5),
(1, 'PanCard', 'PAN Card document', 1, 5),
(2, 'Photo', 'Passport size photograph', 1, 2),
(3, 'BankPassbook', 'Bank passbook or statement', 1, 10),
(4, 'AddressProof', 'Address proof document', 0, 5),
(5, 'BusinessRegistration', 'Business registration certificate', 0, 10),
(6, 'GSTCertificate', 'GST registration certificate', 0, 10),
(7, 'Other', 'Other supporting documents', 0, 10);

-- =============================================
-- Create Main Tables
-- =============================================

-- Agents Table
CREATE TABLE dbo.Agents (
    AgentId INT IDENTITY(1,1) PRIMARY KEY,
    FirstName NVARCHAR(100) NOT NULL,
    LastName NVARCHAR(100) NOT NULL,
    Email NVARCHAR(255) NOT NULL,
    PhoneNumber NVARCHAR(15) NOT NULL,
    AadharNumber NVARCHAR(12) NOT NULL,
    PanNumber NVARCHAR(10) NOT NULL,
    Address NVARCHAR(500),
    City NVARCHAR(100),
    State NVARCHAR(100),
    PinCode NVARCHAR(10),
    DateOfBirth DATETIME2 NOT NULL,
    Status INT NOT NULL DEFAULT 0,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy NVARCHAR(255),
    UpdatedBy NVARCHAR(255),
    
    -- Constraints
    CONSTRAINT FK_Agents_Status FOREIGN KEY (Status) REFERENCES dbo.AgentStatuses(StatusId),
    CONSTRAINT UQ_Agents_Email UNIQUE (Email),
    CONSTRAINT UQ_Agents_AadharNumber UNIQUE (AadharNumber),
    CONSTRAINT UQ_Agents_PanNumber UNIQUE (PanNumber),
    CONSTRAINT UQ_Agents_PhoneNumber UNIQUE (PhoneNumber),
    CONSTRAINT CK_Agents_AadharNumber CHECK (LEN(AadharNumber) = 12 AND AadharNumber NOT LIKE '%[^0-9]%'),
    CONSTRAINT CK_Agents_PanNumber CHECK (LEN(PanNumber) = 10 AND PanNumber LIKE '[A-Z][A-Z][A-Z][A-Z][A-Z][0-9][0-9][0-9][0-9][A-Z]'),
    CONSTRAINT CK_Agents_PinCode CHECK (LEN(PinCode) = 6 AND PinCode NOT LIKE '%[^0-9]%')
);

-- Users Table
CREATE TABLE dbo.Users (
    UserId INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(100) NOT NULL,
    Email NVARCHAR(255) NOT NULL,
    PasswordHash NVARCHAR(MAX) NOT NULL,
    Role INT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastLoginAt DATETIME2 NOT NULL DEFAULT '1900-01-01',
    AgentId INT NULL,
    
    -- Constraints
    CONSTRAINT FK_Users_Role FOREIGN KEY (Role) REFERENCES dbo.UserRoles(RoleId),
    CONSTRAINT FK_Users_Agent FOREIGN KEY (AgentId) REFERENCES dbo.Agents(AgentId) ON DELETE SET NULL,
    CONSTRAINT UQ_Users_Username UNIQUE (Username),
    CONSTRAINT UQ_Users_Email UNIQUE (Email),
    -- Note: Unique constraint on AgentId will be created as filtered index to allow NULLs
);

-- Agent Documents Table
CREATE TABLE dbo.AgentDocuments (
    DocumentId INT IDENTITY(1,1) PRIMARY KEY,
    AgentId INT NOT NULL,
    DocumentType INT NOT NULL,
    FileName NVARCHAR(255) NOT NULL,
    FilePath NVARCHAR(500) NOT NULL,
    FileSize NVARCHAR(100),
    MimeType NVARCHAR(50),
    Status INT NOT NULL DEFAULT 0, -- 0=Pending, 1=UnderReview, 2=Approved, 3=Rejected, 4=ResubmissionRequired
    ReviewComments NVARCHAR(500),
    UploadedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ReviewedAt DATETIME2 NULL,
    ReviewedBy NVARCHAR(255),

    -- Constraints
    CONSTRAINT FK_AgentDocuments_Agent FOREIGN KEY (AgentId) REFERENCES dbo.Agents(AgentId) ON DELETE CASCADE,
    CONSTRAINT FK_AgentDocuments_DocumentType FOREIGN KEY (DocumentType) REFERENCES dbo.DocumentTypes(DocumentTypeId),
    CONSTRAINT CK_AgentDocuments_Status CHECK (Status IN (0, 1, 2, 3, 4))
);

-- Agent Status History Table
CREATE TABLE dbo.AgentStatusHistories (
    HistoryId INT IDENTITY(1,1) PRIMARY KEY,
    AgentId INT NOT NULL,
    FromStatus INT NOT NULL,
    ToStatus INT NOT NULL,
    Comments NVARCHAR(500),
    ChangedBy NVARCHAR(255) NOT NULL,
    ChangedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),

    -- Constraints
    CONSTRAINT FK_AgentStatusHistories_Agent FOREIGN KEY (AgentId) REFERENCES dbo.Agents(AgentId) ON DELETE CASCADE,
    CONSTRAINT FK_AgentStatusHistories_FromStatus FOREIGN KEY (FromStatus) REFERENCES dbo.AgentStatuses(StatusId),
    CONSTRAINT FK_AgentStatusHistories_ToStatus FOREIGN KEY (ToStatus) REFERENCES dbo.AgentStatuses(StatusId)
);

-- Workflow Steps Configuration Table (for future extensibility)
CREATE TABLE dbo.WorkflowSteps (
    StepId INT IDENTITY(1,1) PRIMARY KEY,
    StepName NVARCHAR(100) NOT NULL,
    StepOrder INT NOT NULL,
    StatusId INT NOT NULL,
    Description NVARCHAR(500),
    RequiresManualReview BIT NOT NULL DEFAULT 0,
    EstimatedDurationHours INT DEFAULT 24,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),

    -- Constraints
    CONSTRAINT FK_WorkflowSteps_Status FOREIGN KEY (StatusId) REFERENCES dbo.AgentStatuses(StatusId),
    CONSTRAINT UQ_WorkflowSteps_Order UNIQUE (StepOrder),
    CONSTRAINT UQ_WorkflowSteps_Status UNIQUE (StatusId)
);

-- =============================================
-- Create Performance Indexes
-- =============================================

-- Agents table indexes
CREATE NONCLUSTERED INDEX IX_Agents_Status ON dbo.Agents(Status);
CREATE NONCLUSTERED INDEX IX_Agents_CreatedAt ON dbo.Agents(CreatedAt);
CREATE NONCLUSTERED INDEX IX_Agents_UpdatedAt ON dbo.Agents(UpdatedAt);
CREATE NONCLUSTERED INDEX IX_Agents_City_State ON dbo.Agents(City, State);

-- Users table indexes
CREATE NONCLUSTERED INDEX IX_Users_Role ON dbo.Users(Role);
CREATE NONCLUSTERED INDEX IX_Users_IsActive ON dbo.Users(IsActive);
CREATE NONCLUSTERED INDEX IX_Users_CreatedAt ON dbo.Users(CreatedAt);
CREATE NONCLUSTERED INDEX IX_Users_LastLoginAt ON dbo.Users(LastLoginAt);

-- AgentDocuments table indexes
CREATE NONCLUSTERED INDEX IX_AgentDocuments_AgentId ON dbo.AgentDocuments(AgentId);
CREATE NONCLUSTERED INDEX IX_AgentDocuments_DocumentType ON dbo.AgentDocuments(DocumentType);
CREATE NONCLUSTERED INDEX IX_AgentDocuments_Status ON dbo.AgentDocuments(Status);
CREATE NONCLUSTERED INDEX IX_AgentDocuments_UploadedAt ON dbo.AgentDocuments(UploadedAt);

-- AgentStatusHistories table indexes
CREATE NONCLUSTERED INDEX IX_AgentStatusHistories_AgentId ON dbo.AgentStatusHistories(AgentId);
CREATE NONCLUSTERED INDEX IX_AgentStatusHistories_ChangedAt ON dbo.AgentStatusHistories(ChangedAt);
CREATE NONCLUSTERED INDEX IX_AgentStatusHistories_ToStatus ON dbo.AgentStatusHistories(ToStatus);

-- Create filtered unique index for Users.AgentId to allow NULLs
SET QUOTED_IDENTIFIER ON;
CREATE UNIQUE NONCLUSTERED INDEX UQ_Users_AgentId ON dbo.Users(AgentId) WHERE AgentId IS NOT NULL;

-- =============================================
-- Insert Seed Data
-- =============================================

-- Insert Workflow Steps Configuration
INSERT INTO dbo.WorkflowSteps (StepName, StepOrder, StatusId, Description, RequiresManualReview, EstimatedDurationHours) VALUES
('Application Submitted', 1, 0, 'Agent has submitted their application', 0, 1),
('Under Review', 2, 1, 'Application and documents are being reviewed', 1, 48),
('Approved', 3, 2, 'Application has been approved', 0, 2),
('Active', 4, 4, 'Agent is active and operational', 0, 0),
('Rejected', 5, 3, 'Application has been rejected', 0, 0),
('Inactive', 6, 5, 'Agent is temporarily inactive', 1, 0),
('Suspended', 7, 6, 'Agent has been suspended', 1, 0);

-- Insert Default Admin User
-- Password: Admin@123 (hashed with BCrypt)
INSERT INTO dbo.Users (Username, Email, PasswordHash, Role, IsActive, CreatedAt) VALUES
('admin', '<EMAIL>', '$2a$11$WqYsGnxUS.IJPAv996aHdemj9jgZQpR6MBO1otpT0p6EnH1VkZFSS', 2, 1, '2024-01-01T00:00:00.0000000Z');

-- Insert Sample Reviewer User
-- Password: Reviewer@123 (hashed with BCrypt)
INSERT INTO dbo.Users (Username, Email, PasswordHash, Role, IsActive, CreatedAt) VALUES
('reviewer', '<EMAIL>', '$2a$11$2Ou5h14OUA/ArNQ6kzRCH.BqoyLELOh9MErYYs4.d/nqvUrEmVq/q', 1, 1, '2024-01-01T00:00:00.0000000Z');

-- =============================================
-- Create Stored Procedures and Functions
-- =============================================

-- Procedure to get agent dashboard statistics
GO
CREATE OR ALTER PROCEDURE sp_GetAgentDashboardStats
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        (SELECT COUNT(*) FROM dbo.Agents WHERE Status = 0) AS PendingApplications,
        (SELECT COUNT(*) FROM dbo.Agents WHERE Status = 1) AS UnderReviewApplications,
        (SELECT COUNT(*) FROM dbo.Agents WHERE Status = 2) AS ApprovedApplications,
        (SELECT COUNT(*) FROM dbo.Agents WHERE Status = 4) AS ActiveAgents,
        (SELECT COUNT(*) FROM dbo.Agents WHERE Status = 3) AS RejectedApplications,
        (SELECT COUNT(*) FROM dbo.Agents) AS TotalApplications,
        (SELECT COUNT(*) FROM dbo.AgentDocuments WHERE Status = 0) AS PendingDocuments,
        (SELECT COUNT(*) FROM dbo.Users WHERE IsActive = 1) AS ActiveUsers;
END;
GO

-- Function to calculate agent onboarding progress percentage
GO
CREATE OR ALTER FUNCTION fn_GetAgentProgress(@AgentId INT)
RETURNS DECIMAL(5,2)
AS
BEGIN
    DECLARE @CurrentStatus INT;
    DECLARE @ProgressPercentage DECIMAL(5,2);

    SELECT @CurrentStatus = Status FROM dbo.Agents WHERE AgentId = @AgentId;

    SET @ProgressPercentage = CASE
        WHEN @CurrentStatus = 0 THEN 25.0  -- Pending
        WHEN @CurrentStatus = 1 THEN 50.0  -- Under Review
        WHEN @CurrentStatus = 2 THEN 75.0  -- Approved
        WHEN @CurrentStatus = 4 THEN 100.0 -- Active
        WHEN @CurrentStatus = 3 THEN 0.0   -- Rejected
        WHEN @CurrentStatus = 5 THEN 100.0 -- Inactive (but completed onboarding)
        WHEN @CurrentStatus = 6 THEN 100.0 -- Suspended (but completed onboarding)
        ELSE 0.0
    END;

    RETURN @ProgressPercentage;
END;
GO

-- Procedure to get agents with pagination and filtering
GO
CREATE OR ALTER PROCEDURE sp_GetAgentsWithPagination
    @PageNumber INT = 1,
    @PageSize INT = 10,
    @StatusFilter INT = NULL,
    @SearchTerm NVARCHAR(255) = NULL,
    @SortBy NVARCHAR(50) = 'CreatedAt',
    @SortOrder NVARCHAR(4) = 'DESC'
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;

    WITH FilteredAgents AS (
        SELECT
            a.AgentId,
            a.FirstName,
            a.LastName,
            a.Email,
            a.PhoneNumber,
            a.Status,
            s.StatusName,
            a.CreatedAt,
            a.UpdatedAt,
            dbo.fn_GetAgentProgress(a.AgentId) AS ProgressPercentage,
            COUNT(*) OVER() AS TotalRecords
        FROM dbo.Agents a
        INNER JOIN dbo.AgentStatuses s ON a.Status = s.StatusId
        WHERE
            (@StatusFilter IS NULL OR a.Status = @StatusFilter)
            AND (@SearchTerm IS NULL OR
                 a.FirstName LIKE '%' + @SearchTerm + '%' OR
                 a.LastName LIKE '%' + @SearchTerm + '%' OR
                 a.Email LIKE '%' + @SearchTerm + '%' OR
                 a.PhoneNumber LIKE '%' + @SearchTerm + '%')
    )
    SELECT *
    FROM FilteredAgents
    ORDER BY
        CASE WHEN @SortBy = 'FirstName' AND @SortOrder = 'ASC' THEN FirstName END ASC,
        CASE WHEN @SortBy = 'FirstName' AND @SortOrder = 'DESC' THEN FirstName END DESC,
        CASE WHEN @SortBy = 'Email' AND @SortOrder = 'ASC' THEN Email END ASC,
        CASE WHEN @SortBy = 'Email' AND @SortOrder = 'DESC' THEN Email END DESC,
        CASE WHEN @SortBy = 'CreatedAt' AND @SortOrder = 'ASC' THEN CreatedAt END ASC,
        CASE WHEN @SortBy = 'CreatedAt' AND @SortOrder = 'DESC' THEN CreatedAt END DESC,
        CASE WHEN @SortBy = 'Status' AND @SortOrder = 'ASC' THEN Status END ASC,
        CASE WHEN @SortBy = 'Status' AND @SortOrder = 'DESC' THEN Status END DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END;
GO

-- Procedure to update agent status with history tracking
GO
CREATE OR ALTER PROCEDURE sp_UpdateAgentStatus
    @AgentId INT,
    @NewStatus INT,
    @ChangedBy NVARCHAR(255),
    @Comments NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRANSACTION;

    BEGIN TRY
        DECLARE @CurrentStatus INT;
        SELECT @CurrentStatus = Status FROM dbo.Agents WHERE AgentId = @AgentId;

        IF @CurrentStatus IS NULL
        BEGIN
            RAISERROR('Agent not found', 16, 1);
            RETURN;
        END

        -- Update agent status
        UPDATE dbo.Agents
        SET Status = @NewStatus,
            UpdatedAt = GETUTCDATE(),
            UpdatedBy = @ChangedBy
        WHERE AgentId = @AgentId;

        -- Insert status history record
        INSERT INTO dbo.AgentStatusHistories (AgentId, FromStatus, ToStatus, Comments, ChangedBy, ChangedAt)
        VALUES (@AgentId, @CurrentStatus, @NewStatus, @Comments, @ChangedBy, GETUTCDATE());

        COMMIT TRANSACTION;

        SELECT 'Success' AS Result, 'Agent status updated successfully' AS Message;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;

        SELECT 'Error' AS Result, ERROR_MESSAGE() AS Message;
    END CATCH
END;
GO

-- Procedure to get agent workflow summary
GO
CREATE OR ALTER PROCEDURE sp_GetAgentWorkflowSummary
    @AgentId INT
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        a.AgentId,
        a.FirstName + ' ' + a.LastName AS AgentName,
        a.Email,
        a.Status AS CurrentStatusId,
        s.StatusName AS CurrentStatus,
        ws.StepName AS CurrentStepName,
        ws.Description AS CurrentStepDescription,
        a.CreatedAt,
        a.UpdatedAt,
        dbo.fn_GetAgentProgress(a.AgentId) AS ProgressPercentage,
        (SELECT COUNT(*) FROM dbo.AgentStatusHistories WHERE AgentId = @AgentId) AS StatusChanges,
        (SELECT COUNT(*) FROM dbo.AgentDocuments WHERE AgentId = @AgentId) AS TotalDocuments,
        (SELECT COUNT(*) FROM dbo.AgentDocuments WHERE AgentId = @AgentId AND Status = 2) AS ApprovedDocuments,
        (SELECT COUNT(*) FROM dbo.AgentDocuments WHERE AgentId = @AgentId AND Status = 0) AS PendingDocuments
    FROM dbo.Agents a
    INNER JOIN dbo.AgentStatuses s ON a.Status = s.StatusId
    LEFT JOIN dbo.WorkflowSteps ws ON a.Status = ws.StatusId
    WHERE a.AgentId = @AgentId;

    -- Get status history
    SELECT
        h.HistoryId,
        h.FromStatus,
        fs.StatusName AS FromStatusName,
        h.ToStatus,
        ts.StatusName AS ToStatusName,
        h.Comments,
        h.ChangedBy,
        h.ChangedAt
    FROM dbo.AgentStatusHistories h
    INNER JOIN dbo.AgentStatuses fs ON h.FromStatus = fs.StatusId
    INNER JOIN dbo.AgentStatuses ts ON h.ToStatus = ts.StatusId
    WHERE h.AgentId = @AgentId
    ORDER BY h.ChangedAt DESC;
END;
GO

-- =============================================
-- Create Views for Common Queries
-- =============================================

-- View for agent summary with status information
GO
CREATE OR ALTER VIEW vw_AgentSummary
AS
SELECT
    a.AgentId,
    a.FirstName,
    a.LastName,
    a.FirstName + ' ' + a.LastName AS FullName,
    a.Email,
    a.PhoneNumber,
    a.Status AS StatusId,
    s.StatusName,
    a.CreatedAt,
    a.UpdatedAt,
    dbo.fn_GetAgentProgress(a.AgentId) AS ProgressPercentage,
    (SELECT COUNT(*) FROM dbo.AgentDocuments WHERE AgentId = a.AgentId) AS DocumentCount,
    (SELECT COUNT(*) FROM dbo.AgentDocuments WHERE AgentId = a.AgentId AND Status = 2) AS ApprovedDocumentCount
FROM dbo.Agents a
INNER JOIN dbo.AgentStatuses s ON a.Status = s.StatusId;
GO

-- View for document summary
GO
CREATE OR ALTER VIEW vw_DocumentSummary
AS
SELECT
    d.DocumentId,
    d.AgentId,
    a.FirstName + ' ' + a.LastName AS AgentName,
    a.Email AS AgentEmail,
    d.DocumentType AS DocumentTypeId,
    dt.TypeName AS DocumentTypeName,
    d.FileName,
    d.Status AS StatusId,
    CASE d.Status
        WHEN 0 THEN 'Pending'
        WHEN 1 THEN 'Under Review'
        WHEN 2 THEN 'Approved'
        WHEN 3 THEN 'Rejected'
        WHEN 4 THEN 'Resubmission Required'
    END AS StatusName,
    d.UploadedAt,
    d.ReviewedAt,
    d.ReviewedBy,
    d.ReviewComments
FROM dbo.AgentDocuments d
INNER JOIN dbo.Agents a ON d.AgentId = a.AgentId
INNER JOIN dbo.DocumentTypes dt ON d.DocumentType = dt.DocumentTypeId;
GO

-- =============================================
-- Database Setup Complete
-- =============================================

PRINT '==============================================';
PRINT 'IDFC Agent Onboarding Database Setup Complete';
PRINT '==============================================';
PRINT 'Database: IDFCAgentDB';
PRINT 'Tables Created: 8 main tables + 4 reference tables';
PRINT 'Indexes Created: 15 performance indexes';
PRINT 'Stored Procedures: 5 procedures';
PRINT 'Functions: 1 function';
PRINT 'Views: 2 views';
PRINT '';
PRINT 'Default Users Created:';
PRINT '- Admin: username=admin, password=Admin@123';
PRINT '- Reviewer: username=reviewer, password=Reviewer@123';
PRINT '';
PRINT 'Connection String for appsettings.json:';
PRINT 'Server=(localdb)\\mssqllocaldb;Database=IDFCAgentDB;Trusted_Connection=true;MultipleActiveResultSets=true';
PRINT '';
PRINT 'Setup completed successfully!';
GO
