using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IDFCAgentAPI.Models
{
    public class Agent
    {
        [Key]
        public int AgentId { get; set; }

        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [Phone]
        [StringLength(15)]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(12)]
        public string AadharNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        public string PanNumber { get; set; } = string.Empty;

        [StringLength(500)]
        public string Address { get; set; } = string.Empty;

        [StringLength(100)]
        public string City { get; set; } = string.Empty;

        [StringLength(100)]
        public string State { get; set; } = string.Empty;

        [StringLength(10)]
        public string PinCode { get; set; } = string.Empty;

        [Required]
        public DateTime DateOfBirth { get; set; }

        [Required]
        public AgentStatus Status { get; set; } = AgentStatus.Pending;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(255)]
        public string? CreatedBy { get; set; }

        [StringLength(255)]
        public string? UpdatedBy { get; set; }

        // Navigation properties
        public virtual ICollection<AgentDocument> Documents { get; set; } = new List<AgentDocument>();
        public virtual ICollection<AgentStatusHistory> StatusHistory { get; set; } = new List<AgentStatusHistory>();
        public virtual User? User { get; set; }
    }

    public enum AgentStatus
    {
        Pending = 0,
        UnderReview = 1,
        Approved = 2,
        Rejected = 3,
        Active = 4,
        Inactive = 5,
        Suspended = 6
    }
}
