using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IDFCAgentAPI.Models
{
    public class User
    {
        [Key]
        public int UserId { get; set; }

        [Required]
        [StringLength(100)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; } = string.Empty;

        [Required]
        public string PasswordHash { get; set; } = string.Empty;

        [Required]
        public UserRole Role { get; set; } = UserRole.Agent;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastLoginAt { get; set; }

        // Foreign key to Agent (if user is an agent)
        public int? AgentId { get; set; }

        // Navigation properties
        [ForeignKey("AgentId")]
        public virtual Agent? Agent { get; set; }
    }

    public enum UserRole
    {
        Agent = 0,
        Reviewer = 1,
        Admin = 2,
        SuperAdmin = 3
    }
}
