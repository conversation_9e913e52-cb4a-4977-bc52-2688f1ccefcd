{"Version": 1, "WorkspaceRootPath": "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{8BDECBFE-7D35-EF4B-DD91-E32DB32BBCEA}|IDFCAgentAPI\\IDFCAgentAPI.csproj|d:\\augment-projects\\idfcagentonboardingandmanagementsolution\\idfcagentapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8BDECBFE-7D35-EF4B-DD91-E32DB32BBCEA}|IDFCAgentAPI\\IDFCAgentAPI.csproj|solutionrelative:idfcagentapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8BDECBFE-7D35-EF4B-DD91-E32DB32BBCEA}|IDFCAgentAPI\\IDFCAgentAPI.csproj|d:\\augment-projects\\idfcagentonboardingandmanagementsolution\\idfcagentapi\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8BDECBFE-7D35-EF4B-DD91-E32DB32BBCEA}|IDFCAgentAPI\\IDFCAgentAPI.csproj|solutionrelative:idfcagentapi\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8BDECBFE-7D35-EF4B-DD91-E32DB32BBCEA}|IDFCAgentAPI\\IDFCAgentAPI.csproj|d:\\augment-projects\\idfcagentonboardingandmanagementsolution\\idfcagentapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{8BDECBFE-7D35-EF4B-DD91-E32DB32BBCEA}|IDFCAgentAPI\\IDFCAgentAPI.csproj|solutionrelative:idfcagentapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{8BDECBFE-7D35-EF4B-DD91-E32DB32BBCEA}|IDFCAgentAPI\\IDFCAgentAPI.csproj|d:\\augment-projects\\idfcagentonboardingandmanagementsolution\\idfcagentapi\\services\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8BDECBFE-7D35-EF4B-DD91-E32DB32BBCEA}|IDFCAgentAPI\\IDFCAgentAPI.csproj|solutionrelative:idfcagentapi\\services\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8BDECBFE-7D35-EF4B-DD91-E32DB32BBCEA}|IDFCAgentAPI\\IDFCAgentAPI.csproj|d:\\augment-projects\\idfcagentonboardingandmanagementsolution\\idfcagentapi\\controllers\\agentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8BDECBFE-7D35-EF4B-DD91-E32DB32BBCEA}|IDFCAgentAPI\\IDFCAgentAPI.csproj|solutionrelative:idfcagentapi\\controllers\\agentscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:11:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:12:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:14:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:15:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\IDFCAgentAPI\\Program.cs", "RelativeDocumentMoniker": "IDFCAgentAPI\\Program.cs", "ToolTip": "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\IDFCAgentAPI\\Program.cs", "RelativeToolTip": "IDFCAgentAPI\\Program.cs", "ViewState": "AgIAACoAAAAAAAAAAAAAADcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T08:05:53.766Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AuthService.cs", "DocumentMoniker": "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\IDFCAgentAPI\\Services\\AuthService.cs", "RelativeDocumentMoniker": "IDFCAgentAPI\\Services\\AuthService.cs", "ToolTip": "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\IDFCAgentAPI\\Services\\AuthService.cs", "RelativeToolTip": "IDFCAgentAPI\\Services\\AuthService.cs", "ViewState": "AgIAAA4AAAAAAAAAAIAwwBYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T13:24:44.451Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.json", "DocumentMoniker": "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\IDFCAgentAPI\\appsettings.json", "RelativeDocumentMoniker": "IDFCAgentAPI\\appsettings.json", "ToolTip": "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\IDFCAgentAPI\\appsettings.json", "RelativeToolTip": "IDFCAgentAPI\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-02T13:23:38.425Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "AuthController.cs", "DocumentMoniker": "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\IDFCAgentAPI\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "IDFCAgentAPI\\Controllers\\AuthController.cs", "ToolTip": "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\IDFCAgentAPI\\Controllers\\AuthController.cs", "RelativeToolTip": "IDFCAgentAPI\\Controllers\\AuthController.cs", "ViewState": "AgIAABMAAAAAAAAAAAAWwCIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T13:06:52.121Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "AgentsController.cs", "DocumentMoniker": "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\IDFCAgentAPI\\Controllers\\AgentsController.cs", "RelativeDocumentMoniker": "IDFCAgentAPI\\Controllers\\AgentsController.cs", "ToolTip": "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\IDFCAgentAPI\\Controllers\\AgentsController.cs", "RelativeToolTip": "IDFCAgentAPI\\Controllers\\AgentsController.cs", "ViewState": "AgIAACQAAAAAAAAAAAAYwCQAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T13:06:48.606Z"}]}]}]}