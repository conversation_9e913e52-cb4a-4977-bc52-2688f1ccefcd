using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using IDFCAgentAPI.Data;
using IDFCAgentAPI.Services;
using IDFCAgentAPI.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TestAuthService
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Testing IDFC Agent API Authentication Service");
            Console.WriteLine("============================================");

            // Setup configuration
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string>
                {
                    {"ConnectionStrings:DefaultConnection", "Server=(localdb)\\mssqllocaldb;Database=IDFCAgentDB;Trusted_Connection=true;MultipleActiveResultSets=true"},
                    {"JwtSettings:SecretKey", "YourSuperSecretKeyThatIsAtLeast32CharactersLong!"},
                    {"JwtSettings:Issuer", "IDFCAgentAPI"},
                    {"JwtSettings:Audience", "IDFCAgentAPI"}
                })
                .Build();

            // Setup services
            var services = new ServiceCollection();
            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));
            services.AddScoped<IAuthService, AuthService>();
            services.AddSingleton<IConfiguration>(configuration);

            var serviceProvider = services.BuildServiceProvider();

            using var scope = serviceProvider.CreateScope();
            var authService = scope.ServiceProvider.GetRequiredService<IAuthService>();

            // Test admin login
            Console.WriteLine("\nTesting admin login...");
            var adminLogin = new LoginDto
            {
                Username = "admin",
                Password = "Admin@123"
            };

            try
            {
                var adminResult = await authService.LoginAsync(adminLogin);
                if (adminResult != null)
                {
                    Console.WriteLine("✓ Admin login successful!");
                    Console.WriteLine($"  Token: {adminResult.Token.Substring(0, 50)}...");
                    Console.WriteLine($"  User: {adminResult.User.Username}");
                    Console.WriteLine($"  Role: {adminResult.User.Role}");
                    Console.WriteLine($"  Email: {adminResult.User.Email}");
                }
                else
                {
                    Console.WriteLine("✗ Admin login failed!");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Admin login error: {ex.Message}");
            }

            // Test reviewer login
            Console.WriteLine("\nTesting reviewer login...");
            var reviewerLogin = new LoginDto
            {
                Username = "reviewer",
                Password = "Reviewer@123"
            };

            try
            {
                var reviewerResult = await authService.LoginAsync(reviewerLogin);
                if (reviewerResult != null)
                {
                    Console.WriteLine("✓ Reviewer login successful!");
                    Console.WriteLine($"  Token: {reviewerResult.Token.Substring(0, 50)}...");
                    Console.WriteLine($"  User: {reviewerResult.User.Username}");
                    Console.WriteLine($"  Role: {reviewerResult.User.Role}");
                    Console.WriteLine($"  Email: {reviewerResult.User.Email}");
                }
                else
                {
                    Console.WriteLine("✗ Reviewer login failed!");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Reviewer login error: {ex.Message}");
            }

            // Test wrong password
            Console.WriteLine("\nTesting wrong password...");
            var wrongLogin = new LoginDto
            {
                Username = "admin",
                Password = "WrongPassword"
            };

            try
            {
                var wrongResult = await authService.LoginAsync(wrongLogin);
                if (wrongResult == null)
                {
                    Console.WriteLine("✓ Wrong password correctly rejected!");
                }
                else
                {
                    Console.WriteLine("✗ Wrong password test failed - should have been rejected!");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Wrong password test error: {ex.Message}");
            }

            // Test direct password verification
            Console.WriteLine("\nTesting direct password verification...");
            var user = await authService.GetUserByUsernameAsync("admin");
            if (user != null)
            {
                bool directVerify = authService.VerifyPassword("Admin@123", user.PasswordHash);
                Console.WriteLine($"Direct password verification: {directVerify}");
                Console.WriteLine($"Stored hash: {user.PasswordHash}");
                Console.WriteLine($"Hash length: {user.PasswordHash.Length}");
            }

            Console.WriteLine("\n============================================");
            Console.WriteLine("Authentication service testing completed!");
        }
    }
}
