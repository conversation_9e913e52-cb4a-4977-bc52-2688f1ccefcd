/*! For license information please see main.b6945cb7.js.LICENSE.txt */
(()=>{var e={4:(e,t,n)=>{"use strict";var r=n(853),a=n(43),s=n(950);function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function c(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function u(e){if(l(e)!==e)throw Error(o(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var f=Object.assign,m=Symbol.for("react.element"),p=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),y=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),x=Symbol.for("react.consumer"),w=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),N=Symbol.for("react.suspense"),j=Symbol.for("react.suspense_list"),S=Symbol.for("react.memo"),_=Symbol.for("react.lazy");Symbol.for("react.scope");var E=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var T=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var C=Symbol.iterator;function A(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=C&&e[C]||e["@@iterator"])?e:null}var P=Symbol.for("react.client.reference");function O(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===P?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case v:return"Profiler";case y:return"StrictMode";case N:return"Suspense";case j:return"SuspenseList";case E:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case h:return"Portal";case w:return(e.displayName||"Context")+".Provider";case x:return(e._context.displayName||"Context")+".Consumer";case k:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case S:return null!==(t=e.displayName||null)?t:O(e.type)||"Memo";case _:t=e._payload,e=e._init;try{return O(e(t))}catch(n){}}return null}var R=Array.isArray,F=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,L=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D={pending:!1,data:null,method:null,action:null},z=[],M=-1;function I(e){return{current:e}}function U(e){0>M||(e.current=z[M],z[M]=null,M--)}function V(e,t){M++,z[M]=e.current,e.current=t}var B=I(null),q=I(null),$=I(null),H=I(null);function W(e,t){switch(V($,t),V(q,e),V(B,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=sd(t=ad(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}U(B),V(B,e)}function Q(){U(B),U(q),U($)}function K(e){null!==e.memoizedState&&V(H,e);var t=B.current,n=sd(t,e.type);t!==n&&(V(q,e),V(B,n))}function Y(e){q.current===e&&(U(B),U(q)),H.current===e&&(U(H),Kd._currentValue=D)}var X=Object.prototype.hasOwnProperty,J=r.unstable_scheduleCallback,G=r.unstable_cancelCallback,Z=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,se=r.unstable_NormalPriority,oe=r.unstable_LowPriority,ie=r.unstable_IdlePriority,le=r.log,ce=r.unstable_setDisableYieldValue,ue=null,de=null;function fe(e){if("function"===typeof le&&ce(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ue,e)}catch(t){}}var me=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(pe(e)/he|0)|0},pe=Math.log,he=Math.LN2;var ge=256,ye=4194304;function ve(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,s=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var i=134217727&r;return 0!==i?0!==(r=i&~s)?a=ve(r):0!==(o&=i)?a=ve(o):n||0!==(n=i&~e)&&(a=ve(n)):0!==(i=r&~s)?a=ve(i):0!==o?a=ve(o):n||0!==(n=r&~e)&&(a=ve(n)),0===a?0:0!==t&&t!==a&&0===(t&s)&&((s=a&-a)>=(n=t&-t)||32===s&&0!==(4194048&n))?t:a}function xe(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function we(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ke(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function Ne(){var e=ye;return 0===(62914560&(ye<<=1))&&(ye=4194304),e}function je(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Se(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function _e(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-me(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Ee(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-me(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Te(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ce(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Ae(){var e=L.p;return 0!==e?e:void 0===(e=window.event)?32:uf(e.type)}var Pe=Math.random().toString(36).slice(2),Oe="__reactFiber$"+Pe,Re="__reactProps$"+Pe,Fe="__reactContainer$"+Pe,Le="__reactEvents$"+Pe,De="__reactListeners$"+Pe,ze="__reactHandles$"+Pe,Me="__reactResources$"+Pe,Ie="__reactMarker$"+Pe;function Ue(e){delete e[Oe],delete e[Re],delete e[Le],delete e[De],delete e[ze]}function Ve(e){var t=e[Oe];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Fe]||n[Oe]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=bd(e);null!==e;){if(n=e[Oe])return n;e=bd(e)}return t}n=(e=n).parentNode}return null}function Be(e){if(e=e[Oe]||e[Fe]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function qe(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(o(33))}function $e(e){var t=e[Me];return t||(t=e[Me]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function He(e){e[Ie]=!0}var We=new Set,Qe={};function Ke(e,t){Ye(e,t),Ye(e+"Capture",t)}function Ye(e,t){for(Qe[e]=t,e=0;e<t.length;e++)We.add(t[e])}var Xe,Je,Ge=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},et={};function tt(e,t,n){if(a=t,X.call(et,a)||!X.call(Ze,a)&&(Ge.test(a)?et[a]=!0:(Ze[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function at(e){if(void 0===Xe)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Xe=t&&t[1]||"",Je=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Xe+e+Je}var st=!1;function ot(e,t){if(!e||st)return"";st=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(a){var r=a}Reflect.construct(e,[],n)}else{try{n.call()}catch(s){r=s}e.call(n.prototype)}}else{try{throw Error()}catch(o){r=o}(n=e())&&"function"===typeof n.catch&&n.catch(function(){})}}catch(i){if(i&&r&&"string"===typeof i.stack)return[i.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var s=r.DetermineComponentFrameRoot(),o=s[0],i=s[1];if(o&&i){var l=o.split("\n"),c=i.split("\n");for(a=r=0;r<l.length&&!l[r].includes("DetermineComponentFrameRoot");)r++;for(;a<c.length&&!c[a].includes("DetermineComponentFrameRoot");)a++;if(r===l.length||a===c.length)for(r=l.length-1,a=c.length-1;1<=r&&0<=a&&l[r]!==c[a];)a--;for(;1<=r&&0<=a;r--,a--)if(l[r]!==c[a]){if(1!==r||1!==a)do{if(r--,0>--a||l[r]!==c[a]){var u="\n"+l[r].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=r&&0<=a);break}}}finally{st=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?at(n):""}function it(e){switch(e.tag){case 26:case 27:case 5:return at(e.type);case 16:return at("Lazy");case 13:return at("Suspense");case 19:return at("SuspenseList");case 0:case 15:return ot(e.type,!1);case 11:return ot(e.type.render,!1);case 1:return ot(e.type,!0);case 31:return at("Activity");default:return""}}function lt(e){try{var t="";do{t+=it(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ct(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ut(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ut(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,s.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ut(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function mt(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var pt=/[\n"\\]/g;function ht(e){return e.replace(pt,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function gt(e,t,n,r,a,s,o,i){e.name="",null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o?e.type=o:e.removeAttribute("type"),null!=t?"number"===o?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ct(t)):e.value!==""+ct(t)&&(e.value=""+ct(t)):"submit"!==o&&"reset"!==o||e.removeAttribute("value"),null!=t?vt(e,o,ct(t)):null!=n?vt(e,o,ct(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=s&&(e.defaultChecked=!!s),null!=a&&(e.checked=a&&"function"!==typeof a&&"symbol"!==typeof a),null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.name=""+ct(i):e.removeAttribute("name")}function yt(e,t,n,r,a,s,o,i){if(null!=s&&"function"!==typeof s&&"symbol"!==typeof s&&"boolean"!==typeof s&&(e.type=s),null!=t||null!=n){if(!("submit"!==s&&"reset"!==s||void 0!==t&&null!==t))return;n=null!=n?""+ct(n):"",t=null!=t?""+ct(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:a)&&"symbol"!==typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o&&(e.name=o)}function vt(e,t,n){"number"===t&&mt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ct(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function xt(e,t,n){null==t||((t=""+ct(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ct(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function wt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(o(92));if(R(r)){if(1<r.length)throw Error(o(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ct(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function kt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var Nt=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function jt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||Nt.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function St(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(o(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&jt(e,a,r)}else for(var s in t)t.hasOwnProperty(s)&&jt(e,s,t[s])}function _t(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Et=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Tt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ct(e){return Tt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var At=null;function Pt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Ot=null,Rt=null;function Ft(e){var t=Be(e);if(t&&(e=t.stateNode)){var n=e[Re]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ht(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Re]||null;if(!a)throw Error(o(90));gt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":xt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var Lt=!1;function Dt(e,t,n){if(Lt)return e(t,n);Lt=!0;try{return e(t)}finally{if(Lt=!1,(null!==Ot||null!==Rt)&&(Vc(),Ot&&(t=Ot,e=Rt,Rt=Ot=null,Ft(t),e)))for(t=0;t<e.length;t++)Ft(e[t])}}function zt(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Re]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var Mt=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),It=!1;if(Mt)try{var Ut={};Object.defineProperty(Ut,"passive",{get:function(){It=!0}}),window.addEventListener("test",Ut,Ut),window.removeEventListener("test",Ut,Ut)}catch(Ff){It=!1}var Vt=null,Bt=null,qt=null;function $t(){if(qt)return qt;var e,t,n=Bt,r=n.length,a="value"in Vt?Vt.value:Vt.textContent,s=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[s-t];t++);return qt=a.slice(e,1<t?1-t:void 0)}function Ht(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Wt(){return!0}function Qt(){return!1}function Kt(e){function t(t,n,r,a,s){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=s,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Wt:Qt,this.isPropagationStopped=Qt,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Wt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Wt)},persist:function(){},isPersistent:Wt}),t}var Yt,Xt,Jt,Gt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Kt(Gt),en=f({},Gt,{view:0,detail:0}),tn=Kt(en),nn=f({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Jt&&(Jt&&"mousemove"===e.type?(Yt=e.screenX-Jt.screenX,Xt=e.screenY-Jt.screenY):Xt=Yt=0,Jt=e),Yt)},movementY:function(e){return"movementY"in e?e.movementY:Xt}}),rn=Kt(nn),an=Kt(f({},nn,{dataTransfer:0})),sn=Kt(f({},en,{relatedTarget:0})),on=Kt(f({},Gt,{animationName:0,elapsedTime:0,pseudoElement:0})),ln=Kt(f({},Gt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),cn=Kt(f({},Gt,{data:0})),un={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function mn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function pn(){return mn}var hn=Kt(f({},en,{key:function(e){if(e.key){var t=un[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Ht(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pn,charCode:function(e){return"keypress"===e.type?Ht(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Ht(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gn=Kt(f({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),yn=Kt(f({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pn})),vn=Kt(f({},Gt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Kt(f({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),xn=Kt(f({},Gt,{newState:0,oldState:0})),wn=[9,13,27,32],kn=Mt&&"CompositionEvent"in window,Nn=null;Mt&&"documentMode"in document&&(Nn=document.documentMode);var jn=Mt&&"TextEvent"in window&&!Nn,Sn=Mt&&(!kn||Nn&&8<Nn&&11>=Nn),_n=String.fromCharCode(32),En=!1;function Tn(e,t){switch(e){case"keyup":return-1!==wn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Cn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var An=!1;var Pn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function On(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Pn[e.type]:"textarea"===t}function Rn(e,t,n,r){Ot?Rt?Rt.push(r):Rt=[r]:Ot=r,0<(t=$u(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Fn=null,Ln=null;function Dn(e){Du(e,0)}function zn(e){if(ft(qe(e)))return e}function Mn(e,t){if("change"===e)return t}var In=!1;if(Mt){var Un;if(Mt){var Vn="oninput"in document;if(!Vn){var Bn=document.createElement("div");Bn.setAttribute("oninput","return;"),Vn="function"===typeof Bn.oninput}Un=Vn}else Un=!1;In=Un&&(!document.documentMode||9<document.documentMode)}function qn(){Fn&&(Fn.detachEvent("onpropertychange",$n),Ln=Fn=null)}function $n(e){if("value"===e.propertyName&&zn(Ln)){var t=[];Rn(t,Ln,e,Pt(e)),Dt(Dn,t)}}function Hn(e,t,n){"focusin"===e?(qn(),Ln=n,(Fn=t).attachEvent("onpropertychange",$n)):"focusout"===e&&qn()}function Wn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return zn(Ln)}function Qn(e,t){if("click"===e)return zn(t)}function Kn(e,t){if("input"===e||"change"===e)return zn(t)}var Yn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Xn(e,t){if(Yn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!X.call(t,a)||!Yn(e[a],t[a]))return!1}return!0}function Jn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Gn(e,t){var n,r=Jn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Jn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=mt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=mt((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=Mt&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,sr=null,or=!1;function ir(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;or||null==rr||rr!==mt(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},sr&&Xn(sr,r)||(sr=r,0<(r=$u(ar,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function lr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var cr={animationend:lr("Animation","AnimationEnd"),animationiteration:lr("Animation","AnimationIteration"),animationstart:lr("Animation","AnimationStart"),transitionrun:lr("Transition","TransitionRun"),transitionstart:lr("Transition","TransitionStart"),transitioncancel:lr("Transition","TransitionCancel"),transitionend:lr("Transition","TransitionEnd")},ur={},dr={};function fr(e){if(ur[e])return ur[e];if(!cr[e])return e;var t,n=cr[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return ur[e]=n[t];return e}Mt&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);var mr=fr("animationend"),pr=fr("animationiteration"),hr=fr("animationstart"),gr=fr("transitionrun"),yr=fr("transitionstart"),vr=fr("transitioncancel"),br=fr("transitionend"),xr=new Map,wr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function kr(e,t){xr.set(e,t),Ke(t,[e])}wr.push("scrollEnd");var Nr=new WeakMap;function jr(e,t){if("object"===typeof e&&null!==e){var n=Nr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:lt(t)},Nr.set(e,t),t)}return{value:e,source:t,stack:lt(t)}}var Sr=[],_r=0,Er=0;function Tr(){for(var e=_r,t=Er=_r=0;t<e;){var n=Sr[t];Sr[t++]=null;var r=Sr[t];Sr[t++]=null;var a=Sr[t];Sr[t++]=null;var s=Sr[t];if(Sr[t++]=null,null!==r&&null!==a){var o=r.pending;null===o?a.next=a:(a.next=o.next,o.next=a),r.pending=a}0!==s&&Or(n,a,s)}}function Cr(e,t,n,r){Sr[_r++]=e,Sr[_r++]=t,Sr[_r++]=n,Sr[_r++]=r,Er|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Ar(e,t,n,r){return Cr(e,t,n,r),Rr(e)}function Pr(e,t){return Cr(e,null,null,t),Rr(e)}function Or(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,s=e.return;null!==s;)s.childLanes|=n,null!==(r=s.alternate)&&(r.childLanes|=n),22===s.tag&&(null===(e=s.stateNode)||1&e._visibility||(a=!0)),e=s,s=s.return;return 3===e.tag?(s=e.stateNode,a&&null!==t&&(a=31-me(n),null===(r=(e=s.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),s):null}function Rr(e){if(50<Oc)throw Oc=0,Rc=null,Error(o(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Fr={};function Lr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Dr(e,t,n,r){return new Lr(e,t,n,r)}function zr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Mr(e,t){var n=e.alternate;return null===n?((n=Dr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ir(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ur(e,t,n,r,a,s){var i=0;if(r=e,"function"===typeof e)zr(e)&&(i=1);else if("string"===typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,B.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case E:return(e=Dr(31,n,t,a)).elementType=E,e.lanes=s,e;case g:return Vr(n.children,a,s,t);case y:i=8,a|=24;break;case v:return(e=Dr(12,n,t,2|a)).elementType=v,e.lanes=s,e;case N:return(e=Dr(13,n,t,a)).elementType=N,e.lanes=s,e;case j:return(e=Dr(19,n,t,a)).elementType=j,e.lanes=s,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case b:case w:i=10;break e;case x:i=9;break e;case k:i=11;break e;case S:i=14;break e;case _:i=16,r=null;break e}i=29,n=Error(o(130,null===e?"null":typeof e,"")),r=null}return(t=Dr(i,n,t,a)).elementType=e,t.type=r,t.lanes=s,t}function Vr(e,t,n,r){return(e=Dr(7,e,r,t)).lanes=n,e}function Br(e,t,n){return(e=Dr(6,e,null,t)).lanes=n,e}function qr(e,t,n){return(t=Dr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var $r=[],Hr=0,Wr=null,Qr=0,Kr=[],Yr=0,Xr=null,Jr=1,Gr="";function Zr(e,t){$r[Hr++]=Qr,$r[Hr++]=Wr,Wr=e,Qr=t}function ea(e,t,n){Kr[Yr++]=Jr,Kr[Yr++]=Gr,Kr[Yr++]=Xr,Xr=e;var r=Jr;e=Gr;var a=32-me(r)-1;r&=~(1<<a),n+=1;var s=32-me(t)+a;if(30<s){var o=a-a%5;s=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Jr=1<<32-me(t)+a|n<<a|r,Gr=s+e}else Jr=1<<s|n<<a|r,Gr=e}function ta(e){null!==e.return&&(Zr(e,1),ea(e,1,0))}function na(e){for(;e===Wr;)Wr=$r[--Hr],$r[Hr]=null,Qr=$r[--Hr],$r[Hr]=null;for(;e===Xr;)Xr=Kr[--Yr],Kr[Yr]=null,Gr=Kr[--Yr],Kr[Yr]=null,Jr=Kr[--Yr],Kr[Yr]=null}var ra=null,aa=null,sa=!1,oa=null,ia=!1,la=Error(o(519));function ca(e){throw ha(jr(Error(o(418,"")),e)),la}function ua(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Oe]=e,t[Re]=r,n){case"dialog":zu("cancel",t),zu("close",t);break;case"iframe":case"object":case"embed":zu("load",t);break;case"video":case"audio":for(n=0;n<Fu.length;n++)zu(Fu[n],t);break;case"source":zu("error",t);break;case"img":case"image":case"link":zu("error",t),zu("load",t);break;case"details":zu("toggle",t);break;case"input":zu("invalid",t),yt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":zu("invalid",t);break;case"textarea":zu("invalid",t),wt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Xu(t.textContent,n)?(null!=r.popover&&(zu("beforetoggle",t),zu("toggle",t)),null!=r.onScroll&&zu("scroll",t),null!=r.onScrollEnd&&zu("scrollend",t),null!=r.onClick&&(t.onclick=Ju),t=!0):t=!1,t||ca(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(ia=!1);case 27:case 3:return void(ia=!0);default:ra=ra.return}}function fa(e){if(e!==ra)return!1;if(!sa)return da(e),sa=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||od(e.type,e.memoizedProps)),t=!t),t&&aa&&ca(e),da(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){aa=yd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}aa=null}}else 27===n?(n=aa,md(e.type)?(e=vd,vd=null,aa=e):aa=n):aa=ra?yd(e.stateNode.nextSibling):null;return!0}function ma(){aa=ra=null,sa=!1}function pa(){var e=oa;return null!==e&&(null===bc?bc=e:bc.push.apply(bc,e),oa=null),e}function ha(e){null===oa?oa=[e]:oa.push(e)}var ga=I(null),ya=null,va=null;function ba(e,t,n){V(ga,t._currentValue),t._currentValue=n}function xa(e){e._currentValue=ga.current,U(ga)}function wa(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ka(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var s=a.dependencies;if(null!==s){var i=a.child;s=s.firstContext;e:for(;null!==s;){var l=s;s=a;for(var c=0;c<t.length;c++)if(l.context===t[c]){s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),wa(s.return,n,e),r||(i=null);break e}s=l.next}}else if(18===a.tag){if(null===(i=a.return))throw Error(o(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),wa(i,n,e),i=null}else i=a.child;if(null!==i)i.return=a;else for(i=a;null!==i;){if(i===e){i=null;break}if(null!==(a=i.sibling)){a.return=i.return,i=a;break}i=i.return}a=i}}function Na(e,t,n,r){e=null;for(var a=t,s=!1;null!==a;){if(!s)if(0!==(524288&a.flags))s=!0;else if(0!==(262144&a.flags))break;if(10===a.tag){var i=a.alternate;if(null===i)throw Error(o(387));if(null!==(i=i.memoizedProps)){var l=a.type;Yn(a.pendingProps.value,i.value)||(null!==e?e.push(l):e=[l])}}else if(a===H.current){if(null===(i=a.alternate))throw Error(o(387));i.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Kd):e=[Kd])}a=a.return}null!==e&&ka(t,e,n,r),t.flags|=262144}function ja(e){for(e=e.firstContext;null!==e;){if(!Yn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Sa(e){ya=e,va=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function _a(e){return Ta(ya,e)}function Ea(e,t){return null===ya&&Sa(e),Ta(e,t)}function Ta(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===va){if(null===e)throw Error(o(308));va=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else va=va.next=t;return n}var Ca="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},Aa=r.unstable_scheduleCallback,Pa=r.unstable_NormalPriority,Oa={$$typeof:w,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ra(){return{controller:new Ca,data:new Map,refCount:0}}function Fa(e){e.refCount--,0===e.refCount&&Aa(Pa,function(){e.controller.abort()})}var La=null,Da=0,za=0,Ma=null;function Ia(){if(0===--Da&&null!==La){null!==Ma&&(Ma.status="fulfilled");var e=La;La=null,za=0,Ma=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ua=F.S;F.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===La){var n=La=[];Da=0,za=Cu(),Ma={status:"pending",value:void 0,then:function(e){n.push(e)}}}Da++,t.then(Ia,Ia)}(0,t),null!==Ua&&Ua(e,t)};var Va=I(null);function Ba(){var e=Va.current;return null!==e?e:rc.pooledCache}function qa(e,t){V(Va,null===t?Va.current:t.pool)}function $a(){var e=Ba();return null===e?null:{parent:Oa._currentValue,pool:e}}var Ha=Error(o(460)),Wa=Error(o(474)),Qa=Error(o(542)),Ka={then:function(){}};function Ya(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Xa(){}function Ja(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Xa,Xa),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw es(e=t.reason),e;default:if("string"===typeof t.status)t.then(Xa,Xa);else{if(null!==(e=rc)&&100<e.shellSuspendCounter)throw Error(o(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw es(e=t.reason),e}throw Ga=t,Ha}}var Ga=null;function Za(){if(null===Ga)throw Error(o(459));var e=Ga;return Ga=null,e}function es(e){if(e===Ha||e===Qa)throw Error(o(483))}var ts=!1;function ns(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function rs(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function as(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ss(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&nc)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Rr(e),Or(e,null,n),t}return Cr(e,r,t,n),Rr(e)}function os(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ee(e,n)}}function is(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,s=null;if(null!==(n=n.firstBaseUpdate)){do{var o={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===s?a=s=o:s=s.next=o,n=n.next}while(null!==n);null===s?a=s=t:s=s.next=t}else a=s=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:s,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var ls=!1;function cs(){if(ls){if(null!==Ma)throw Ma}}function us(e,t,n,r){ls=!1;var a=e.updateQueue;ts=!1;var s=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var l=i,c=l.next;l.next=null,null===o?s=c:o.next=c,o=l;var u=e.alternate;null!==u&&((i=(u=u.updateQueue).lastBaseUpdate)!==o&&(null===i?u.firstBaseUpdate=c:i.next=c,u.lastBaseUpdate=l))}if(null!==s){var d=a.baseState;for(o=0,u=c=l=null,i=s;;){var m=-536870913&i.lane,p=m!==i.lane;if(p?(sc&m)===m:(r&m)===m){0!==m&&m===za&&(ls=!0),null!==u&&(u=u.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var h=e,g=i;m=t;var y=n;switch(g.tag){case 1:if("function"===typeof(h=g.payload)){d=h.call(y,d,m);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(m="function"===typeof(h=g.payload)?h.call(y,d,m):h)||void 0===m)break e;d=f({},d,m);break e;case 2:ts=!0}}null!==(m=i.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=a.callbacks)?a.callbacks=[m]:p.push(m))}else p={lane:m,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===u?(c=u=p,l=d):u=u.next=p,o|=m;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(p=i).next,p.next=null,a.lastBaseUpdate=p,a.shared.pending=null}}null===u&&(l=d),a.baseState=l,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null===s&&(a.shared.lanes=0),mc|=o,e.lanes=o,e.memoizedState=d}}function ds(e,t){if("function"!==typeof e)throw Error(o(191,e));e.call(t)}function fs(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)ds(n[e],t)}var ms=I(null),ps=I(0);function hs(e,t){V(ps,e=dc),V(ms,t),dc=e|t.baseLanes}function gs(){V(ps,dc),V(ms,ms.current)}function ys(){dc=ps.current,U(ms),U(ps)}var vs=0,bs=null,xs=null,ws=null,ks=!1,Ns=!1,js=!1,Ss=0,_s=0,Es=null,Ts=0;function Cs(){throw Error(o(321))}function As(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Yn(e[n],t[n]))return!1;return!0}function Ps(e,t,n,r,a,s){return vs=s,bs=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,F.H=null===e||null===e.memoizedState?Wo:Qo,js=!1,s=n(r,a),js=!1,Ns&&(s=Rs(t,n,r,a)),Os(e),s}function Os(e){F.H=Ho;var t=null!==xs&&null!==xs.next;if(vs=0,ws=xs=bs=null,ks=!1,_s=0,Es=null,t)throw Error(o(300));null===e||Ei||null!==(e=e.dependencies)&&ja(e)&&(Ei=!0)}function Rs(e,t,n,r){bs=e;var a=0;do{if(Ns&&(Es=null),_s=0,Ns=!1,25<=a)throw Error(o(301));if(a+=1,ws=xs=null,null!=e.updateQueue){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,null!=s.memoCache&&(s.memoCache.index=0)}F.H=Ko,s=t(n,r)}while(Ns);return s}function Fs(){var e=F.H,t=e.useState()[0];return t="function"===typeof t.then?Us(t):t,e=e.useState()[0],(null!==xs?xs.memoizedState:null)!==e&&(bs.flags|=1024),t}function Ls(){var e=0!==Ss;return Ss=0,e}function Ds(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function zs(e){if(ks){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}ks=!1}vs=0,ws=xs=bs=null,Ns=!1,_s=Ss=0,Es=null}function Ms(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ws?bs.memoizedState=ws=e:ws=ws.next=e,ws}function Is(){if(null===xs){var e=bs.alternate;e=null!==e?e.memoizedState:null}else e=xs.next;var t=null===ws?bs.memoizedState:ws.next;if(null!==t)ws=t,xs=e;else{if(null===e){if(null===bs.alternate)throw Error(o(467));throw Error(o(310))}e={memoizedState:(xs=e).memoizedState,baseState:xs.baseState,baseQueue:xs.baseQueue,queue:xs.queue,next:null},null===ws?bs.memoizedState=ws=e:ws=ws.next=e}return ws}function Us(e){var t=_s;return _s+=1,null===Es&&(Es=[]),e=Ja(Es,e,t),t=bs,null===(null===ws?t.memoizedState:ws.next)&&(t=t.alternate,F.H=null===t||null===t.memoizedState?Wo:Qo),e}function Vs(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Us(e);if(e.$$typeof===w)return _a(e)}throw Error(o(438,String(e)))}function Bs(e){var t=null,n=bs.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=bs.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},bs.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=T;return t.index++,n}function qs(e,t){return"function"===typeof t?t(e):t}function $s(e){return Hs(Is(),xs,e)}function Hs(e,t,n){var r=e.queue;if(null===r)throw Error(o(311));r.lastRenderedReducer=n;var a=e.baseQueue,s=r.pending;if(null!==s){if(null!==a){var i=a.next;a.next=s.next,s.next=i}t.baseQueue=a=s,r.pending=null}if(s=e.baseState,null===a)e.memoizedState=s;else{var l=i=null,c=null,u=t=a.next,d=!1;do{var f=-536870913&u.lane;if(f!==u.lane?(sc&f)===f:(vs&f)===f){var m=u.revertLane;if(0===m)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),f===za&&(d=!0);else{if((vs&m)===m){u=u.next,m===za&&(d=!0);continue}f={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(l=c=f,i=s):c=c.next=f,bs.lanes|=m,mc|=m}f=u.action,js&&n(s,f),s=u.hasEagerState?u.eagerState:n(s,f)}else m={lane:f,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(l=c=m,i=s):c=c.next=m,bs.lanes|=f,mc|=f;u=u.next}while(null!==u&&u!==t);if(null===c?i=s:c.next=l,!Yn(s,e.memoizedState)&&(Ei=!0,d&&null!==(n=Ma)))throw n;e.memoizedState=s,e.baseState=i,e.baseQueue=c,r.lastRenderedState=s}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Ws(e){var t=Is(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,s=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{s=e(s,i.action),i=i.next}while(i!==a);Yn(s,t.memoizedState)||(Ei=!0),t.memoizedState=s,null===t.baseQueue&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Qs(e,t,n){var r=bs,a=Is(),s=sa;if(s){if(void 0===n)throw Error(o(407));n=n()}else n=t();var i=!Yn((xs||a).memoizedState,n);if(i&&(a.memoizedState=n,Ei=!0),a=a.queue,yo(2048,8,Xs.bind(null,r,a,e),[e]),a.getSnapshot!==t||i||null!==ws&&1&ws.memoizedState.tag){if(r.flags|=2048,po(9,{destroy:void 0,resource:void 0},Ys.bind(null,r,a,n,t),null),null===rc)throw Error(o(349));s||0!==(124&vs)||Ks(r,t,n)}return n}function Ks(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=bs.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},bs.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ys(e,t,n,r){t.value=n,t.getSnapshot=r,Js(t)&&Gs(e)}function Xs(e,t,n){return n(function(){Js(t)&&Gs(e)})}function Js(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Yn(e,n)}catch(r){return!0}}function Gs(e){var t=Pr(e,2);null!==t&&Dc(t,e,2)}function Zs(e){var t=Ms();if("function"===typeof e){var n=e;if(e=n(),js){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:qs,lastRenderedState:e},t}function eo(e,t,n,r){return e.baseState=n,Hs(e,xs,"function"===typeof r?r:qs)}function to(e,t,n,r,a){if(Bo(e))throw Error(o(485));if(null!==(e=t.action)){var s={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){s.listeners.push(e)}};null!==F.T?n(!0):s.isTransition=!1,r(s),null===(n=t.pending)?(s.next=t.pending=s,no(t,s)):(s.next=n.next,t.pending=n.next=s)}}function no(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var s=F.T,o={};F.T=o;try{var i=n(a,r),l=F.S;null!==l&&l(o,i),ro(e,t,i)}catch(c){so(e,t,c)}finally{F.T=s}}else try{ro(e,t,s=n(a,r))}catch(u){so(e,t,u)}}function ro(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then(function(n){ao(e,t,n)},function(n){return so(e,t,n)}):ao(e,t,n)}function ao(e,t,n){t.status="fulfilled",t.value=n,oo(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,no(e,n)))}function so(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,oo(t),t=t.next}while(t!==r)}e.action=null}function oo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function io(e,t){return t}function lo(e,t){if(sa){var n=rc.formState;if(null!==n){e:{var r=bs;if(sa){if(aa){t:{for(var a=aa,s=ia;8!==a.nodeType;){if(!s){a=null;break t}if(null===(a=yd(a.nextSibling))){a=null;break t}}a="F!"===(s=a.data)||"F"===s?a:null}if(a){aa=yd(a.nextSibling),r="F!"===a.data;break e}}ca(r)}r=!1}r&&(t=n[0])}}return(n=Ms()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:io,lastRenderedState:t},n.queue=r,n=Io.bind(null,bs,r),r.dispatch=n,r=Zs(!1),s=Vo.bind(null,bs,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Ms()).queue=a,n=to.bind(null,bs,a,s,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function co(e){return uo(Is(),xs,e)}function uo(e,t,n){if(t=Hs(e,t,io)[0],e=$s(qs)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Us(t)}catch(o){if(o===Ha)throw Qa;throw o}else r=t;var a=(t=Is()).queue,s=a.dispatch;return n!==t.memoizedState&&(bs.flags|=2048,po(9,{destroy:void 0,resource:void 0},fo.bind(null,a,n),null)),[r,s,e]}function fo(e,t){e.action=t}function mo(e){var t=Is(),n=xs;if(null!==n)return uo(t,n,e);Is(),t=t.memoizedState;var r=(n=Is()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function po(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=bs.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},bs.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function ho(){return Is().memoizedState}function go(e,t,n,r){var a=Ms();r=void 0===r?null:r,bs.flags|=e,a.memoizedState=po(1|t,{destroy:void 0,resource:void 0},n,r)}function yo(e,t,n,r){var a=Is();r=void 0===r?null:r;var s=a.memoizedState.inst;null!==xs&&null!==r&&As(r,xs.memoizedState.deps)?a.memoizedState=po(t,s,n,r):(bs.flags|=e,a.memoizedState=po(1|t,s,n,r))}function vo(e,t){go(8390656,8,e,t)}function bo(e,t){yo(2048,8,e,t)}function xo(e,t){return yo(4,2,e,t)}function wo(e,t){return yo(4,4,e,t)}function ko(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function No(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,yo(4,4,ko.bind(null,t,e),n)}function jo(){}function So(e,t){var n=Is();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&As(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function _o(e,t){var n=Is();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&As(t,r[1]))return r[0];if(r=e(),js){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function Eo(e,t,n){return void 0===n||0!==(1073741824&vs)?e.memoizedState=t:(e.memoizedState=n,e=Lc(),bs.lanes|=e,mc|=e,n)}function To(e,t,n,r){return Yn(n,t)?n:null!==ms.current?(e=Eo(e,n,r),Yn(e,t)||(Ei=!0),e):0===(42&vs)?(Ei=!0,e.memoizedState=n):(e=Lc(),bs.lanes|=e,mc|=e,t)}function Co(e,t,n,r,a){var s=L.p;L.p=0!==s&&8>s?s:8;var o=F.T,i={};F.T=i,Vo(e,!1,t,n);try{var l=a(),c=F.S;if(null!==c&&c(i,l),null!==l&&"object"===typeof l&&"function"===typeof l.then)Uo(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)},function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)}),r}(l,r),Fc());else Uo(e,t,r,Fc())}catch(u){Uo(e,t,{then:function(){},status:"rejected",reason:u},Fc())}finally{L.p=s,F.T=o}}function Ao(){}function Po(e,t,n,r){if(5!==e.tag)throw Error(o(476));var a=Oo(e).queue;Co(e,a,t,D,null===n?Ao:function(){return Ro(e),n(r)})}function Oo(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:D,baseState:D,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:qs,lastRenderedState:D},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:qs,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Ro(e){Uo(e,Oo(e).next.queue,{},Fc())}function Fo(){return _a(Kd)}function Lo(){return Is().memoizedState}function Do(){return Is().memoizedState}function zo(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Fc(),r=ss(t,e=as(n),n);return null!==r&&(Dc(r,t,n),os(r,t,n)),t={cache:Ra()},void(e.payload=t)}t=t.return}}function Mo(e,t,n){var r=Fc();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Bo(e)?qo(t,n):null!==(n=Ar(e,t,n,r))&&(Dc(n,e,r),$o(n,t,r))}function Io(e,t,n){Uo(e,t,n,Fc())}function Uo(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Bo(e))qo(t,a);else{var s=e.alternate;if(0===e.lanes&&(null===s||0===s.lanes)&&null!==(s=t.lastRenderedReducer))try{var o=t.lastRenderedState,i=s(o,n);if(a.hasEagerState=!0,a.eagerState=i,Yn(i,o))return Cr(e,t,a,0),null===rc&&Tr(),!1}catch(l){}if(null!==(n=Ar(e,t,a,r)))return Dc(n,e,r),$o(n,t,r),!0}return!1}function Vo(e,t,n,r){if(r={lane:2,revertLane:Cu(),action:r,hasEagerState:!1,eagerState:null,next:null},Bo(e)){if(t)throw Error(o(479))}else null!==(t=Ar(e,n,r,2))&&Dc(t,e,2)}function Bo(e){var t=e.alternate;return e===bs||null!==t&&t===bs}function qo(e,t){Ns=ks=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function $o(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ee(e,n)}}var Ho={readContext:_a,use:Vs,useCallback:Cs,useContext:Cs,useEffect:Cs,useImperativeHandle:Cs,useLayoutEffect:Cs,useInsertionEffect:Cs,useMemo:Cs,useReducer:Cs,useRef:Cs,useState:Cs,useDebugValue:Cs,useDeferredValue:Cs,useTransition:Cs,useSyncExternalStore:Cs,useId:Cs,useHostTransitionStatus:Cs,useFormState:Cs,useActionState:Cs,useOptimistic:Cs,useMemoCache:Cs,useCacheRefresh:Cs},Wo={readContext:_a,use:Vs,useCallback:function(e,t){return Ms().memoizedState=[e,void 0===t?null:t],e},useContext:_a,useEffect:vo,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,go(4194308,4,ko.bind(null,t,e),n)},useLayoutEffect:function(e,t){return go(4194308,4,e,t)},useInsertionEffect:function(e,t){go(4,2,e,t)},useMemo:function(e,t){var n=Ms();t=void 0===t?null:t;var r=e();if(js){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Ms();if(void 0!==n){var a=n(t);if(js){fe(!0);try{n(t)}finally{fe(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Mo.bind(null,bs,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ms().memoizedState=e},useState:function(e){var t=(e=Zs(e)).queue,n=Io.bind(null,bs,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:jo,useDeferredValue:function(e,t){return Eo(Ms(),e,t)},useTransition:function(){var e=Zs(!1);return e=Co.bind(null,bs,e.queue,!0,!1),Ms().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=bs,a=Ms();if(sa){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===rc)throw Error(o(349));0!==(124&sc)||Ks(r,t,n)}a.memoizedState=n;var s={value:n,getSnapshot:t};return a.queue=s,vo(Xs.bind(null,r,s,e),[e]),r.flags|=2048,po(9,{destroy:void 0,resource:void 0},Ys.bind(null,r,s,n,t),null),n},useId:function(){var e=Ms(),t=rc.identifierPrefix;if(sa){var n=Gr;t="\xab"+t+"R"+(n=(Jr&~(1<<32-me(Jr)-1)).toString(32)+n),0<(n=Ss++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=Ts++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:Fo,useFormState:lo,useActionState:lo,useOptimistic:function(e){var t=Ms();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Vo.bind(null,bs,!0,n),n.dispatch=t,[e,t]},useMemoCache:Bs,useCacheRefresh:function(){return Ms().memoizedState=zo.bind(null,bs)}},Qo={readContext:_a,use:Vs,useCallback:So,useContext:_a,useEffect:bo,useImperativeHandle:No,useInsertionEffect:xo,useLayoutEffect:wo,useMemo:_o,useReducer:$s,useRef:ho,useState:function(){return $s(qs)},useDebugValue:jo,useDeferredValue:function(e,t){return To(Is(),xs.memoizedState,e,t)},useTransition:function(){var e=$s(qs)[0],t=Is().memoizedState;return["boolean"===typeof e?e:Us(e),t]},useSyncExternalStore:Qs,useId:Lo,useHostTransitionStatus:Fo,useFormState:co,useActionState:co,useOptimistic:function(e,t){return eo(Is(),0,e,t)},useMemoCache:Bs,useCacheRefresh:Do},Ko={readContext:_a,use:Vs,useCallback:So,useContext:_a,useEffect:bo,useImperativeHandle:No,useInsertionEffect:xo,useLayoutEffect:wo,useMemo:_o,useReducer:Ws,useRef:ho,useState:function(){return Ws(qs)},useDebugValue:jo,useDeferredValue:function(e,t){var n=Is();return null===xs?Eo(n,e,t):To(n,xs.memoizedState,e,t)},useTransition:function(){var e=Ws(qs)[0],t=Is().memoizedState;return["boolean"===typeof e?e:Us(e),t]},useSyncExternalStore:Qs,useId:Lo,useHostTransitionStatus:Fo,useFormState:mo,useActionState:mo,useOptimistic:function(e,t){var n=Is();return null!==xs?eo(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Bs,useCacheRefresh:Do},Yo=null,Xo=0;function Jo(e){var t=Xo;return Xo+=1,null===Yo&&(Yo=[]),Ja(Yo,e,t)}function Go(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zo(e,t){if(t.$$typeof===m)throw Error(o(525));throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ei(e){return(0,e._init)(e._payload)}function ti(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=Mr(e,t)).index=0,e.sibling=null,e}function s(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Br(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var s=n.type;return s===g?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===s||"object"===typeof s&&null!==s&&s.$$typeof===_&&ei(s)===t.type)?(Go(t=a(t,n.props),n),t.return=e,t):(Go(t=Ur(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=qr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,s){return null===t||7!==t.tag?((t=Vr(n,e.mode,r,s)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Br(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case p:return Go(n=Ur(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case h:return(t=qr(t,e.mode,n)).return=e,t;case _:return f(e,t=(0,t._init)(t._payload),n)}if(R(t)||A(t))return(t=Vr(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return f(e,Jo(t),n);if(t.$$typeof===w)return f(e,Ea(e,t),n);Zo(e,t)}return null}function m(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==a?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case p:return n.key===a?c(e,t,n,r):null;case h:return n.key===a?u(e,t,n,r):null;case _:return m(e,t,n=(a=n._init)(n._payload),r)}if(R(n)||A(n))return null!==a?null:d(e,t,n,r,null);if("function"===typeof n.then)return m(e,t,Jo(n),r);if(n.$$typeof===w)return m(e,t,Ea(e,n),r);Zo(e,n)}return null}function y(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return l(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case p:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case h:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case _:return y(e,t,n,r=(0,r._init)(r._payload),a)}if(R(r)||A(r))return d(t,e=e.get(n)||null,r,a,null);if("function"===typeof r.then)return y(e,t,n,Jo(r),a);if(r.$$typeof===w)return y(e,t,n,Ea(t,r),a);Zo(t,r)}return null}function v(l,c,u,d){if("object"===typeof u&&null!==u&&u.type===g&&null===u.key&&(u=u.props.children),"object"===typeof u&&null!==u){switch(u.$$typeof){case p:e:{for(var b=u.key;null!==c;){if(c.key===b){if((b=u.type)===g){if(7===c.tag){n(l,c.sibling),(d=a(c,u.props.children)).return=l,l=d;break e}}else if(c.elementType===b||"object"===typeof b&&null!==b&&b.$$typeof===_&&ei(b)===c.type){n(l,c.sibling),Go(d=a(c,u.props),u),d.return=l,l=d;break e}n(l,c);break}t(l,c),c=c.sibling}u.type===g?((d=Vr(u.props.children,l.mode,d,u.key)).return=l,l=d):(Go(d=Ur(u.type,u.key,u.props,null,l.mode,d),u),d.return=l,l=d)}return i(l);case h:e:{for(b=u.key;null!==c;){if(c.key===b){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){n(l,c.sibling),(d=a(c,u.children||[])).return=l,l=d;break e}n(l,c);break}t(l,c),c=c.sibling}(d=qr(u,l.mode,d)).return=l,l=d}return i(l);case _:return v(l,c,u=(b=u._init)(u._payload),d)}if(R(u))return function(a,o,i,l){for(var c=null,u=null,d=o,p=o=0,h=null;null!==d&&p<i.length;p++){d.index>p?(h=d,d=null):h=d.sibling;var g=m(a,d,i[p],l);if(null===g){null===d&&(d=h);break}e&&d&&null===g.alternate&&t(a,d),o=s(g,o,p),null===u?c=g:u.sibling=g,u=g,d=h}if(p===i.length)return n(a,d),sa&&Zr(a,p),c;if(null===d){for(;p<i.length;p++)null!==(d=f(a,i[p],l))&&(o=s(d,o,p),null===u?c=d:u.sibling=d,u=d);return sa&&Zr(a,p),c}for(d=r(d);p<i.length;p++)null!==(h=y(d,a,p,i[p],l))&&(e&&null!==h.alternate&&d.delete(null===h.key?p:h.key),o=s(h,o,p),null===u?c=h:u.sibling=h,u=h);return e&&d.forEach(function(e){return t(a,e)}),sa&&Zr(a,p),c}(l,c,u,d);if(A(u)){if("function"!==typeof(b=A(u)))throw Error(o(150));return function(a,i,l,c){if(null==l)throw Error(o(151));for(var u=null,d=null,p=i,h=i=0,g=null,v=l.next();null!==p&&!v.done;h++,v=l.next()){p.index>h?(g=p,p=null):g=p.sibling;var b=m(a,p,v.value,c);if(null===b){null===p&&(p=g);break}e&&p&&null===b.alternate&&t(a,p),i=s(b,i,h),null===d?u=b:d.sibling=b,d=b,p=g}if(v.done)return n(a,p),sa&&Zr(a,h),u;if(null===p){for(;!v.done;h++,v=l.next())null!==(v=f(a,v.value,c))&&(i=s(v,i,h),null===d?u=v:d.sibling=v,d=v);return sa&&Zr(a,h),u}for(p=r(p);!v.done;h++,v=l.next())null!==(v=y(p,a,h,v.value,c))&&(e&&null!==v.alternate&&p.delete(null===v.key?h:v.key),i=s(v,i,h),null===d?u=v:d.sibling=v,d=v);return e&&p.forEach(function(e){return t(a,e)}),sa&&Zr(a,h),u}(l,c,u=b.call(u),d)}if("function"===typeof u.then)return v(l,c,Jo(u),d);if(u.$$typeof===w)return v(l,c,Ea(l,u),d);Zo(l,u)}return"string"===typeof u&&""!==u||"number"===typeof u||"bigint"===typeof u?(u=""+u,null!==c&&6===c.tag?(n(l,c.sibling),(d=a(c,u)).return=l,l=d):(n(l,c),(d=Br(u,l.mode,d)).return=l,l=d),i(l)):n(l,c)}return function(e,t,n,r){try{Xo=0;var a=v(e,t,n,r);return Yo=null,a}catch(o){if(o===Ha||o===Qa)throw o;var s=Dr(29,o,null,e.mode);return s.lanes=r,s.return=e,s}}}var ni=ti(!0),ri=ti(!1),ai=I(null),si=null;function oi(e){var t=e.alternate;V(ui,1&ui.current),V(ai,e),null===si&&(null===t||null!==ms.current||null!==t.memoizedState)&&(si=e)}function ii(e){if(22===e.tag){if(V(ui,ui.current),V(ai,e),null===si){var t=e.alternate;null!==t&&null!==t.memoizedState&&(si=e)}}else li()}function li(){V(ui,ui.current),V(ai,ai.current)}function ci(e){U(ai),si===e&&(si=null),U(ui)}var ui=I(0);function di(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||gd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fi(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var mi={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Fc(),a=as(r);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=ss(e,a,r))&&(Dc(t,e,r),os(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Fc(),a=as(r);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=ss(e,a,r))&&(Dc(t,e,r),os(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Fc(),r=as(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=ss(e,r,n))&&(Dc(t,e,n),os(t,e,n))}};function pi(e,t,n,r,a,s,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,s,o):!t.prototype||!t.prototype.isPureReactComponent||(!Xn(n,r)||!Xn(a,s))}function hi(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&mi.enqueueReplaceState(t,t.state,null)}function gi(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=f({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var yi="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function vi(e){yi(e)}function bi(e){console.error(e)}function xi(e){yi(e)}function wi(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function ki(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function Ni(e,t,n){return(n=as(n)).tag=3,n.payload={element:null},n.callback=function(){wi(e,t)},n}function ji(e){return(e=as(e)).tag=3,e}function Si(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"===typeof a){var s=r.value;e.payload=function(){return a(s)},e.callback=function(){ki(t,n,r)}}var o=n.stateNode;null!==o&&"function"===typeof o.componentDidCatch&&(e.callback=function(){ki(t,n,r),"function"!==typeof a&&(null===jc?jc=new Set([this]):jc.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var _i=Error(o(461)),Ei=!1;function Ti(e,t,n,r){t.child=null===e?ri(t,null,n,r):ni(t,e.child,n,r)}function Ci(e,t,n,r,a){n=n.render;var s=t.ref;if("ref"in r){var o={};for(var i in r)"ref"!==i&&(o[i]=r[i])}else o=r;return Sa(t),r=Ps(e,t,n,o,s,a),i=Ls(),null===e||Ei?(sa&&i&&ta(t),t.flags|=1,Ti(e,t,r,a),t.child):(Ds(e,t,a),Yi(e,t,a))}function Ai(e,t,n,r,a){if(null===e){var s=n.type;return"function"!==typeof s||zr(s)||void 0!==s.defaultProps||null!==n.compare?((e=Ur(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=s,Pi(e,t,s,r,a))}if(s=e.child,!Xi(e,a)){var o=s.memoizedProps;if((n=null!==(n=n.compare)?n:Xn)(o,r)&&e.ref===t.ref)return Yi(e,t,a)}return t.flags|=1,(e=Mr(s,r)).ref=t.ref,e.return=t,t.child=e}function Pi(e,t,n,r,a){if(null!==e){var s=e.memoizedProps;if(Xn(s,r)&&e.ref===t.ref){if(Ei=!1,t.pendingProps=r=s,!Xi(e,a))return t.lanes=e.lanes,Yi(e,t,a);0!==(131072&e.flags)&&(Ei=!0)}}return Li(e,t,n,r,a)}function Oi(e,t,n){var r=t.pendingProps,a=r.children,s=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==s?s.baseLanes|n:n,null!==e){for(a=t.child=e.child,s=0;null!==a;)s=s|a.lanes|a.childLanes,a=a.sibling;t.childLanes=s&~r}else t.childLanes=0,t.child=null;return Ri(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Ri(e,t,null!==s?s.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&qa(0,null!==s?s.cachePool:null),null!==s?hs(t,s):gs(),ii(t)}else null!==s?(qa(0,s.cachePool),hs(t,s),li(),t.memoizedState=null):(null!==e&&qa(0,null),gs(),li());return Ti(e,t,a,n),t.child}function Ri(e,t,n,r){var a=Ba();return a=null===a?null:{parent:Oa._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&qa(0,null),gs(),ii(t),null!==e&&Na(e,t,r,!0),null}function Fi(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(o(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Li(e,t,n,r,a){return Sa(t),n=Ps(e,t,n,r,void 0,a),r=Ls(),null===e||Ei?(sa&&r&&ta(t),t.flags|=1,Ti(e,t,n,a),t.child):(Ds(e,t,a),Yi(e,t,a))}function Di(e,t,n,r,a,s){return Sa(t),t.updateQueue=null,n=Rs(t,r,n,a),Os(e),r=Ls(),null===e||Ei?(sa&&r&&ta(t),t.flags|=1,Ti(e,t,n,s),t.child):(Ds(e,t,s),Yi(e,t,s))}function zi(e,t,n,r,a){if(Sa(t),null===t.stateNode){var s=Fr,o=n.contextType;"object"===typeof o&&null!==o&&(s=_a(o)),s=new n(r,s),t.memoizedState=null!==s.state&&void 0!==s.state?s.state:null,s.updater=mi,t.stateNode=s,s._reactInternals=t,(s=t.stateNode).props=r,s.state=t.memoizedState,s.refs={},ns(t),o=n.contextType,s.context="object"===typeof o&&null!==o?_a(o):Fr,s.state=t.memoizedState,"function"===typeof(o=n.getDerivedStateFromProps)&&(fi(t,n,o,r),s.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof s.getSnapshotBeforeUpdate||"function"!==typeof s.UNSAFE_componentWillMount&&"function"!==typeof s.componentWillMount||(o=s.state,"function"===typeof s.componentWillMount&&s.componentWillMount(),"function"===typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount(),o!==s.state&&mi.enqueueReplaceState(s,s.state,null),us(t,r,s,a),cs(),s.state=t.memoizedState),"function"===typeof s.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){s=t.stateNode;var i=t.memoizedProps,l=gi(n,i);s.props=l;var c=s.context,u=n.contextType;o=Fr,"object"===typeof u&&null!==u&&(o=_a(u));var d=n.getDerivedStateFromProps;u="function"===typeof d||"function"===typeof s.getSnapshotBeforeUpdate,i=t.pendingProps!==i,u||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(i||c!==o)&&hi(t,s,r,o),ts=!1;var f=t.memoizedState;s.state=f,us(t,r,s,a),cs(),c=t.memoizedState,i||f!==c||ts?("function"===typeof d&&(fi(t,n,d,r),c=t.memoizedState),(l=ts||pi(t,n,l,r,f,c,o))?(u||"function"!==typeof s.UNSAFE_componentWillMount&&"function"!==typeof s.componentWillMount||("function"===typeof s.componentWillMount&&s.componentWillMount(),"function"===typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount()),"function"===typeof s.componentDidMount&&(t.flags|=4194308)):("function"===typeof s.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),s.props=r,s.state=c,s.context=o,r=l):("function"===typeof s.componentDidMount&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,rs(e,t),u=gi(n,o=t.memoizedProps),s.props=u,d=t.pendingProps,f=s.context,c=n.contextType,l=Fr,"object"===typeof c&&null!==c&&(l=_a(c)),(c="function"===typeof(i=n.getDerivedStateFromProps)||"function"===typeof s.getSnapshotBeforeUpdate)||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(o!==d||f!==l)&&hi(t,s,r,l),ts=!1,f=t.memoizedState,s.state=f,us(t,r,s,a),cs();var m=t.memoizedState;o!==d||f!==m||ts||null!==e&&null!==e.dependencies&&ja(e.dependencies)?("function"===typeof i&&(fi(t,n,i,r),m=t.memoizedState),(u=ts||pi(t,n,u,r,f,m,l)||null!==e&&null!==e.dependencies&&ja(e.dependencies))?(c||"function"!==typeof s.UNSAFE_componentWillUpdate&&"function"!==typeof s.componentWillUpdate||("function"===typeof s.componentWillUpdate&&s.componentWillUpdate(r,m,l),"function"===typeof s.UNSAFE_componentWillUpdate&&s.UNSAFE_componentWillUpdate(r,m,l)),"function"===typeof s.componentDidUpdate&&(t.flags|=4),"function"===typeof s.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof s.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),s.props=r,s.state=m,s.context=l,r=u):("function"!==typeof s.componentDidUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||o===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return s=r,Fi(e,t),r=0!==(128&t.flags),s||r?(s=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:s.render(),t.flags|=1,null!==e&&r?(t.child=ni(t,e.child,null,a),t.child=ni(t,null,n,a)):Ti(e,t,n,a),t.memoizedState=s.state,e=t.child):e=Yi(e,t,a),e}function Mi(e,t,n,r){return ma(),t.flags|=256,Ti(e,t,n,r),t.child}var Ii={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ui(e){return{baseLanes:e,cachePool:$a()}}function Vi(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=gc),e}function Bi(e,t,n){var r,a=t.pendingProps,s=!1,i=0!==(128&t.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&0!==(2&ui.current)),r&&(s=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(sa){if(s?oi(t):li(),sa){var l,c=aa;if(l=c){e:{for(l=c,c=ia;8!==l.nodeType;){if(!c){c=null;break e}if(null===(l=yd(l.nextSibling))){c=null;break e}}c=l}null!==c?(t.memoizedState={dehydrated:c,treeContext:null!==Xr?{id:Jr,overflow:Gr}:null,retryLane:536870912,hydrationErrors:null},(l=Dr(18,null,null,0)).stateNode=c,l.return=t,t.child=l,ra=t,aa=null,l=!0):l=!1}l||ca(t)}if(null!==(c=t.memoizedState)&&null!==(c=c.dehydrated))return gd(c)?t.lanes=32:t.lanes=536870912,null;ci(t)}return c=a.children,a=a.fallback,s?(li(),c=$i({mode:"hidden",children:c},s=t.mode),a=Vr(a,s,n,null),c.return=t,a.return=t,c.sibling=a,t.child=c,(s=t.child).memoizedState=Ui(n),s.childLanes=Vi(e,r,n),t.memoizedState=Ii,a):(oi(t),qi(t,c))}if(null!==(l=e.memoizedState)&&null!==(c=l.dehydrated)){if(i)256&t.flags?(oi(t),t.flags&=-257,t=Hi(e,t,n)):null!==t.memoizedState?(li(),t.child=e.child,t.flags|=128,t=null):(li(),s=a.fallback,c=t.mode,a=$i({mode:"visible",children:a.children},c),(s=Vr(s,c,n,null)).flags|=2,a.return=t,s.return=t,a.sibling=s,t.child=a,ni(t,e.child,null,n),(a=t.child).memoizedState=Ui(n),a.childLanes=Vi(e,r,n),t.memoizedState=Ii,t=s);else if(oi(t),gd(c)){if(r=c.nextSibling&&c.nextSibling.dataset)var u=r.dgst;r=u,(a=Error(o(419))).stack="",a.digest=r,ha({value:a,source:null,stack:null}),t=Hi(e,t,n)}else if(Ei||Na(e,t,n,!1),r=0!==(n&e.childLanes),Ei||r){if(null!==(r=rc)&&(0!==(a=0!==((a=0!==(42&(a=n&-n))?1:Te(a))&(r.suspendedLanes|n))?0:a)&&a!==l.retryLane))throw l.retryLane=a,Pr(e,a),Dc(r,e,a),_i;"$?"===c.data||Qc(),t=Hi(e,t,n)}else"$?"===c.data?(t.flags|=192,t.child=e.child,t=null):(e=l.treeContext,aa=yd(c.nextSibling),ra=t,sa=!0,oa=null,ia=!1,null!==e&&(Kr[Yr++]=Jr,Kr[Yr++]=Gr,Kr[Yr++]=Xr,Jr=e.id,Gr=e.overflow,Xr=t),(t=qi(t,a.children)).flags|=4096);return t}return s?(li(),s=a.fallback,c=t.mode,u=(l=e.child).sibling,(a=Mr(l,{mode:"hidden",children:a.children})).subtreeFlags=65011712&l.subtreeFlags,null!==u?s=Mr(u,s):(s=Vr(s,c,n,null)).flags|=2,s.return=t,a.return=t,a.sibling=s,t.child=a,a=s,s=t.child,null===(c=e.child.memoizedState)?c=Ui(n):(null!==(l=c.cachePool)?(u=Oa._currentValue,l=l.parent!==u?{parent:u,pool:u}:l):l=$a(),c={baseLanes:c.baseLanes|n,cachePool:l}),s.memoizedState=c,s.childLanes=Vi(e,r,n),t.memoizedState=Ii,a):(oi(t),e=(n=e.child).sibling,(n=Mr(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function qi(e,t){return(t=$i({mode:"visible",children:t},e.mode)).return=e,e.child=t}function $i(e,t){return(e=Dr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Hi(e,t,n){return ni(t,e.child,null,n),(e=qi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Wi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),wa(e.return,t,n)}function Qi(e,t,n,r,a){var s=e.memoizedState;null===s?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=a)}function Ki(e,t,n){var r=t.pendingProps,a=r.revealOrder,s=r.tail;if(Ti(e,t,r.children,n),0!==(2&(r=ui.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Wi(e,n,t);else if(19===e.tag)Wi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(V(ui,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===di(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Qi(t,!1,a,n,s);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===di(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Qi(t,!0,n,null,s);break;case"together":Qi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Yi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),mc|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Na(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Mr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Mr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Xi(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!ja(e))}function Ji(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Ei=!0;else{if(!Xi(e,n)&&0===(128&t.flags))return Ei=!1,function(e,t,n){switch(t.tag){case 3:W(t,t.stateNode.containerInfo),ba(0,Oa,e.memoizedState.cache),ma();break;case 27:case 5:K(t);break;case 4:W(t,t.stateNode.containerInfo);break;case 10:ba(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(oi(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Bi(e,t,n):(oi(t),null!==(e=Yi(e,t,n))?e.sibling:null);oi(t);break;case 19:var a=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(Na(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Ki(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),V(ui,ui.current),r)break;return null;case 22:case 23:return t.lanes=0,Oi(e,t,n);case 24:ba(0,Oa,e.memoizedState.cache)}return Yi(e,t,n)}(e,t,n);Ei=0!==(131072&e.flags)}else Ei=!1,sa&&0!==(1048576&t.flags)&&ea(t,Qr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((a=r.$$typeof)===k){t.tag=11,t=Ci(null,t,r,e,n);break e}if(a===S){t.tag=14,t=Ai(null,t,r,e,n);break e}}throw t=O(r)||r,Error(o(306,t,""))}zr(r)?(e=gi(r,e),t.tag=1,t=zi(null,t,r,e,n)):(t.tag=0,t=Li(null,t,r,e,n))}return t;case 0:return Li(e,t,t.type,t.pendingProps,n);case 1:return zi(e,t,r=t.type,a=gi(r,t.pendingProps),n);case 3:e:{if(W(t,t.stateNode.containerInfo),null===e)throw Error(o(387));r=t.pendingProps;var s=t.memoizedState;a=s.element,rs(e,t),us(t,r,null,n);var i=t.memoizedState;if(r=i.cache,ba(0,Oa,r),r!==s.cache&&ka(t,[Oa],n,!0),cs(),r=i.element,s.isDehydrated){if(s={element:r,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=s,t.memoizedState=s,256&t.flags){t=Mi(e,t,r,n);break e}if(r!==a){ha(a=jr(Error(o(424)),t)),t=Mi(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(aa=yd(e.firstChild),ra=t,sa=!0,oa=null,ia=!0,n=ri(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ma(),r===a){t=Yi(e,t,n);break e}Ti(e,t,r,n)}t=t.child}return t;case 26:return Fi(e,t),null===e?(n=Td(t.type,null,t.pendingProps,null))?t.memoizedState=n:sa||(n=t.type,e=t.pendingProps,(r=rd($.current).createElement(n))[Oe]=t,r[Re]=e,ed(r,n,e),He(r),t.stateNode=r):t.memoizedState=Td(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return K(t),null===e&&sa&&(r=t.stateNode=xd(t.type,t.pendingProps,$.current),ra=t,ia=!0,a=aa,md(t.type)?(vd=a,aa=yd(r.firstChild)):aa=a),Ti(e,t,t.pendingProps.children,n),Fi(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&sa&&((a=r=aa)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Ie])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(s=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(s!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((s=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&s&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var s=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===s)return e}if(null===(e=yd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,ia))?(t.stateNode=r,ra=t,aa=yd(r.firstChild),ia=!1,a=!0):a=!1),a||ca(t)),K(t),a=t.type,s=t.pendingProps,i=null!==e?e.memoizedProps:null,r=s.children,od(a,s)?r=null:null!==i&&od(a,i)&&(t.flags|=32),null!==t.memoizedState&&(a=Ps(e,t,Fs,null,null,n),Kd._currentValue=a),Fi(e,t),Ti(e,t,r,n),t.child;case 6:return null===e&&sa&&((e=n=aa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yd(e.nextSibling)))return null}return e}(n,t.pendingProps,ia))?(t.stateNode=n,ra=t,aa=null,e=!0):e=!1),e||ca(t)),null;case 13:return Bi(e,t,n);case 4:return W(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ni(t,null,r,n):Ti(e,t,r,n),t.child;case 11:return Ci(e,t,t.type,t.pendingProps,n);case 7:return Ti(e,t,t.pendingProps,n),t.child;case 8:case 12:return Ti(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,ba(0,t.type,r.value),Ti(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,Sa(t),r=r(a=_a(a)),t.flags|=1,Ti(e,t,r,n),t.child;case 14:return Ai(e,t,t.type,t.pendingProps,n);case 15:return Pi(e,t,t.type,t.pendingProps,n);case 19:return Ki(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=$i(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Mr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Oi(e,t,n);case 24:return Sa(t),r=_a(Oa),null===e?(null===(a=Ba())&&(a=rc,s=Ra(),a.pooledCache=s,s.refCount++,null!==s&&(a.pooledCacheLanes|=n),a=s),t.memoizedState={parent:r,cache:a},ns(t),ba(0,Oa,a)):(0!==(e.lanes&n)&&(rs(e,t),us(t,null,null,n),cs()),a=e.memoizedState,s=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),ba(0,Oa,r)):(r=s.cache,ba(0,Oa,r),r!==a.cache&&ka(t,[Oa],n,!0))),Ti(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(o(156,t.tag))}function Gi(e){e.flags|=4}function Zi(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Vd(t)){if(null!==(t=ai.current)&&((4194048&sc)===sc?null!==si:(62914560&sc)!==sc&&0===(536870912&sc)||t!==si))throw Ga=Ka,Wa;e.flags|=8192}}function el(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Ne():536870912,e.lanes|=t,yc|=t)}function tl(e,t){if(!sa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function nl(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rl(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return nl(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),xa(Oa),Q(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fa(t)?Gi(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,pa())),nl(t),null;case 26:return n=t.memoizedState,null===e?(Gi(t),null!==n?(nl(t),Zi(t,n)):(nl(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Gi(t),nl(t),Zi(t,n)):(nl(t),t.flags&=-16777217):(e.memoizedProps!==r&&Gi(t),nl(t),t.flags&=-16777217),null;case 27:Y(t),n=$.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Gi(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return nl(t),null}e=B.current,fa(t)?ua(t):(e=xd(a,r,n),t.stateNode=e,Gi(t))}return nl(t),null;case 5:if(Y(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Gi(t);else{if(!r){if(null===t.stateNode)throw Error(o(166));return nl(t),null}if(e=B.current,fa(t))ua(t);else{switch(a=rd($.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[Oe]=t,e[Re]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Gi(t)}}return nl(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Gi(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(e=$.current,fa(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Oe]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Xu(e.nodeValue,n)))||ca(t)}else(e=rd(e).createTextNode(r))[Oe]=t,t.stateNode=e}return nl(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(o(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(o(317));a[Oe]=t}else ma(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;nl(t),a=!1}else a=pa(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(ci(t),t):(ci(t),null)}if(ci(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var s=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(s=r.memoizedState.cachePool.pool),s!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),el(t,t.updateQueue),nl(t),null;case 4:return Q(),null===e&&Uu(t.stateNode.containerInfo),nl(t),null;case 10:return xa(t.type),nl(t),null;case 19:if(U(ui),null===(a=t.memoizedState))return nl(t),null;if(r=0!==(128&t.flags),null===(s=a.rendering))if(r)tl(a,!1);else{if(0!==fc||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=di(e))){for(t.flags|=128,tl(a,!1),e=s.updateQueue,t.updateQueue=e,el(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ir(n,e),n=n.sibling;return V(ui,1&ui.current|2),t.child}e=e.sibling}null!==a.tail&&te()>kc&&(t.flags|=128,r=!0,tl(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=di(s))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,el(t,e),tl(a,!0),null===a.tail&&"hidden"===a.tailMode&&!s.alternate&&!sa)return nl(t),null}else 2*te()-a.renderingStartTime>kc&&536870912!==n&&(t.flags|=128,r=!0,tl(a,!1),t.lanes=4194304);a.isBackwards?(s.sibling=t.child,t.child=s):(null!==(e=a.last)?e.sibling=s:t.child=s,a.last=s)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,e=ui.current,V(ui,r?1&e|2:1&e),t):(nl(t),null);case 22:case 23:return ci(t),ys(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(nl(t),6&t.subtreeFlags&&(t.flags|=8192)):nl(t),null!==(n=t.updateQueue)&&el(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&U(Va),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),xa(Oa),nl(t),null;case 25:case 30:return null}throw Error(o(156,t.tag))}function al(e,t){switch(na(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return xa(Oa),Q(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Y(t),null;case 13:if(ci(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));ma()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return U(ui),null;case 4:return Q(),null;case 10:return xa(t.type),null;case 22:case 23:return ci(t),ys(),null!==e&&U(Va),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return xa(Oa),null;default:return null}}function sl(e,t){switch(na(t),t.tag){case 3:xa(Oa),Q();break;case 26:case 27:case 5:Y(t);break;case 4:Q();break;case 13:ci(t);break;case 19:U(ui);break;case 10:xa(t.type);break;case 22:case 23:ci(t),ys(),null!==e&&U(Va);break;case 24:xa(Oa)}}function ol(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var s=n.create,o=n.inst;r=s(),o.destroy=r}n=n.next}while(n!==a)}}catch(i){uu(t,t.return,i)}}function il(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var s=a.next;r=s;do{if((r.tag&e)===e){var o=r.inst,i=o.destroy;if(void 0!==i){o.destroy=void 0,a=t;var l=n,c=i;try{c()}catch(u){uu(a,l,u)}}}r=r.next}while(r!==s)}}catch(u){uu(t,t.return,u)}}function ll(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{fs(t,n)}catch(r){uu(e,e.return,r)}}}function cl(e,t,n){n.props=gi(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){uu(e,t,r)}}function ul(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(a){uu(e,t,a)}}function dl(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(a){uu(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(s){uu(e,t,s)}else n.current=null}function fl(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){uu(e,e.return,a)}}function ml(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,s=null,i=null,l=null,c=null,u=null,d=null;for(p in n){var f=n[p];if(n.hasOwnProperty(p)&&null!=f)switch(p){case"checked":case"value":break;case"defaultValue":c=f;default:r.hasOwnProperty(p)||Gu(e,t,p,null,r,f)}}for(var m in r){var p=r[m];if(f=n[m],r.hasOwnProperty(m)&&(null!=p||null!=f))switch(m){case"type":s=p;break;case"name":a=p;break;case"checked":u=p;break;case"defaultChecked":d=p;break;case"value":i=p;break;case"defaultValue":l=p;break;case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(o(137,t));break;default:p!==f&&Gu(e,t,m,p,r,f)}}return void gt(e,i,l,c,u,d,s,a);case"select":for(s in p=i=l=m=null,n)if(c=n[s],n.hasOwnProperty(s)&&null!=c)switch(s){case"value":break;case"multiple":p=c;default:r.hasOwnProperty(s)||Gu(e,t,s,null,r,c)}for(a in r)if(s=r[a],c=n[a],r.hasOwnProperty(a)&&(null!=s||null!=c))switch(a){case"value":m=s;break;case"defaultValue":l=s;break;case"multiple":i=s;default:s!==c&&Gu(e,t,a,s,r,c)}return t=l,n=i,r=p,void(null!=m?bt(e,!!n,m,!1):!!r!==!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(l in p=m=null,n)if(a=n[l],n.hasOwnProperty(l)&&null!=a&&!r.hasOwnProperty(l))switch(l){case"value":case"children":break;default:Gu(e,t,l,null,r,a)}for(i in r)if(a=r[i],s=n[i],r.hasOwnProperty(i)&&(null!=a||null!=s))switch(i){case"value":m=a;break;case"defaultValue":p=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(o(91));break;default:a!==s&&Gu(e,t,i,a,r,s)}return void xt(e,m,p);case"option":for(var h in n)if(m=n[h],n.hasOwnProperty(h)&&null!=m&&!r.hasOwnProperty(h))if("selected"===h)e.selected=!1;else Gu(e,t,h,null,r,m);for(c in r)if(m=r[c],p=n[c],r.hasOwnProperty(c)&&m!==p&&(null!=m||null!=p))if("selected"===c)e.selected=m&&"function"!==typeof m&&"symbol"!==typeof m;else Gu(e,t,c,m,r,p);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)m=n[g],n.hasOwnProperty(g)&&null!=m&&!r.hasOwnProperty(g)&&Gu(e,t,g,null,r,m);for(u in r)if(m=r[u],p=n[u],r.hasOwnProperty(u)&&m!==p&&(null!=m||null!=p))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=m)throw Error(o(137,t));break;default:Gu(e,t,u,m,r,p)}return;default:if(_t(t)){for(var y in n)m=n[y],n.hasOwnProperty(y)&&void 0!==m&&!r.hasOwnProperty(y)&&Zu(e,t,y,void 0,r,m);for(d in r)m=r[d],p=n[d],!r.hasOwnProperty(d)||m===p||void 0===m&&void 0===p||Zu(e,t,d,m,r,p);return}}for(var v in n)m=n[v],n.hasOwnProperty(v)&&null!=m&&!r.hasOwnProperty(v)&&Gu(e,t,v,null,r,m);for(f in r)m=r[f],p=n[f],!r.hasOwnProperty(f)||m===p||null==m&&null==p||Gu(e,t,f,m,r,p)}(r,e.type,n,t),r[Re]=t}catch(a){uu(e,e.return,a)}}function pl(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&md(e.type)||4===e.tag}function hl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||pl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&md(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function gl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Ju));else if(4!==r&&(27===r&&md(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(gl(e,t,n),e=e.sibling;null!==e;)gl(e,t,n),e=e.sibling}function yl(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&md(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(yl(e,t,n),e=e.sibling;null!==e;)yl(e,t,n),e=e.sibling}function vl(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);ed(t,r,n),t[Oe]=e,t[Re]=n}catch(s){uu(e,e.return,s)}}var bl=!1,xl=!1,wl=!1,kl="function"===typeof WeakSet?WeakSet:Set,Nl=null;function jl(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:zl(e,n),4&r&&ol(5,n);break;case 1:if(zl(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(o){uu(n,n.return,o)}else{var a=gi(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(i){uu(n,n.return,i)}}64&r&&ll(n),512&r&&ul(n,n.return);break;case 3:if(zl(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{fs(e,t)}catch(o){uu(n,n.return,o)}}break;case 27:null===t&&4&r&&vl(n);case 26:case 5:zl(e,n),null===t&&4&r&&fl(n),512&r&&ul(n,n.return);break;case 12:zl(e,n);break;case 13:zl(e,n),4&r&&Al(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=pu.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||bl)){t=null!==t&&null!==t.memoizedState||xl,a=bl;var s=xl;bl=r,(xl=t)&&!s?Il(e,n,0!==(8772&n.subtreeFlags)):zl(e,n),bl=a,xl=s}break;case 30:break;default:zl(e,n)}}function Sl(e){var t=e.alternate;null!==t&&(e.alternate=null,Sl(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Ue(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var _l=null,El=!1;function Tl(e,t,n){for(n=n.child;null!==n;)Cl(e,t,n),n=n.sibling}function Cl(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ue,n)}catch(s){}switch(n.tag){case 26:xl||dl(n,t),Tl(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:xl||dl(n,t);var r=_l,a=El;md(n.type)&&(_l=n.stateNode,El=!1),Tl(e,t,n),wd(n.stateNode),_l=r,El=a;break;case 5:xl||dl(n,t);case 6:if(r=_l,a=El,_l=null,Tl(e,t,n),El=a,null!==(_l=r))if(El)try{(9===_l.nodeType?_l.body:"HTML"===_l.nodeName?_l.ownerDocument.body:_l).removeChild(n.stateNode)}catch(o){uu(n,t,o)}else try{_l.removeChild(n.stateNode)}catch(o){uu(n,t,o)}break;case 18:null!==_l&&(El?(pd(9===(e=_l).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Tf(e)):pd(_l,n.stateNode));break;case 4:r=_l,a=El,_l=n.stateNode.containerInfo,El=!0,Tl(e,t,n),_l=r,El=a;break;case 0:case 11:case 14:case 15:xl||il(2,n,t),xl||il(4,n,t),Tl(e,t,n);break;case 1:xl||(dl(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&cl(n,t,r)),Tl(e,t,n);break;case 21:Tl(e,t,n);break;case 22:xl=(r=xl)||null!==n.memoizedState,Tl(e,t,n),xl=r;break;default:Tl(e,t,n)}}function Al(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Tf(e)}catch(n){uu(t,t.return,n)}}function Pl(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new kl),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new kl),t;default:throw Error(o(435,e.tag))}}(e);t.forEach(function(t){var r=hu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function Ol(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],s=e,i=t,l=i;e:for(;null!==l;){switch(l.tag){case 27:if(md(l.type)){_l=l.stateNode,El=!1;break e}break;case 5:_l=l.stateNode,El=!1;break e;case 3:case 4:_l=l.stateNode.containerInfo,El=!0;break e}l=l.return}if(null===_l)throw Error(o(160));Cl(s,i,a),_l=null,El=!1,null!==(s=a.alternate)&&(s.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Fl(t,e),t=t.sibling}var Rl=null;function Fl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ol(t,e),Ll(e),4&r&&(il(3,e,e.return),ol(3,e),il(5,e,e.return));break;case 1:Ol(t,e),Ll(e),512&r&&(xl||null===n||dl(n,n.return)),64&r&&bl&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var a=Rl;if(Ol(t,e),Ll(e),512&r&&(xl||null===n||dl(n,n.return)),4&r){var s=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(s=a.getElementsByTagName("title")[0])||s[Ie]||s[Oe]||"http://www.w3.org/2000/svg"===s.namespaceURI||s.hasAttribute("itemprop"))&&(s=a.createElement(r),a.head.insertBefore(s,a.querySelector("head > title"))),ed(s,r,n),s[Oe]=e,He(s),r=s;break e;case"link":var i=Id("link","href",a).get(r+(n.href||""));if(i)for(var l=0;l<i.length;l++)if((s=i[l]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&s.getAttribute("rel")===(null==n.rel?null:n.rel)&&s.getAttribute("title")===(null==n.title?null:n.title)&&s.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(l,1);break t}ed(s=a.createElement(r),r,n),a.head.appendChild(s);break;case"meta":if(i=Id("meta","content",a).get(r+(n.content||"")))for(l=0;l<i.length;l++)if((s=i[l]).getAttribute("content")===(null==n.content?null:""+n.content)&&s.getAttribute("name")===(null==n.name?null:n.name)&&s.getAttribute("property")===(null==n.property?null:n.property)&&s.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&s.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(l,1);break t}ed(s=a.createElement(r),r,n),a.head.appendChild(s);break;default:throw Error(o(468,r))}s[Oe]=e,He(s),r=s}e.stateNode=r}else Ud(a,e.type,e.stateNode);else e.stateNode=Fd(a,r,e.memoizedProps);else s!==r?(null===s?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):s.count--,null===r?Ud(a,e.type,e.stateNode):Fd(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&ml(e,e.memoizedProps,n.memoizedProps)}break;case 27:Ol(t,e),Ll(e),512&r&&(xl||null===n||dl(n,n.return)),null!==n&&4&r&&ml(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Ol(t,e),Ll(e),512&r&&(xl||null===n||dl(n,n.return)),32&e.flags){a=e.stateNode;try{kt(a,"")}catch(p){uu(e,e.return,p)}}4&r&&null!=e.stateNode&&ml(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(wl=!0);break;case 6:if(Ol(t,e),Ll(e),4&r){if(null===e.stateNode)throw Error(o(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(p){uu(e,e.return,p)}}break;case 3:if(Md=null,a=Rl,Rl=jd(t.containerInfo),Ol(t,e),Rl=a,Ll(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Tf(t.containerInfo)}catch(p){uu(e,e.return,p)}wl&&(wl=!1,Dl(e));break;case 4:r=Rl,Rl=jd(e.stateNode.containerInfo),Ol(t,e),Ll(e),Rl=r;break;case 12:default:Ol(t,e),Ll(e);break;case 13:Ol(t,e),Ll(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(wc=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Pl(e,r)));break;case 22:a=null!==e.memoizedState;var c=null!==n&&null!==n.memoizedState,u=bl,d=xl;if(bl=u||a,xl=d||c,Ol(t,e),xl=d,bl=u,Ll(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||c||bl||xl||Ml(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){c=n=t;try{if(s=c.stateNode,a)"function"===typeof(i=s.style).setProperty?i.setProperty("display","none","important"):i.display="none";else{l=c.stateNode;var f=c.memoizedProps.style,m=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;l.style.display=null==m||"boolean"===typeof m?"":(""+m).trim()}}catch(p){uu(c,c.return,p)}}}else if(6===t.tag){if(null===n){c=t;try{c.stateNode.nodeValue=a?"":c.memoizedProps}catch(p){uu(c,c.return,p)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,Pl(e,n))));break;case 19:Ol(t,e),Ll(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,Pl(e,r)));case 30:case 21:}}function Ll(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(pl(r)){n=r;break}r=r.return}if(null==n)throw Error(o(160));switch(n.tag){case 27:var a=n.stateNode;yl(e,hl(e),a);break;case 5:var s=n.stateNode;32&n.flags&&(kt(s,""),n.flags&=-33),yl(e,hl(e),s);break;case 3:case 4:var i=n.stateNode.containerInfo;gl(e,hl(e),i);break;default:throw Error(o(161))}}catch(l){uu(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Dl(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Dl(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function zl(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)jl(e,t.alternate,t),t=t.sibling}function Ml(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:il(4,t,t.return),Ml(t);break;case 1:dl(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&cl(t,t.return,n),Ml(t);break;case 27:wd(t.stateNode);case 26:case 5:dl(t,t.return),Ml(t);break;case 22:null===t.memoizedState&&Ml(t);break;default:Ml(t)}e=e.sibling}}function Il(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,s=t,o=s.flags;switch(s.tag){case 0:case 11:case 15:Il(a,s,n),ol(4,s);break;case 1:if(Il(a,s,n),"function"===typeof(a=(r=s).stateNode).componentDidMount)try{a.componentDidMount()}catch(c){uu(r,r.return,c)}if(null!==(a=(r=s).updateQueue)){var i=r.stateNode;try{var l=a.shared.hiddenCallbacks;if(null!==l)for(a.shared.hiddenCallbacks=null,a=0;a<l.length;a++)ds(l[a],i)}catch(c){uu(r,r.return,c)}}n&&64&o&&ll(s),ul(s,s.return);break;case 27:vl(s);case 26:case 5:Il(a,s,n),n&&null===r&&4&o&&fl(s),ul(s,s.return);break;case 12:Il(a,s,n);break;case 13:Il(a,s,n),n&&4&o&&Al(a,s);break;case 22:null===s.memoizedState&&Il(a,s,n),ul(s,s.return);break;case 30:break;default:Il(a,s,n)}t=t.sibling}}function Ul(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Fa(n))}function Vl(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Fa(e))}function Bl(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)ql(e,t,n,r),t=t.sibling}function ql(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Bl(e,t,n,r),2048&a&&ol(9,t);break;case 1:case 13:default:Bl(e,t,n,r);break;case 3:Bl(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Fa(e)));break;case 12:if(2048&a){Bl(e,t,n,r),e=t.stateNode;try{var s=t.memoizedProps,o=s.id,i=s.onPostCommit;"function"===typeof i&&i(o,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(l){uu(t,t.return,l)}}else Bl(e,t,n,r);break;case 23:break;case 22:s=t.stateNode,o=t.alternate,null!==t.memoizedState?2&s._visibility?Bl(e,t,n,r):Hl(e,t):2&s._visibility?Bl(e,t,n,r):(s._visibility|=2,$l(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&a&&Ul(o,t);break;case 24:Bl(e,t,n,r),2048&a&&Vl(t.alternate,t)}}function $l(e,t,n,r,a){for(a=a&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var s=e,o=t,i=n,l=r,c=o.flags;switch(o.tag){case 0:case 11:case 15:$l(s,o,i,l,a),ol(8,o);break;case 23:break;case 22:var u=o.stateNode;null!==o.memoizedState?2&u._visibility?$l(s,o,i,l,a):Hl(s,o):(u._visibility|=2,$l(s,o,i,l,a)),a&&2048&c&&Ul(o.alternate,o);break;case 24:$l(s,o,i,l,a),a&&2048&c&&Vl(o.alternate,o);break;default:$l(s,o,i,l,a)}t=t.sibling}}function Hl(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:Hl(n,r),2048&a&&Ul(r.alternate,r);break;case 24:Hl(n,r),2048&a&&Vl(r.alternate,r);break;default:Hl(n,r)}t=t.sibling}}var Wl=8192;function Ql(e){if(e.subtreeFlags&Wl)for(e=e.child;null!==e;)Kl(e),e=e.sibling}function Kl(e){switch(e.tag){case 26:Ql(e),e.flags&Wl&&null!==e.memoizedState&&function(e,t,n){if(null===Bd)throw Error(o(475));var r=Bd;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var a=Cd(n.href),s=e.querySelector(Ad(a));if(s)return null!==(e=s._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=$d.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=s,void He(s);s=e.ownerDocument||e,n=Pd(n),(a=kd.get(a))&&Dd(n,a),He(s=s.createElement("link"));var i=s;i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),ed(s,"link",n),t.instance=s}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=$d.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Rl,e.memoizedState,e.memoizedProps);break;case 5:default:Ql(e);break;case 3:case 4:var t=Rl;Rl=jd(e.stateNode.containerInfo),Ql(e),Rl=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Wl,Wl=16777216,Ql(e),Wl=t):Ql(e))}}function Yl(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Xl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Nl=r,Zl(r,e)}Yl(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Jl(e),e=e.sibling}function Jl(e){switch(e.tag){case 0:case 11:case 15:Xl(e),2048&e.flags&&il(9,e,e.return);break;case 3:case 12:default:Xl(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Gl(e)):Xl(e)}}function Gl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Nl=r,Zl(r,e)}Yl(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:il(8,t,t.return),Gl(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Gl(t));break;default:Gl(t)}e=e.sibling}}function Zl(e,t){for(;null!==Nl;){var n=Nl;switch(n.tag){case 0:case 11:case 15:il(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Fa(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Nl=r;else e:for(n=e;null!==Nl;){var a=(r=Nl).sibling,s=r.return;if(Sl(r),r===n){Nl=null;break e}if(null!==a){a.return=s,Nl=a;break e}Nl=s}}}var ec={getCacheForType:function(e){var t=_a(Oa),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tc="function"===typeof WeakMap?WeakMap:Map,nc=0,rc=null,ac=null,sc=0,oc=0,ic=null,lc=!1,cc=!1,uc=!1,dc=0,fc=0,mc=0,pc=0,hc=0,gc=0,yc=0,vc=null,bc=null,xc=!1,wc=0,kc=1/0,Nc=null,jc=null,Sc=0,_c=null,Ec=null,Tc=0,Cc=0,Ac=null,Pc=null,Oc=0,Rc=null;function Fc(){if(0!==(2&nc)&&0!==sc)return sc&-sc;if(null!==F.T){return 0!==za?za:Cu()}return Ae()}function Lc(){0===gc&&(gc=0===(536870912&sc)||sa?ke():536870912);var e=ai.current;return null!==e&&(e.flags|=32),gc}function Dc(e,t,n){(e!==rc||2!==oc&&9!==oc)&&null===e.cancelPendingCommit||(qc(e,0),Uc(e,sc,gc,!1)),Se(e,n),0!==(2&nc)&&e===rc||(e===rc&&(0===(2&nc)&&(pc|=n),4===fc&&Uc(e,sc,gc,!1)),ku(e))}function zc(e,t,n){if(0!==(6&nc))throw Error(o(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||xe(e,t),a=r?function(e,t){var n=nc;nc|=2;var r=Hc(),a=Wc();rc!==e||sc!==t?(Nc=null,kc=te()+500,qc(e,t)):cc=xe(e,t);e:for(;;)try{if(0!==oc&&null!==ac){t=ac;var s=ic;t:switch(oc){case 1:oc=0,ic=null,Zc(e,t,s,1);break;case 2:case 9:if(Ya(s)){oc=0,ic=null,Gc(t);break}t=function(){2!==oc&&9!==oc||rc!==e||(oc=7),ku(e)},s.then(t,t);break e;case 3:oc=7;break e;case 4:oc=5;break e;case 7:Ya(s)?(oc=0,ic=null,Gc(t)):(oc=0,ic=null,Zc(e,t,s,7));break;case 5:var i=null;switch(ac.tag){case 26:i=ac.memoizedState;case 5:case 27:var l=ac;if(!i||Vd(i)){oc=0,ic=null;var c=l.sibling;if(null!==c)ac=c;else{var u=l.return;null!==u?(ac=u,eu(u)):ac=null}break t}}oc=0,ic=null,Zc(e,t,s,5);break;case 6:oc=0,ic=null,Zc(e,t,s,6);break;case 8:Bc(),fc=6;break e;default:throw Error(o(462))}}Xc();break}catch(d){$c(e,d)}return va=ya=null,F.H=r,F.A=a,nc=n,null!==ac?0:(rc=null,sc=0,Tr(),fc)}(e,t):Kc(e,t,!0),s=r;;){if(0===a){cc&&!r&&Uc(e,t,0,!1);break}if(n=e.current.alternate,!s||Ic(n)){if(2===a){if(s=t,e.errorRecoveryDisabledLanes&s)var i=0;else i=0!==(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var l=e;a=vc;var c=l.current.memoizedState.isDehydrated;if(c&&(qc(l,i).flags|=256),2!==(i=Kc(l,i,!1))){if(uc&&!c){l.errorRecoveryDisabledLanes|=s,pc|=s,a=4;break e}s=bc,bc=a,null!==s&&(null===bc?bc=s:bc.push.apply(bc,s))}a=i}if(s=!1,2!==a)continue}}if(1===a){qc(e,0),Uc(e,t,0,!0);break}e:{switch(r=e,s=a){case 0:case 1:throw Error(o(345));case 4:if((4194048&t)!==t)break;case 6:Uc(r,t,gc,!lc);break e;case 2:bc=null;break;case 3:case 5:break;default:throw Error(o(329))}if((62914560&t)===t&&10<(a=wc+300-te())){if(Uc(r,t,gc,!lc),0!==be(r,0,!0))break e;r.timeoutHandle=ld(Mc.bind(null,r,n,bc,Nc,xc,t,gc,pc,yc,lc,s,2,-0,0),a)}else Mc(r,n,bc,Nc,xc,t,gc,pc,yc,lc,s,0,-0,0)}break}a=Kc(e,t,!1),s=!1}ku(e)}function Mc(e,t,n,r,a,s,i,l,c,u,d,f,m,p){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||16785408===(16785408&f))&&(Bd={stylesheets:null,count:0,unsuspend:qd},Kl(t),null!==(f=function(){if(null===Bd)throw Error(o(475));var e=Bd;return e.stylesheets&&0===e.count&&Wd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Wd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(nu.bind(null,e,t,s,n,r,a,i,l,c,d,1,m,p)),void Uc(e,s,i,!u);nu(e,t,s,n,r,a,i,l,c)}function Ic(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],s=a.getSnapshot;a=a.value;try{if(!Yn(s(),a))return!1}catch(o){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Uc(e,t,n,r){t&=~hc,t&=~pc,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var s=31-me(a),o=1<<s;r[s]=-1,a&=~o}0!==n&&_e(e,n,t)}function Vc(){return 0!==(6&nc)||(Nu(0,!1),!1)}function Bc(){if(null!==ac){if(0===oc)var e=ac.return;else va=ya=null,zs(e=ac),Yo=null,Xo=0,e=ac;for(;null!==e;)sl(e.alternate,e),e=e.return;ac=null}}function qc(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,cd(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Bc(),rc=e,ac=n=Mr(e.current,null),sc=t,oc=0,ic=null,lc=!1,cc=xe(e,t),uc=!1,yc=gc=hc=pc=mc=fc=0,bc=vc=null,xc=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-me(r),s=1<<a;t|=e[a],r&=~s}return dc=t,Tr(),n}function $c(e,t){bs=null,F.H=Ho,t===Ha||t===Qa?(t=Za(),oc=3):t===Wa?(t=Za(),oc=4):oc=t===_i?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,ic=t,null===ac&&(fc=1,wi(e,jr(t,e.current)))}function Hc(){var e=F.H;return F.H=Ho,null===e?Ho:e}function Wc(){var e=F.A;return F.A=ec,e}function Qc(){fc=4,lc||(4194048&sc)!==sc&&null!==ai.current||(cc=!0),0===(134217727&mc)&&0===(134217727&pc)||null===rc||Uc(rc,sc,gc,!1)}function Kc(e,t,n){var r=nc;nc|=2;var a=Hc(),s=Wc();rc===e&&sc===t||(Nc=null,qc(e,t)),t=!1;var o=fc;e:for(;;)try{if(0!==oc&&null!==ac){var i=ac,l=ic;switch(oc){case 8:Bc(),o=6;break e;case 3:case 2:case 9:case 6:null===ai.current&&(t=!0);var c=oc;if(oc=0,ic=null,Zc(e,i,l,c),n&&cc){o=0;break e}break;default:c=oc,oc=0,ic=null,Zc(e,i,l,c)}}Yc(),o=fc;break}catch(u){$c(e,u)}return t&&e.shellSuspendCounter++,va=ya=null,nc=r,F.H=a,F.A=s,null===ac&&(rc=null,sc=0,Tr()),o}function Yc(){for(;null!==ac;)Jc(ac)}function Xc(){for(;null!==ac&&!Z();)Jc(ac)}function Jc(e){var t=Ji(e.alternate,e,dc);e.memoizedProps=e.pendingProps,null===t?eu(e):ac=t}function Gc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Di(n,t,t.pendingProps,t.type,void 0,sc);break;case 11:t=Di(n,t,t.pendingProps,t.type.render,t.ref,sc);break;case 5:zs(t);default:sl(n,t),t=Ji(n,t=ac=Ir(t,dc),dc)}e.memoizedProps=e.pendingProps,null===t?eu(e):ac=t}function Zc(e,t,n,r){va=ya=null,zs(t),Yo=null,Xo=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&Na(t,n,a,!0),null!==(n=ai.current)){switch(n.tag){case 13:return null===si?Qc():null===n.alternate&&0===fc&&(fc=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Ka?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),du(e,r,a)),!1;case 22:return n.flags|=65536,r===Ka?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),du(e,r,a)),!1}throw Error(o(435,n.tag))}return du(e,r,a),Qc(),!1}if(sa)return null!==(t=ai.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==la&&ha(jr(e=Error(o(422),{cause:r}),n))):(r!==la&&ha(jr(t=Error(o(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=jr(r,n),is(e,a=Ni(e.stateNode,r,a)),4!==fc&&(fc=2)),!1;var s=Error(o(520),{cause:r});if(s=jr(s,n),null===vc?vc=[s]:vc.push(s),4!==fc&&(fc=2),null===t)return!0;r=jr(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,is(n,e=Ni(n.stateNode,r,e)),!1;case 1:if(t=n.type,s=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==s&&"function"===typeof s.componentDidCatch&&(null===jc||!jc.has(s))))return n.flags|=65536,a&=-a,n.lanes|=a,Si(a=ji(a),e,n,r),is(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,sc))return fc=1,wi(e,jr(n,e.current)),void(ac=null)}catch(s){if(null!==a)throw ac=a,s;return fc=1,wi(e,jr(n,e.current)),void(ac=null)}32768&t.flags?(sa||1===r?e=!0:cc||0!==(536870912&sc)?e=!1:(lc=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=ai.current)&&13===r.tag&&(r.flags|=16384))),tu(t,e)):eu(t)}function eu(e){var t=e;do{if(0!==(32768&t.flags))return void tu(t,lc);e=t.return;var n=rl(t.alternate,t,dc);if(null!==n)return void(ac=n);if(null!==(t=t.sibling))return void(ac=t);ac=t=e}while(null!==t);0===fc&&(fc=5)}function tu(e,t){do{var n=al(e.alternate,e);if(null!==n)return n.flags&=32767,void(ac=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(ac=e);ac=e=n}while(null!==e);fc=6,ac=null}function nu(e,t,n,r,a,s,i,l,c){e.cancelPendingCommit=null;do{iu()}while(0!==Sc);if(0!==(6&nc))throw Error(o(327));if(null!==t){if(t===e.current)throw Error(o(177));if(s=t.lanes|t.childLanes,function(e,t,n,r,a,s){var o=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,l=e.expirationTimes,c=e.hiddenUpdates;for(n=o&~n;0<n;){var u=31-me(n),d=1<<u;i[u]=0,l[u]=-1;var f=c[u];if(null!==f)for(c[u]=null,u=0;u<f.length;u++){var m=f[u];null!==m&&(m.lane&=-536870913)}n&=~d}0!==r&&_e(e,r,0),0!==s&&0===a&&0!==e.tag&&(e.suspendedLanes|=s&~(o&~t))}(e,n,s|=Er,i,l,c),e===rc&&(ac=rc=null,sc=0),Ec=t,_c=e,Tc=n,Cc=s,Ac=a,Pc=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,J(se,function(){return lu(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=F.T,F.T=null,a=L.p,L.p=2,i=nc,nc|=4;try{!function(e,t){if(e=e.containerInfo,td=nf,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch(g){n=null;break e}var i=0,l=-1,c=-1,u=0,d=0,f=e,m=null;t:for(;;){for(var p;f!==n||0!==a&&3!==f.nodeType||(l=i+a),f!==s||0!==r&&3!==f.nodeType||(c=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(p=f.firstChild);)m=f,f=p;for(;;){if(f===e)break t;if(m===n&&++u===a&&(l=i),m===s&&++d===r&&(c=i),null!==(p=f.nextSibling))break;m=(f=m).parentNode}f=p}n=-1===l||-1===c?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},nf=!1,Nl=t;null!==Nl;)if(e=(t=Nl).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,Nl=e;else for(;null!==Nl;){switch(s=(t=Nl).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==s){e=void 0,n=t,a=s.memoizedProps,s=s.memoizedState,r=n.stateNode;try{var h=gi(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(h,s),r.__reactInternalSnapshotBeforeUpdate=e}catch(y){uu(n,n.return,y)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))hd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":hd(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(o(163))}if(null!==(e=t.sibling)){e.return=t.return,Nl=e;break}Nl=t.return}}(e,t)}finally{nc=i,L.p=a,F.T=r}}Sc=1,ru(),au(),su()}}function ru(){if(1===Sc){Sc=0;var e=_c,t=Ec,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=F.T,F.T=null;var r=L.p;L.p=2;var a=nc;nc|=4;try{Fl(t,e);var s=nd,o=er(e.containerInfo),i=s.focusedElem,l=s.selectionRange;if(o!==i&&i&&i.ownerDocument&&Zn(i.ownerDocument.documentElement,i)){if(null!==l&&tr(i)){var c=l.start,u=l.end;if(void 0===u&&(u=c),"selectionStart"in i)i.selectionStart=c,i.selectionEnd=Math.min(u,i.value.length);else{var d=i.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var m=f.getSelection(),p=i.textContent.length,h=Math.min(l.start,p),g=void 0===l.end?h:Math.min(l.end,p);!m.extend&&h>g&&(o=g,g=h,h=o);var y=Gn(i,h),v=Gn(i,g);if(y&&v&&(1!==m.rangeCount||m.anchorNode!==y.node||m.anchorOffset!==y.offset||m.focusNode!==v.node||m.focusOffset!==v.offset)){var b=d.createRange();b.setStart(y.node,y.offset),m.removeAllRanges(),h>g?(m.addRange(b),m.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),m.addRange(b))}}}}for(d=[],m=i;m=m.parentNode;)1===m.nodeType&&d.push({element:m,left:m.scrollLeft,top:m.scrollTop});for("function"===typeof i.focus&&i.focus(),i=0;i<d.length;i++){var x=d[i];x.element.scrollLeft=x.left,x.element.scrollTop=x.top}}nf=!!td,nd=td=null}finally{nc=a,L.p=r,F.T=n}}e.current=t,Sc=2}}function au(){if(2===Sc){Sc=0;var e=_c,t=Ec,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=F.T,F.T=null;var r=L.p;L.p=2;var a=nc;nc|=4;try{jl(e,t.alternate,t)}finally{nc=a,L.p=r,F.T=n}}Sc=3}}function su(){if(4===Sc||3===Sc){Sc=0,ee();var e=_c,t=Ec,n=Tc,r=Pc;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?Sc=5:(Sc=0,Ec=_c=null,ou(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(jc=null),Ce(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ue,t,void 0,128===(128&t.current.flags))}catch(l){}if(null!==r){t=F.T,a=L.p,L.p=2,F.T=null;try{for(var s=e.onRecoverableError,o=0;o<r.length;o++){var i=r[o];s(i.value,{componentStack:i.stack})}}finally{F.T=t,L.p=a}}0!==(3&Tc)&&iu(),ku(e),a=e.pendingLanes,0!==(4194090&n)&&0!==(42&a)?e===Rc?Oc++:(Oc=0,Rc=e):Oc=0,Nu(0,!1)}}function ou(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Fa(t)))}function iu(e){return ru(),au(),su(),lu()}function lu(){if(5!==Sc)return!1;var e=_c,t=Cc;Cc=0;var n=Ce(Tc),r=F.T,a=L.p;try{L.p=32>n?32:n,F.T=null,n=Ac,Ac=null;var s=_c,i=Tc;if(Sc=0,Ec=_c=null,Tc=0,0!==(6&nc))throw Error(o(331));var l=nc;if(nc|=4,Jl(s.current),ql(s,s.current,i,n),nc=l,Nu(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ue,s)}catch(c){}return!0}finally{L.p=a,F.T=r,ou(e,t)}}function cu(e,t,n){t=jr(n,t),null!==(e=ss(e,t=Ni(e.stateNode,t,2),2))&&(Se(e,2),ku(e))}function uu(e,t,n){if(3===e.tag)cu(e,e,n);else for(;null!==t;){if(3===t.tag){cu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===jc||!jc.has(r))){e=jr(n,e),null!==(r=ss(t,n=ji(2),2))&&(Si(n,r,t,e),Se(r,2),ku(r));break}}t=t.return}}function du(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tc;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(uc=!0,a.add(n),e=fu.bind(null,e,t,n),t.then(e,e))}function fu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,rc===e&&(sc&n)===n&&(4===fc||3===fc&&(62914560&sc)===sc&&300>te()-wc?0===(2&nc)&&qc(e,0):hc|=n,yc===sc&&(yc=0)),ku(e)}function mu(e,t){0===t&&(t=Ne()),null!==(e=Pr(e,t))&&(Se(e,t),ku(e))}function pu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),mu(e,n)}function hu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(o(314))}null!==r&&r.delete(t),mu(e,n)}var gu=null,yu=null,vu=!1,bu=!1,xu=!1,wu=0;function ku(e){e!==yu&&null===e.next&&(null===yu?gu=yu=e:yu=yu.next=e),bu=!0,vu||(vu=!0,dd(function(){0!==(6&nc)?J(re,ju):Su()}))}function Nu(e,t){if(!xu&&bu){xu=!0;do{for(var n=!1,r=gu;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var s=0;else{var o=r.suspendedLanes,i=r.pingedLanes;s=(1<<31-me(42|e)+1)-1,s=201326741&(s&=a&~(o&~i))?201326741&s|1:s?2|s:0}0!==s&&(n=!0,Tu(r,s))}else s=sc,0===(3&(s=be(r,r===rc?s:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||xe(r,s)||(n=!0,Tu(r,s));r=r.next}}while(n);xu=!1}}function ju(){Su()}function Su(){bu=vu=!1;var e=0;0!==wu&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==id&&(id=e,!0);return id=null,!1}()&&(e=wu),wu=0);for(var t=te(),n=null,r=gu;null!==r;){var a=r.next,s=_u(r,t);0===s?(r.next=null,null===n?gu=a:n.next=a,null===a&&(yu=n)):(n=r,(0!==e||0!==(3&s))&&(bu=!0)),r=a}Nu(e,!1)}function _u(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,s=-62914561&e.pendingLanes;0<s;){var o=31-me(s),i=1<<o,l=a[o];-1===l?0!==(i&n)&&0===(i&r)||(a[o]=we(i,t)):l<=t&&(e.expiredLanes|=i),s&=~i}if(n=sc,n=be(e,e===(t=rc)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===oc||9===oc)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&G(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||xe(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&G(r),Ce(n)){case 2:case 8:n=ae;break;case 32:default:n=se;break;case 268435456:n=ie}return r=Eu.bind(null,e),n=J(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&G(r),e.callbackPriority=2,e.callbackNode=null,2}function Eu(e,t){if(0!==Sc&&5!==Sc)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(iu()&&e.callbackNode!==n)return null;var r=sc;return 0===(r=be(e,e===rc?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(zc(e,r,t),_u(e,te()),null!=e.callbackNode&&e.callbackNode===n?Eu.bind(null,e):null)}function Tu(e,t){if(iu())return null;zc(e,t,!0)}function Cu(){return 0===wu&&(wu=ke()),wu}function Au(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:Ct(""+e)}function Pu(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Ou=0;Ou<wr.length;Ou++){var Ru=wr[Ou];kr(Ru.toLowerCase(),"on"+(Ru[0].toUpperCase()+Ru.slice(1)))}kr(mr,"onAnimationEnd"),kr(pr,"onAnimationIteration"),kr(hr,"onAnimationStart"),kr("dblclick","onDoubleClick"),kr("focusin","onFocus"),kr("focusout","onBlur"),kr(gr,"onTransitionRun"),kr(yr,"onTransitionStart"),kr(vr,"onTransitionCancel"),kr(br,"onTransitionEnd"),Ye("onMouseEnter",["mouseout","mouseover"]),Ye("onMouseLeave",["mouseout","mouseover"]),Ye("onPointerEnter",["pointerout","pointerover"]),Ye("onPointerLeave",["pointerout","pointerover"]),Ke("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ke("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ke("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ke("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fu="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lu=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Fu));function Du(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],l=i.instance,c=i.currentTarget;if(i=i.listener,l!==s&&a.isPropagationStopped())break e;s=i,a.currentTarget=c;try{s(a)}catch(u){yi(u)}a.currentTarget=null,s=l}else for(o=0;o<r.length;o++){if(l=(i=r[o]).instance,c=i.currentTarget,i=i.listener,l!==s&&a.isPropagationStopped())break e;s=i,a.currentTarget=c;try{s(a)}catch(u){yi(u)}a.currentTarget=null,s=l}}}}function zu(e,t){var n=t[Le];void 0===n&&(n=t[Le]=new Set);var r=e+"__bubble";n.has(r)||(Vu(t,e,2,!1),n.add(r))}function Mu(e,t,n){var r=0;t&&(r|=4),Vu(n,e,r,t)}var Iu="_reactListening"+Math.random().toString(36).slice(2);function Uu(e){if(!e[Iu]){e[Iu]=!0,We.forEach(function(t){"selectionchange"!==t&&(Lu.has(t)||Mu(t,!1,e),Mu(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Iu]||(t[Iu]=!0,Mu("selectionchange",!1,t))}}function Vu(e,t,n,r){switch(uf(t)){case 2:var a=rf;break;case 8:a=af;break;default:a=sf}n=a.bind(null,t,n,e),a=void 0,!It||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Bu(e,t,n,r,a){var s=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var i=r.stateNode.containerInfo;if(i===a)break;if(4===o)for(o=r.return;null!==o;){var c=o.tag;if((3===c||4===c)&&o.stateNode.containerInfo===a)return;o=o.return}for(;null!==i;){if(null===(o=Ve(i)))return;if(5===(c=o.tag)||6===c||26===c||27===c){r=s=o;continue e}i=i.parentNode}}r=r.return}Dt(function(){var r=s,a=Pt(n),o=[];e:{var i=xr.get(e);if(void 0!==i){var c=Zt,u=e;switch(e){case"keypress":if(0===Ht(n))break e;case"keydown":case"keyup":c=hn;break;case"focusin":u="focus",c=sn;break;case"focusout":u="blur",c=sn;break;case"beforeblur":case"afterblur":c=sn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=yn;break;case mr:case pr:case hr:c=on;break;case br:c=vn;break;case"scroll":case"scrollend":c=tn;break;case"wheel":c=bn;break;case"copy":case"cut":case"paste":c=ln;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=gn;break;case"toggle":case"beforetoggle":c=xn}var d=0!==(4&t),f=!d&&("scroll"===e||"scrollend"===e),m=d?null!==i?i+"Capture":null:i;d=[];for(var p,h=r;null!==h;){var g=h;if(p=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===p||null===m||null!=(g=zt(h,m))&&d.push(qu(h,g,p)),f)break;h=h.return}0<d.length&&(i=new c(i,u,null,n,a),o.push({event:i,listeners:d}))}}if(0===(7&t)){if(c="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===At||!(u=n.relatedTarget||n.fromElement)||!Ve(u)&&!u[Fe])&&(c||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,c?(c=r,null!==(u=(u=n.relatedTarget||n.toElement)?Ve(u):null)&&(f=l(u),d=u.tag,u!==f||5!==d&&27!==d&&6!==d)&&(u=null)):(c=null,u=r),c!==u)){if(d=rn,g="onMouseLeave",m="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(d=gn,g="onPointerLeave",m="onPointerEnter",h="pointer"),f=null==c?i:qe(c),p=null==u?i:qe(u),(i=new d(g,h+"leave",c,n,a)).target=f,i.relatedTarget=p,g=null,Ve(a)===r&&((d=new d(m,h+"enter",u,n,a)).target=p,d.relatedTarget=f,g=d),f=g,c&&u)e:{for(m=u,h=0,p=d=c;p;p=Hu(p))h++;for(p=0,g=m;g;g=Hu(g))p++;for(;0<h-p;)d=Hu(d),h--;for(;0<p-h;)m=Hu(m),p--;for(;h--;){if(d===m||null!==m&&d===m.alternate)break e;d=Hu(d),m=Hu(m)}d=null}else d=null;null!==c&&Wu(o,i,c,d,!1),null!==u&&null!==f&&Wu(o,f,u,d,!0)}if("select"===(c=(i=r?qe(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===c&&"file"===i.type)var y=Mn;else if(On(i))if(In)y=Kn;else{y=Wn;var v=Hn}else!(c=i.nodeName)||"input"!==c.toLowerCase()||"checkbox"!==i.type&&"radio"!==i.type?r&&_t(r.elementType)&&(y=Mn):y=Qn;switch(y&&(y=y(e,r))?Rn(o,y,n,a):(v&&v(e,i,r),"focusout"===e&&r&&"number"===i.type&&null!=r.memoizedProps.value&&vt(i,"number",i.value)),v=r?qe(r):window,e){case"focusin":(On(v)||"true"===v.contentEditable)&&(rr=v,ar=r,sr=null);break;case"focusout":sr=ar=rr=null;break;case"mousedown":or=!0;break;case"contextmenu":case"mouseup":case"dragend":or=!1,ir(o,n,a);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":ir(o,n,a)}var b;if(kn)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else An?Tn(e,n)&&(x="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(x="onCompositionStart");x&&(Sn&&"ko"!==n.locale&&(An||"onCompositionStart"!==x?"onCompositionEnd"===x&&An&&(b=$t()):(Bt="value"in(Vt=a)?Vt.value:Vt.textContent,An=!0)),0<(v=$u(r,x)).length&&(x=new cn(x,e,null,n,a),o.push({event:x,listeners:v}),b?x.data=b:null!==(b=Cn(n))&&(x.data=b))),(b=jn?function(e,t){switch(e){case"compositionend":return Cn(t);case"keypress":return 32!==t.which?null:(En=!0,_n);case"textInput":return(e=t.data)===_n&&En?null:e;default:return null}}(e,n):function(e,t){if(An)return"compositionend"===e||!kn&&Tn(e,t)?(e=$t(),qt=Bt=Vt=null,An=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Sn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(x=$u(r,"onBeforeInput")).length&&(v=new cn("onBeforeInput","beforeinput",null,n,a),o.push({event:v,listeners:x}),v.data=b)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var s=Au((a[Re]||null).action),o=r.submitter;o&&null!==(t=(t=o[Re]||null)?Au(t.formAction):o.getAttribute("formAction"))&&(s=t,o=null);var i=new Zt("action","action",null,r,a);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==wu){var e=o?Pu(a,o):new FormData(a);Po(n,{pending:!0,data:e,method:a.method,action:s},null,e)}}else"function"===typeof s&&(i.preventDefault(),e=o?Pu(a,o):new FormData(a),Po(n,{pending:!0,data:e,method:a.method,action:s},s,e))},currentTarget:a}]})}}(o,e,r,n,a)}Du(o,t)})}function qu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function $u(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,s=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===s||(null!=(a=zt(e,n))&&r.unshift(qu(e,a,s)),null!=(a=zt(e,t))&&r.push(qu(e,a,s))),3===e.tag)return r;e=e.return}return[]}function Hu(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Wu(e,t,n,r,a){for(var s=t._reactName,o=[];null!==n&&n!==r;){var i=n,l=i.alternate,c=i.stateNode;if(i=i.tag,null!==l&&l===r)break;5!==i&&26!==i&&27!==i||null===c||(l=c,a?null!=(c=zt(n,s))&&o.unshift(qu(n,c,l)):a||null!=(c=zt(n,s))&&o.push(qu(n,c,l))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Qu=/\r\n?/g,Ku=/\u0000|\uFFFD/g;function Yu(e){return("string"===typeof e?e:""+e).replace(Qu,"\n").replace(Ku,"")}function Xu(e,t){return t=Yu(t),Yu(e)===t}function Ju(){}function Gu(e,t,n,r,a,s){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||kt(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&kt(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":St(e,r,s);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Ct(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof s&&("formAction"===n?("input"!==t&&Gu(e,t,"name",a.name,a,null),Gu(e,t,"formEncType",a.formEncType,a,null),Gu(e,t,"formMethod",a.formMethod,a,null),Gu(e,t,"formTarget",a.formTarget,a,null)):(Gu(e,t,"encType",a.encType,a,null),Gu(e,t,"method",a.method,a,null),Gu(e,t,"target",a.target,a,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Ct(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Ju);break;case"onScroll":null!=r&&zu("scroll",e);break;case"onScrollEnd":null!=r&&zu("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=Ct(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":zu("beforetoggle",e),zu("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Et.get(n)||n,r)}}function Zu(e,t,n,r,a,s){switch(n){case"style":St(e,r,s);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(o(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(o(60));e.innerHTML=n}}break;case"children":"string"===typeof r?kt(e,r):("number"===typeof r||"bigint"===typeof r)&&kt(e,""+r);break;case"onScroll":null!=r&&zu("scroll",e);break;case"onScrollEnd":null!=r&&zu("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Ju);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Qe.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"===typeof(s=null!=(s=e[Re]||null)?s[n]:null)&&e.removeEventListener(t,s,a),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof s&&null!==s&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":zu("error",e),zu("load",e);var r,a=!1,s=!1;for(r in n)if(n.hasOwnProperty(r)){var i=n[r];if(null!=i)switch(r){case"src":a=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Gu(e,t,r,i,n,null)}}return s&&Gu(e,t,"srcSet",n.srcSet,n,null),void(a&&Gu(e,t,"src",n.src,n,null));case"input":zu("invalid",e);var l=r=i=s=null,c=null,u=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":s=d;break;case"type":i=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":r=d;break;case"defaultValue":l=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(o(137,t));break;default:Gu(e,t,a,d,n,null)}}return yt(e,r,l,c,u,i,s,!1),void dt(e);case"select":for(s in zu("invalid",e),a=i=r=null,n)if(n.hasOwnProperty(s)&&null!=(l=n[s]))switch(s){case"value":r=l;break;case"defaultValue":i=l;break;case"multiple":a=l;default:Gu(e,t,s,l,n,null)}return t=r,n=i,e.multiple=!!a,void(null!=t?bt(e,!!a,t,!1):null!=n&&bt(e,!!a,n,!0));case"textarea":for(i in zu("invalid",e),r=s=a=null,n)if(n.hasOwnProperty(i)&&null!=(l=n[i]))switch(i){case"value":a=l;break;case"defaultValue":s=l;break;case"children":r=l;break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(o(91));break;default:Gu(e,t,i,l,n,null)}return wt(e,a,s,r),void dt(e);case"option":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))if("selected"===c)e.selected=a&&"function"!==typeof a&&"symbol"!==typeof a;else Gu(e,t,c,a,n,null);return;case"dialog":zu("beforetoggle",e),zu("toggle",e),zu("cancel",e),zu("close",e);break;case"iframe":case"object":zu("load",e);break;case"video":case"audio":for(a=0;a<Fu.length;a++)zu(Fu[a],e);break;case"image":zu("error",e),zu("load",e);break;case"details":zu("toggle",e);break;case"embed":case"source":case"link":zu("error",e),zu("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(a=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,t));default:Gu(e,t,u,a,n,null)}return;default:if(_t(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(a=n[d])&&Zu(e,t,d,a,n,void 0));return}}for(l in n)n.hasOwnProperty(l)&&(null!=(a=n[l])&&Gu(e,t,l,a,n,null))}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function sd(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function od(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var id=null;var ld="function"===typeof setTimeout?setTimeout:void 0,cd="function"===typeof clearTimeout?clearTimeout:void 0,ud="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ud?function(e){return ud.resolve(null).then(e).catch(fd)}:ld;function fd(e){setTimeout(function(){throw e})}function md(e){return"head"===e}function pd(e,t){var n=t,r=0,a=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&8===s.nodeType)if("/$"===(n=s.data)){if(0<r&&8>r){n=r;var o=e.ownerDocument;if(1&n&&wd(o.documentElement),2&n&&wd(o.body),4&n)for(wd(n=o.head),o=n.firstChild;o;){var i=o.nextSibling,l=o.nodeName;o[Ie]||"SCRIPT"===l||"STYLE"===l||"LINK"===l&&"stylesheet"===o.rel.toLowerCase()||n.removeChild(o),o=i}}if(0===a)return e.removeChild(s),void Tf(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=s}while(n);Tf(t)}function hd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":hd(n),Ue(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var vd=null;function bd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function xd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(o(452));return e;case"head":if(!(e=t.head))throw Error(o(453));return e;case"body":if(!(e=t.body))throw Error(o(454));return e;default:throw Error(o(451))}}function wd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ue(e)}var kd=new Map,Nd=new Set;function jd(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Sd=L.d;L.d={f:function(){var e=Sd.f(),t=Vc();return e||t},r:function(e){var t=Be(e);null!==t&&5===t.tag&&"form"===t.type?Ro(t):Sd.r(e)},D:function(e){Sd.D(e),Ed("dns-prefetch",e,null)},C:function(e,t){Sd.C(e,t),Ed("preconnect",e,t)},L:function(e,t,n){Sd.L(e,t,n);var r=_d;if(r&&e&&t){var a='link[rel="preload"][as="'+ht(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+ht(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(a+='[imagesizes="'+ht(n.imageSizes)+'"]')):a+='[href="'+ht(e)+'"]';var s=a;switch(t){case"style":s=Cd(e);break;case"script":s=Od(e)}kd.has(s)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),kd.set(s,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Ad(s))||"script"===t&&r.querySelector(Rd(s))||(ed(t=r.createElement("link"),"link",e),He(t),r.head.appendChild(t)))}},m:function(e,t){Sd.m(e,t);var n=_d;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+ht(r)+'"][href="'+ht(e)+'"]',s=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":s=Od(e)}if(!kd.has(s)&&(e=f({rel:"modulepreload",href:e},t),kd.set(s,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Rd(s)))return}ed(r=n.createElement("link"),"link",e),He(r),n.head.appendChild(r)}}},X:function(e,t){Sd.X(e,t);var n=_d;if(n&&e){var r=$e(n).hoistableScripts,a=Od(e),s=r.get(a);s||((s=n.querySelector(Rd(a)))||(e=f({src:e,async:!0},t),(t=kd.get(a))&&zd(e,t),He(s=n.createElement("script")),ed(s,"link",e),n.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},r.set(a,s))}},S:function(e,t,n){Sd.S(e,t,n);var r=_d;if(r&&e){var a=$e(r).hoistableStyles,s=Cd(e);t=t||"default";var o=a.get(s);if(!o){var i={loading:0,preload:null};if(o=r.querySelector(Ad(s)))i.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=kd.get(s))&&Dd(e,n);var l=o=r.createElement("link");He(l),ed(l,"link",e),l._p=new Promise(function(e,t){l.onload=e,l.onerror=t}),l.addEventListener("load",function(){i.loading|=1}),l.addEventListener("error",function(){i.loading|=2}),i.loading|=4,Ld(o,t,r)}o={type:"stylesheet",instance:o,count:1,state:i},a.set(s,o)}}},M:function(e,t){Sd.M(e,t);var n=_d;if(n&&e){var r=$e(n).hoistableScripts,a=Od(e),s=r.get(a);s||((s=n.querySelector(Rd(a)))||(e=f({src:e,async:!0,type:"module"},t),(t=kd.get(a))&&zd(e,t),He(s=n.createElement("script")),ed(s,"link",e),n.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},r.set(a,s))}}};var _d="undefined"===typeof document?null:document;function Ed(e,t,n){var r=_d;if(r&&"string"===typeof t&&t){var a=ht(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"===typeof n&&(a+='[crossorigin="'+n+'"]'),Nd.has(a)||(Nd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(ed(t=r.createElement("link"),"link",e),He(t),r.head.appendChild(t)))}}function Td(e,t,n,r){var a,s,i,l,c=(c=$.current)?jd(c):null;if(!c)throw Error(o(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=Cd(n.href),(r=(n=$e(c).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=Cd(n.href);var u=$e(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(Ad(e)))&&!u._p&&(d.instance=u,d.state.loading=5),kd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},kd.set(e,n),u||(a=c,s=e,i=n,l=d.state,a.querySelector('link[rel="preload"][as="style"]['+s+"]")?l.loading=1:(s=a.createElement("link"),l.preload=s,s.addEventListener("load",function(){return l.loading|=1}),s.addEventListener("error",function(){return l.loading|=2}),ed(s,"link",i),He(s),a.head.appendChild(s))))),t&&null===r)throw Error(o(528,""));return d}if(t&&null!==r)throw Error(o(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=Od(n),(r=(n=$e(c).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,e))}}function Cd(e){return'href="'+ht(e)+'"'}function Ad(e){return'link[rel="stylesheet"]['+e+"]"}function Pd(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function Od(e){return'[src="'+ht(e)+'"]'}function Rd(e){return"script[async]"+e}function Fd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+ht(n.href)+'"]');if(r)return t.instance=r,He(r),r;var a=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return He(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",a),Ld(r,n.precedence,e),t.instance=r;case"stylesheet":a=Cd(n.href);var s=e.querySelector(Ad(a));if(s)return t.state.loading|=4,t.instance=s,He(s),s;r=Pd(n),(a=kd.get(a))&&Dd(r,a),He(s=(e.ownerDocument||e).createElement("link"));var i=s;return i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),ed(s,"link",r),t.state.loading|=4,Ld(s,n.precedence,e),t.instance=s;case"script":return s=Od(n.src),(a=e.querySelector(Rd(s)))?(t.instance=a,He(a),a):(r=n,(a=kd.get(s))&&zd(r=f({},n),a),He(a=(e=e.ownerDocument||e).createElement("script")),ed(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(o(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Ld(r,n.precedence,e));return t.instance}function Ld(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,s=a,o=0;o<r.length;o++){var i=r[o];if(i.dataset.precedence===t)s=i;else if(s!==a)break}s?s.parentNode.insertBefore(e,s.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Dd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function zd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Md=null;function Id(e,t,n){if(null===Md){var r=new Map,a=Md=new Map;a.set(n,r)}else(r=(a=Md).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var s=n[a];if(!(s[Ie]||s[Oe]||"link"===e&&"stylesheet"===s.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==s.namespaceURI){var o=s.getAttribute(t)||"";o=e+o;var i=r.get(o);i?i.push(s):r.set(o,[s])}}return r}function Ud(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Vd(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Bd=null;function qd(){}function $d(){if(this.count--,0===this.count)if(this.stylesheets)Wd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Hd=null;function Wd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Hd=new Map,t.forEach(Qd,e),Hd=null,$d.call(e))}function Qd(e,t){if(!(4&t.state.loading)){var n=Hd.get(e);if(n)var r=n.get(null);else{n=new Map,Hd.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),s=0;s<a.length;s++){var o=a[s];"LINK"!==o.nodeName&&"not all"===o.getAttribute("media")||(n.set(o.dataset.precedence,o),r=o)}r&&n.set(null,r)}o=(a=t.instance).getAttribute("data-precedence"),(s=n.get(o)||r)===r&&n.set(null,a),n.set(o,a),this.count++,r=$d.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),s?s.parentNode.insertBefore(a,s.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Kd={$$typeof:w,Provider:null,Consumer:null,_currentValue:D,_currentValue2:D,_threadCount:0};function Yd(e,t,n,r,a,s,o,i){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=je(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=je(0),this.hiddenUpdates=je(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=s,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Xd(e,t,n,r,a,s,o,i,l,c,u,d){return e=new Yd(e,t,n,o,i,l,c,d),t=1,!0===s&&(t|=24),s=Dr(3,null,null,t),e.current=s,s.stateNode=e,(t=Ra()).refCount++,e.pooledCache=t,t.refCount++,s.memoizedState={element:r,isDehydrated:n,cache:t},ns(s),e}function Jd(e){return e?e=Fr:Fr}function Gd(e,t,n,r,a,s){a=Jd(a),null===r.context?r.context=a:r.pendingContext=a,(r=as(t)).payload={element:n},null!==(s=void 0===s?null:s)&&(r.callback=s),null!==(n=ss(e,r,t))&&(Dc(n,0,t),os(n,e,t))}function Zd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ef(e,t){Zd(e,t),(e=e.alternate)&&Zd(e,t)}function tf(e){if(13===e.tag){var t=Pr(e,67108864);null!==t&&Dc(t,0,67108864),ef(e,67108864)}}var nf=!0;function rf(e,t,n,r){var a=F.T;F.T=null;var s=L.p;try{L.p=2,sf(e,t,n,r)}finally{L.p=s,F.T=a}}function af(e,t,n,r){var a=F.T;F.T=null;var s=L.p;try{L.p=8,sf(e,t,n,r)}finally{L.p=s,F.T=a}}function sf(e,t,n,r){if(nf){var a=of(r);if(null===a)Bu(e,t,r,lf,n),bf(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return ff=xf(ff,e,t,n,r,a),!0;case"dragenter":return mf=xf(mf,e,t,n,r,a),!0;case"mouseover":return pf=xf(pf,e,t,n,r,a),!0;case"pointerover":var s=a.pointerId;return hf.set(s,xf(hf.get(s)||null,e,t,n,r,a)),!0;case"gotpointercapture":return s=a.pointerId,gf.set(s,xf(gf.get(s)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(bf(e,r),4&t&&-1<vf.indexOf(e)){for(;null!==a;){var s=Be(a);if(null!==s)switch(s.tag){case 3:if((s=s.stateNode).current.memoizedState.isDehydrated){var o=ve(s.pendingLanes);if(0!==o){var i=s;for(i.pendingLanes|=2,i.entangledLanes|=2;o;){var l=1<<31-me(o);i.entanglements[1]|=l,o&=~l}ku(s),0===(6&nc)&&(kc=te()+500,Nu(0,!1))}}break;case 13:null!==(i=Pr(s,2))&&Dc(i,0,2),Vc(),ef(s,2)}if(null===(s=of(r))&&Bu(e,t,r,lf,n),s===a)break;a=s}null!==a&&r.stopPropagation()}else Bu(e,t,r,null,n)}}function of(e){return cf(e=Pt(e))}var lf=null;function cf(e){if(lf=null,null!==(e=Ve(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=c(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return lf=e,null}function uf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ae:return 8;case se:case oe:return 32;case ie:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,mf=null,pf=null,hf=new Map,gf=new Map,yf=[],vf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bf(e,t){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":mf=null;break;case"mouseover":case"mouseout":pf=null;break;case"pointerover":case"pointerout":hf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gf.delete(t.pointerId)}}function xf(e,t,n,r,a,s){return null===e||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[a]},null!==t&&(null!==(t=Be(t))&&tf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function wf(e){var t=Ve(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=c(n)))return e.blockedOn=t,void function(e,t){var n=L.p;try{return L.p=e,t()}finally{L.p=n}}(e.priority,function(){if(13===n.tag){var e=Fc();e=Te(e);var t=Pr(n,e);null!==t&&Dc(t,0,e),ef(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function kf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=of(e.nativeEvent);if(null!==n)return null!==(t=Be(n))&&tf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);At=r,n.target.dispatchEvent(r),At=null,t.shift()}return!0}function Nf(e,t,n){kf(e)&&n.delete(t)}function jf(){df=!1,null!==ff&&kf(ff)&&(ff=null),null!==mf&&kf(mf)&&(mf=null),null!==pf&&kf(pf)&&(pf=null),hf.forEach(Nf),gf.forEach(Nf)}function Sf(e,t){e.blockedOn===t&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,jf)))}var _f=null;function Ef(e){_f!==e&&(_f=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){_f===e&&(_f=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!==typeof r){if(null===cf(r||n))continue;break}var s=Be(n);null!==s&&(e.splice(t,3),t-=3,Po(s,{pending:!0,data:a,method:n.method,action:r},r,a))}}))}function Tf(e){function t(t){return Sf(t,e)}null!==ff&&Sf(ff,e),null!==mf&&Sf(mf,e),null!==pf&&Sf(pf,e),hf.forEach(t),gf.forEach(t);for(var n=0;n<yf.length;n++){var r=yf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<yf.length&&null===(n=yf[0]).blockedOn;)wf(n),null===n.blockedOn&&yf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],s=n[r+1],o=a[Re]||null;if("function"===typeof s)o||Ef(n);else if(o){var i=null;if(s&&s.hasAttribute("formAction")){if(a=s,o=s[Re]||null)i=o.formAction;else if(null!==cf(a))continue}else i=o.action;"function"===typeof i?n[r+1]=i:(n.splice(r,3),r-=3),Ef(n)}}}function Cf(e){this._internalRoot=e}function Af(e){this._internalRoot=e}Af.prototype.render=Cf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Gd(t.current,Fc(),e,t,null,null)},Af.prototype.unmount=Cf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Gd(e.current,2,null,e,null,null),Vc(),t[Fe]=null}},Af.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ae();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yf.length&&0!==t&&t<yf[n].priority;n++);yf.splice(n,0,e),0===n&&wf(e)}};var Pf=a.version;if("19.1.0"!==Pf)throw Error(o(527,Pf,"19.1.0"));L.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var s=a.alternate;if(null===s){if(null!==(r=a.return)){n=r;continue}break}if(a.child===s.child){for(s=a.child;s;){if(s===n)return u(a),e;if(s===r)return u(a),t;s=s.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=s;else{for(var i=!1,c=a.child;c;){if(c===n){i=!0,n=a,r=s;break}if(c===r){i=!0,r=a,n=s;break}c=c.sibling}if(!i){for(c=s.child;c;){if(c===n){i=!0,n=s,r=a;break}if(c===r){i=!0,r=s,n=a;break}c=c.sibling}if(!i)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var Of={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:F,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Rf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Rf.isDisabled&&Rf.supportsFiber)try{ue=Rf.inject(Of),de=Rf}catch(Lf){}}t.createRoot=function(e,t){if(!i(e))throw Error(o(299));var n=!1,r="",a=vi,s=bi,l=xi;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(s=t.onCaughtError),void 0!==t.onRecoverableError&&(l=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Xd(e,1,!1,null,0,n,r,a,s,l,0,null),e[Fe]=t.current,Uu(e),new Cf(t)},t.hydrateRoot=function(e,t,n){if(!i(e))throw Error(o(299));var r=!1,a="",s=vi,l=bi,c=xi,u=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(s=n.onUncaughtError),void 0!==n.onCaughtError&&(l=n.onCaughtError),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(u=n.formState)),(t=Xd(e,1,!0,t,0,r,a,s,l,c,0,u)).context=Jd(null),n=t.current,(a=as(r=Te(r=Fc()))).callback=null,ss(n,a,r),n=r,t.current.lanes=n,Se(t,n),ku(t),e[Fe]=t.current,Uu(e),new Af(t)},t.version="19.1.0"},43:(e,t,n)=>{"use strict";e.exports=n(288)},288:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),m=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var x=b.prototype=new v;x.constructor=b,h(x,y.prototype),x.isPureReactComponent=!0;var w=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},N=Object.prototype.hasOwnProperty;function j(e,t,r,a,s,o){return r=o.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:o}}function S(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var _=/\/+/g;function E(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function T(){}function C(e,t,a,s,o){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var l,c,u=!1;if(null===e)u=!0;else switch(i){case"bigint":case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case r:u=!0;break;case f:return C((u=e._init)(e._payload),t,a,s,o)}}if(u)return o=o(e),u=""===s?"."+E(e,0):s,w(o)?(a="",null!=u&&(a=u.replace(_,"$&/")+"/"),C(o,t,a,"",function(e){return e})):null!=o&&(S(o)&&(l=o,c=a+(null==o.key||e&&e.key===o.key?"":(""+o.key).replace(_,"$&/")+"/")+u,o=j(l.type,c,void 0,0,0,l.props)),t.push(o)),1;u=0;var d,p=""===s?".":s+":";if(w(e))for(var h=0;h<e.length;h++)u+=C(s=e[h],t,a,i=p+E(s,h),o);else if("function"===typeof(h=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=m&&d[m]||d["@@iterator"])?d:null))for(e=h.call(e),h=0;!(s=e.next()).done;)u+=C(s=s.value,t,a,i=p+E(s,h++),o);else if("object"===i){if("function"===typeof e.then)return C(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(T,T):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,s,o);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return u}function A(e,t,n){if(null==e)return e;var r=[],a=0;return C(e,r,"","",function(e){return t.call(n,e,a++)}),r}function P(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var O="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function R(){}t.Children={map:A,forEach:function(e,t,n){A(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return A(e,function(){t++}),t},toArray:function(e){return A(e,function(e){return e})||[]},only:function(e){if(!S(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=o,t.PureComponent=b,t.StrictMode=s,t.Suspense=u,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return k.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=h({},e.props),a=e.key;if(null!=t)for(s in void 0!==t.ref&&void 0,void 0!==t.key&&(a=""+t.key),t)!N.call(t,s)||"key"===s||"__self"===s||"__source"===s||"ref"===s&&void 0===t.ref||(r[s]=t[s]);var s=arguments.length-2;if(1===s)r.children=n;else if(1<s){for(var o=Array(s),i=0;i<s;i++)o[i]=arguments[i+2];r.children=o}return j(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:i,_context:e},e},t.createElement=function(e,t,n){var r,a={},s=null;if(null!=t)for(r in void 0!==t.key&&(s=""+t.key),t)N.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var o=arguments.length-2;if(1===o)a.children=n;else if(1<o){for(var i=Array(o),l=0;l<o;l++)i[l]=arguments[l+2];a.children=i}if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===a[r]&&(a[r]=o[r]);return j(e,s,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=S,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:P}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=k.T,n={};k.T=n;try{var r=e(),a=k.S;null!==a&&a(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(R,O)}catch(s){O(s)}finally{k.T=t}},t.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},t.use=function(e){return k.H.use(e)},t.useActionState=function(e,t,n){return k.H.useActionState(e,t,n)},t.useCallback=function(e,t){return k.H.useCallback(e,t)},t.useContext=function(e){return k.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return k.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=k.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return k.H.useId()},t.useImperativeHandle=function(e,t,n){return k.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return k.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return k.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return k.H.useMemo(e,t)},t.useOptimistic=function(e,t){return k.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return k.H.useReducer(e,t,n)},t.useRef=function(e){return k.H.useRef(e)},t.useState=function(e){return k.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return k.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return k.H.useTransition()},t.version="19.1.0"},358:(e,t)=>{"use strict";const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,a=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,i=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function l(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function c(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},391:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},517:e=>{const t=/[A-Z\xc0-\xd6\xd8-\xde]?[a-z\xdf-\xf6\xf8-\xff]+(?:['\u2019](?:d|ll|m|re|s|t|ve))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde]|$)|(?:[A-Z\xc0-\xd6\xd8-\xde]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['\u2019](?:D|LL|M|RE|S|T|VE))?(?=[\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000]|[A-Z\xc0-\xd6\xd8-\xde](?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])|$)|[A-Z\xc0-\xd6\xd8-\xde]?(?:[a-z\xdf-\xf6\xf8-\xff]|[^\ud800-\udfff\xac\xb1\xd7\xf7\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\xbf\u2000-\u206f \t\x0b\f\xa0\ufeff\n\r\u2028\u2029\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\d+\u2700-\u27bfa-z\xdf-\xf6\xf8-\xffA-Z\xc0-\xd6\xd8-\xde])+(?:['\u2019](?:d|ll|m|re|s|t|ve))?|[A-Z\xc0-\xd6\xd8-\xde]+(?:['\u2019](?:D|LL|M|RE|S|T|VE))?|\d*(?:1ST|2ND|3RD|(?![123])\dTH)(?=\b|[a-z_])|\d*(?:1st|2nd|3rd|(?![123])\dth)(?=\b|[A-Z_])|\d+|(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|\ud83c[\udffb-\udfff])?)*/g,n=e=>e.match(t)||[],r=e=>e[0].toUpperCase()+e.slice(1),a=(e,t)=>n(e).join(t).toLowerCase(),s=e=>n(e).reduce((e,t)=>"".concat(e).concat(e?t[0].toUpperCase()+t.slice(1).toLowerCase():t.toLowerCase()),"");e.exports={words:n,upperFirst:r,camelCase:s,pascalCase:e=>r(s(e)),snakeCase:e=>a(e,"_"),kebabCase:e=>a(e,"-"),sentenceCase:e=>r(a(e," ")),titleCase:e=>n(e).map(r).join(" ")}},575:e=>{"use strict";function t(e){this._maxSize=e,this.clear()}t.prototype.clear=function(){this._size=0,this._values=Object.create(null)},t.prototype.get=function(e){return this._values[e]},t.prototype.set=function(e,t){return this._size>=this._maxSize&&this.clear(),e in this._values||this._size++,this._values[e]=t};var n=/[^.^\]^[]+|(?=\[\]|\.\.)/g,r=/^\d+$/,a=/^\d/,s=/[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/g,o=/^\s*(['"]?)(.*?)(\1)\s*$/,i=new t(512),l=new t(512),c=new t(512);function u(e){return i.get(e)||i.set(e,d(e).map(function(e){return e.replace(o,"$2")}))}function d(e){return e.match(n)||[""]}function f(e){return"string"===typeof e&&e&&-1!==["'",'"'].indexOf(e.charAt(0))}function m(e){return!f(e)&&(function(e){return e.match(a)&&!e.match(r)}(e)||function(e){return s.test(e)}(e))}e.exports={Cache:t,split:d,normalizePath:u,setter:function(e){var t=u(e);return l.get(e)||l.set(e,function(e,n){for(var r=0,a=t.length,s=e;r<a-1;){var o=t[r];if("__proto__"===o||"constructor"===o||"prototype"===o)return e;s=s[t[r++]]}s[t[r]]=n})},getter:function(e,t){var n=u(e);return c.get(e)||c.set(e,function(e){for(var r=0,a=n.length;r<a;){if(null==e&&t)return;e=e[n[r++]]}return e})},join:function(e){return e.reduce(function(e,t){return e+(f(t)||r.test(t)?"["+t+"]":(e?".":"")+t)},"")},forEach:function(e,t,n){!function(e,t,n){var r,a,s,o,i=e.length;for(a=0;a<i;a++)(r=e[a])&&(m(r)&&(r='"'+r+'"'),s=!(o=f(r))&&/^\d+$/.test(r),t.call(n,r,o,s,a,e))}(Array.isArray(e)?e:d(e),t,n)}}},579:(e,t,n)=>{"use strict";e.exports=n(799)},672:(e,t,n)=>{"use strict";var r=n(43);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var o={d:{f:s,r:function(){throw Error(a(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},i=Symbol.for("react.portal");var l=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:i,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=l.T,n=o.p;try{if(l.T=null,o.p=2,e)return e()}finally{l.T=t,o.p=n,o.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,o.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&o.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin),a="string"===typeof t.integrity?t.integrity:void 0,s="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?o.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:s}):"script"===n&&o.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:s,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=c(t.as,t.crossOrigin);o.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&o.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin);o.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=c(t.as,t.crossOrigin);o.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else o.d.m(e)},t.requestFormReset=function(e){o.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return l.H.useFormState(e,t,n)},t.useFormStatus=function(){return l.H.useHostTransitionStatus()},t.version="19.1.0"},704:e=>{function t(e,t){var n=e.length,r=new Array(n),a={},s=n,o=function(e){for(var t=new Map,n=0,r=e.length;n<r;n++){var a=e[n];t.has(a[0])||t.set(a[0],new Set),t.has(a[1])||t.set(a[1],new Set),t.get(a[0]).add(a[1])}return t}(t),i=function(e){for(var t=new Map,n=0,r=e.length;n<r;n++)t.set(e[n],n);return t}(e);for(t.forEach(function(e){if(!i.has(e[0])||!i.has(e[1]))throw new Error("Unknown node. There is an unknown node in the supplied edges.")});s--;)a[s]||l(e[s],s,new Set);return r;function l(e,t,s){if(s.has(e)){var c;try{c=", node was:"+JSON.stringify(e)}catch(f){c=""}throw new Error("Cyclic dependency"+c)}if(!i.has(e))throw new Error("Found unknown node. Make sure to provided all involved nodes. Unknown node: "+JSON.stringify(e));if(!a[t]){a[t]=!0;var u=o.get(e)||new Set;if(t=(u=Array.from(u)).length){s.add(e);do{var d=u[--t];l(d,i.get(d),s)}while(t);s.delete(e)}r[--n]=e}}}e.exports=function(e){return t(function(e){for(var t=new Set,n=0,r=e.length;n<r;n++){var a=e[n];t.add(a[0]),t.add(a[1])}return Array.from(t)}(e),e)},e.exports.array=t},799:(e,t)=>{"use strict";var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(e,t,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==t.key&&(a=""+t.key),"key"in t)for(var s in r={},t)"key"!==s&&(r[s]=t[s]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:a,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=a,t.jsxs=a},853:(e,t,n)=>{"use strict";e.exports=n(896)},896:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<s(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var i=2*(r+1)-1,l=e[i],c=i+1,u=e[c];if(0>s(l,n))c<a&&0>s(u,l)?(e[r]=u,e[c]=n,r=c):(e[r]=l,e[i]=n,r=i);else{if(!(c<a&&0>s(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function s(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,l=i.now();t.unstable_now=function(){return i.now()-l}}var c=[],u=[],d=1,f=null,m=3,p=!1,h=!1,g=!1,y=!1,v="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,x="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function k(e){if(g=!1,w(e),!h)if(null!==r(c))h=!0,j||(j=!0,N());else{var t=r(u);null!==t&&O(k,t.startTime-e)}}var N,j=!1,S=-1,_=5,E=-1;function T(){return!!y||!(t.unstable_now()-E<_)}function C(){if(y=!1,j){var e=t.unstable_now();E=e;var n=!0;try{e:{h=!1,g&&(g=!1,b(S),S=-1),p=!0;var s=m;try{t:{for(w(e),f=r(c);null!==f&&!(f.expirationTime>e&&T());){var o=f.callback;if("function"===typeof o){f.callback=null,m=f.priorityLevel;var i=o(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof i){f.callback=i,w(e),n=!0;break t}f===r(c)&&a(c),w(e)}else a(c);f=r(c)}if(null!==f)n=!0;else{var l=r(u);null!==l&&O(k,l.startTime-e),n=!1}}break e}finally{f=null,m=s,p=!1}n=void 0}}finally{n?N():j=!1}}}if("function"===typeof x)N=function(){x(C)};else if("undefined"!==typeof MessageChannel){var A=new MessageChannel,P=A.port2;A.port1.onmessage=C,N=function(){P.postMessage(null)}}else N=function(){v(C,0)};function O(e,n){S=v(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return m},t.unstable_next=function(e){switch(m){case 1:case 2:case 3:var t=3;break;default:t=m}var n=m;m=t;try{return e()}finally{m=n}},t.unstable_requestPaint=function(){y=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=m;m=e;try{return t()}finally{m=n}},t.unstable_scheduleCallback=function(e,a,s){var o=t.unstable_now();switch("object"===typeof s&&null!==s?s="number"===typeof(s=s.delay)&&0<s?o+s:o:s=o,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:s,expirationTime:i=s+i,sortIndex:-1},s>o?(e.sortIndex=s,n(u,e),null===r(c)&&e===r(u)&&(g?(b(S),S=-1):g=!0,O(k,s-o))):(e.sortIndex=i,n(c,e),h||p||(h=!0,j||(j=!0,N()))),e},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(e){var t=m;return function(){var n=m;m=t;try{return e.apply(this,arguments)}finally{m=n}}}},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,n),s.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+".bfd6bebf.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="idfc-agent-frontend:";n.l=(r,a,s,o)=>{if(e[r])e[r].push(a);else{var i,l;if(void 0!==s)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+s){i=d;break}}i||(l=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,n.nc&&i.setAttribute("nonce",n.nc),i.setAttribute("data-webpack",t+s),i.src=r),e[r]=[a];var f=(t,n)=>{i.onerror=i.onload=null,clearTimeout(m);var a=e[r];if(delete e[r],i.parentNode&&i.parentNode.removeChild(i),a&&a.forEach(e=>e(n)),t)return t(n)},m=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),l&&document.head.appendChild(i)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var s=new Promise((n,r)=>a=e[t]=[n,r]);r.push(a[2]=s);var o=n.p+n.u(t),i=new Error;n.l(o,r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var s=r&&("load"===r.type?"missing":r.type),o=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+s+": "+o+")",i.name="ChunkLoadError",i.type=s,i.request=o,a[1](i)}},"chunk-"+t,t)}};var t=(t,r)=>{var a,s,o=r[0],i=r[1],l=r[2],c=0;if(o.some(t=>0!==e[t])){for(a in i)n.o(i,a)&&(n.m[a]=i[a]);if(l)l(n)}for(t&&t(r);c<o.length;c++)s=o[c],n.o(e,s)&&e[s]&&e[s][0](),e[s]=0},r=self.webpackChunkidfc_agent_frontend=self.webpackChunkidfc_agent_frontend||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";var e={};n.r(e),n.d(e,{hasBrowserEnv:()=>gr,hasStandardBrowserEnv:()=>vr,hasStandardBrowserWebWorkerEnv:()=>br,navigator:()=>yr,origin:()=>xr});var t=n(43),r=n(391);function a(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function o(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}function i(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach(function(t){i(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}n(358);const u=["sri"],d=["page"],f=["page","matches"],m=["onClick","discover","prefetch","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],p=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],h=["discover","fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"];var g="popstate";function y(){return j(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return w("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:k(t)},null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function v(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function b(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function x(e,t){return{usr:e.state,key:e.key,idx:t}}function w(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return c(c({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?N(t):t),{},{state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)})}function k(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function N(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function j(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:a=document.defaultView,v5Compat:s=!1}=r,o=a.history,i="POP",l=null,u=d();function d(){return(o.state||{idx:null}).idx}function f(){i="POP";let e=d(),t=null==e?null:e-u;u=e,l&&l({action:i,location:p.location,delta:t})}function m(e){return S(e)}null==u&&(u=0,o.replaceState(c(c({},o.state),{},{idx:u}),""));let p={get action(){return i},get location(){return e(a,o)},listen(e){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(g,f),l=e,()=>{a.removeEventListener(g,f),l=null}},createHref:e=>t(a,e),createURL:m,encodeLocation(e){let t=m(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){i="PUSH";let r=w(p.location,e,t);n&&n(r,e),u=d()+1;let c=x(r,u),f=p.createHref(r);try{o.pushState(c,"",f)}catch(m){if(m instanceof DOMException&&"DataCloneError"===m.name)throw m;a.location.assign(f)}s&&l&&l({action:i,location:p.location,delta:1})},replace:function(e,t){i="REPLACE";let r=w(p.location,e,t);n&&n(r,e),u=d();let a=x(r,u),c=p.createHref(r);o.replaceState(a,"",c),s&&l&&l({action:i,location:p.location,delta:0})},go:e=>o.go(e)};return p}function S(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="http://localhost";"undefined"!==typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href),v(n,"No window.location.(origin|href) available to create URL");let r="string"===typeof e?e:k(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}new WeakMap;function _(e,t){return E(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function E(e,t,n,r){let a=B(("string"===typeof t?N(t):t).pathname||"/",n);if(null==a)return null;let s=T(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(s);let o=null;for(let i=0;null==o&&i<s.length;++i){let e=V(a);o=M(s[i],e,r)}return o}function T(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=(e,a,s)=>{let o={relativePath:void 0===s?e.path||"":s,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};o.relativePath.startsWith("/")&&(v(o.relativePath.startsWith(r),'Absolute route path "'.concat(o.relativePath,'" nested under path "').concat(r,'" is not valid. An absolute child route path must start with the combined path of all its parent routes.')),o.relativePath=o.relativePath.slice(r.length));let i=Q([r,o.relativePath]),l=n.concat(o);e.children&&e.children.length>0&&(v(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'.concat(i,'".')),T(e.children,t,l,i)),(null!=e.path||e.index)&&t.push({path:i,score:z(i,e.index),routesMeta:l})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!==(n=e.path)&&void 0!==n&&n.includes("?"))for(let r of C(e.path))a(e,t,r);else a(e,t)}),t}function C(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),s=n.replace(/\?$/,"");if(0===r.length)return a?[s,""]:[s];let o=C(r.join("/")),i=[];return i.push(...o.map(e=>""===e?s:[s,e].join("/"))),a&&i.push(...o),i.map(t=>e.startsWith("/")&&""===t?"/":t)}var A=/^:[\w-]+$/,P=3,O=2,R=1,F=10,L=-2,D=e=>"*"===e;function z(e,t){let n=e.split("/"),r=n.length;return n.some(D)&&(r+=L),t&&(r+=O),n.filter(e=>!D(e)).reduce((e,t)=>e+(A.test(t)?P:""===t?R:F),r)}function M(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,a={},s="/",o=[];for(let i=0;i<r.length;++i){let e=r[i],l=i===r.length-1,c="/"===s?t:t.slice(s.length)||"/",u=I({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},c),d=e.route;if(!u&&l&&n&&!r[r.length-1].route.index&&(u=I({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(a,u.params),o.push({params:a,pathname:Q([s,u.pathname]),pathnameBase:K(Q([s,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(s=Q([s,u.pathnameBase]))}return o}function I(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=U(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let s=a[0],o=s.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";o=s.slice(0,s.length-e.length).replace(/(.)\/+$/,"$1")}const l=i[n];return e[r]=a&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{}),pathname:s,pathnameBase:o,pattern:e}}function U(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];b("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'.concat(e,'" will be treated as if it were "').concat(e.replace(/\*$/,"/*"),'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "').concat(e.replace(/\*$/,"/*"),'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function V(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return b(!1,'The URL path "'.concat(e,'" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (').concat(t,").")),e}}function B(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function q(e,t,n,r){return"Cannot include a '".concat(e,"' character in a manually specified `to.").concat(t,"` field [").concat(JSON.stringify(r),"].  Please separate it out to the `to.").concat(n,'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.')}function $(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function H(e){let t=$(e);return t.map((e,n)=>n===t.length-1?e.pathname:e.pathnameBase)}function W(e,t,n){let r,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=N(e):(r=c({},e),v(!r.pathname||!r.pathname.includes("?"),q("?","pathname","search",r)),v(!r.pathname||!r.pathname.includes("#"),q("#","pathname","hash",r)),v(!r.search||!r.search.includes("#"),q("#","search","hash",r)));let s,o=""===e||""===r.pathname,i=o?"/":r.pathname;if(null==i)s=n;else{let e=t.length-1;if(!a&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}s=e>=0?t[e]:"/"}let l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:a=""}="string"===typeof e?N(e):e,s=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:s,search:Y(r),hash:X(a)}}(r,s),u=i&&"/"!==i&&i.endsWith("/"),d=(o||"."===i)&&n.endsWith("/");return l.pathname.endsWith("/")||!u&&!d||(l.pathname+="/"),l}var Q=e=>e.join("/").replace(/\/\/+/g,"/"),K=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Y=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",X=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function J(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var G=["POST","PUT","PATCH","DELETE"],Z=(new Set(G),["GET",...G]);new Set(Z),Symbol("ResetLoaderData");var ee=t.createContext(null);ee.displayName="DataRouter";var te=t.createContext(null);te.displayName="DataRouterState";var ne=t.createContext({isTransitioning:!1});ne.displayName="ViewTransition";var re=t.createContext(new Map);re.displayName="Fetchers";var ae=t.createContext(null);ae.displayName="Await";var se=t.createContext(null);se.displayName="Navigation";var oe=t.createContext(null);oe.displayName="Location";var ie=t.createContext({outlet:null,matches:[],isDataRoute:!1});ie.displayName="Route";var le=t.createContext(null);le.displayName="RouteError";function ce(){return null!=t.useContext(oe)}function ue(){return v(ce(),"useLocation() may be used only in the context of a <Router> component."),t.useContext(oe).location}var de="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function fe(e){t.useContext(se).static||t.useLayoutEffect(e)}function me(){let{isDataRoute:e}=t.useContext(ie);return e?function(){let{router:e}=je("useNavigate"),n=_e("useNavigate"),r=t.useRef(!1);fe(()=>{r.current=!0});let a=t.useCallback(async function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};b(r.current,de),r.current&&("number"===typeof t?e.navigate(t):await e.navigate(t,c({fromRouteId:n},a)))},[e,n]);return a}():function(){v(ce(),"useNavigate() may be used only in the context of a <Router> component.");let e=t.useContext(ee),{basename:n,navigator:r}=t.useContext(se),{matches:a}=t.useContext(ie),{pathname:s}=ue(),o=JSON.stringify(H(a)),i=t.useRef(!1);fe(()=>{i.current=!0});let l=t.useCallback(function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(b(i.current,de),!i.current)return;if("number"===typeof t)return void r.go(t);let l=W(t,JSON.parse(o),s,"path"===a.relative);null==e&&"/"!==n&&(l.pathname="/"===l.pathname?n:Q([n,l.pathname])),(a.replace?r.replace:r.push)(l,a.state,a)},[n,r,o,s,e]);return l}()}var pe=t.createContext(null);function he(){let{matches:e}=t.useContext(ie),n=e[e.length-1];return n?n.params:{}}function ge(e){let{relative:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:r}=t.useContext(ie),{pathname:a}=ue(),s=JSON.stringify(H(r));return t.useMemo(()=>W(e,JSON.parse(s),a,"path"===n),[e,s,a,n])}function ye(e,n,r,a){v(ce(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:s}=t.useContext(se),{matches:o}=t.useContext(ie),i=o[o.length-1],l=i?i.params:{},u=i?i.pathname:"/",d=i?i.pathnameBase:"/",f=i&&i.route;{let e=f&&f.path||"";Ce(u,!f||e.endsWith("*")||e.endsWith("*?"),'You rendered descendant <Routes> (or called `useRoutes()`) at "'.concat(u,'" (under <Route path="').concat(e,'">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won\'t match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="').concat(e,'"> to <Route path="').concat("/"===e?"*":"".concat(e,"/*"),'">.'))}let m,p=ue();if(n){var h;let e="string"===typeof n?N(n):n;v("/"===d||(null===(h=e.pathname)||void 0===h?void 0:h.startsWith(d)),'When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "'.concat(d,'" but pathname "').concat(e.pathname,'" was given in the `location` prop.')),m=e}else m=p;let g=m.pathname||"/",y=g;if("/"!==d){let e=d.replace(/^\//,"").split("/");y="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let x=_(e,{pathname:y});b(f||null!=x,'No routes matched location "'.concat(m.pathname).concat(m.search).concat(m.hash,'" ')),b(null==x||void 0!==x[x.length-1].route.element||void 0!==x[x.length-1].route.Component||void 0!==x[x.length-1].route.lazy,'Matched leaf route at location "'.concat(m.pathname).concat(m.search).concat(m.hash,'" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.'));let w=ke(x&&x.map(e=>Object.assign({},e,{params:Object.assign({},l,e.params),pathname:Q([d,s.encodeLocation?s.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:Q([d,s.encodeLocation?s.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),o,r,a);return n&&w?t.createElement(oe.Provider,{value:{location:c({pathname:"/",search:"",hash:"",state:null,key:"default"},m),navigationType:"POP"}},w):w}function ve(){let e=Ee(),n=J(e)?"".concat(e.status," ").concat(e.statusText):e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:a},o={padding:"2px 4px",backgroundColor:a},i=null;return console.error("Error handled by React Router default ErrorBoundary:",e),i=t.createElement(t.Fragment,null,t.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),t.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",t.createElement("code",{style:o},"ErrorBoundary")," or"," ",t.createElement("code",{style:o},"errorElement")," prop on your route.")),t.createElement(t.Fragment,null,t.createElement("h2",null,"Unexpected Application Error!"),t.createElement("h3",{style:{fontStyle:"italic"}},n),r?t.createElement("pre",{style:s},r):null,i)}var be=t.createElement(ve,null),xe=class extends t.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?t.createElement(ie.Provider,{value:this.props.routeContext},t.createElement(le.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function we(e){let{routeContext:n,match:r,children:a}=e,s=t.useContext(ee);return s&&s.static&&s.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=r.route.id),t.createElement(ie.Provider,{value:n},a)}function ke(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null==e){if(!r)return null;if(r.errors)e=r.matches;else{if(0!==n.length||r.initialized||!(r.matches.length>0))return null;e=r.matches}}let a=e,s=null===r||void 0===r?void 0:r.errors;if(null!=s){let e=a.findIndex(e=>e.route.id&&void 0!==(null===s||void 0===s?void 0:s[e.route.id]));v(e>=0,"Could not find a matching route for errors on route IDs: ".concat(Object.keys(s).join(","))),a=a.slice(0,Math.min(a.length,e+1))}let o=!1,i=-1;if(r)for(let t=0;t<a.length;t++){let e=a[t];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(i=t),e.route.id){let{loaderData:t,errors:n}=r,s=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!n||void 0===n[e.route.id]);if(e.route.lazy||s){o=!0,a=i>=0?a.slice(0,i+1):[a[0]];break}}}return a.reduceRight((e,l,c)=>{let u,d=!1,f=null,m=null;r&&(u=s&&l.route.id?s[l.route.id]:void 0,f=l.route.errorElement||be,o&&(i<0&&0===c?(Ce("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,m=null):i===c&&(d=!0,m=l.route.hydrateFallbackElement||null)));let p=n.concat(a.slice(0,c+1)),h=()=>{let n;return n=u?f:d?m:l.route.Component?t.createElement(l.route.Component,null):l.route.element?l.route.element:e,t.createElement(we,{match:l,routeContext:{outlet:e,matches:p,isDataRoute:null!=r},children:n})};return r&&(l.route.ErrorBoundary||l.route.errorElement||0===c)?t.createElement(xe,{location:r.location,revalidation:r.revalidation,component:f,error:u,children:h(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):h()},null)}function Ne(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function je(e){let n=t.useContext(ee);return v(n,Ne(e)),n}function Se(e){let n=t.useContext(te);return v(n,Ne(e)),n}function _e(e){let n=function(e){let n=t.useContext(ie);return v(n,Ne(e)),n}(e),r=n.matches[n.matches.length-1];return v(r.route.id,"".concat(e,' can only be used on routes that contain a unique "id"')),r.route.id}function Ee(){var e;let n=t.useContext(le),r=Se("useRouteError"),a=_e("useRouteError");return void 0!==n?n:null===(e=r.errors)||void 0===e?void 0:e[a]}var Te={};function Ce(e,t,n){t||Te[e]||(Te[e]=!0,b(!1,n))}t.memo(function(e){let{routes:t,future:n,state:r}=e;return ye(t,void 0,r,n)});function Ae(e){let{to:n,replace:r,state:a,relative:s}=e;v(ce(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=t.useContext(se);b(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:i}=t.useContext(ie),{pathname:l}=ue(),c=me(),u=W(n,H(i),l,"path"===s),d=JSON.stringify(u);return t.useEffect(()=>{c(JSON.parse(d),{replace:r,state:a,relative:s})},[c,d,s,r,a]),null}function Pe(e){return function(e){let n=t.useContext(ie).outlet;return n?t.createElement(pe.Provider,{value:e},n):n}(e.context)}function Oe(e){v(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Re(e){let{basename:n="/",children:r=null,location:a,navigationType:s="POP",navigator:o,static:i=!1}=e;v(!ce(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=n.replace(/^\/*/,"/"),c=t.useMemo(()=>({basename:l,navigator:o,static:i,future:{}}),[l,o,i]);"string"===typeof a&&(a=N(a));let{pathname:u="/",search:d="",hash:f="",state:m=null,key:p="default"}=a,h=t.useMemo(()=>{let e=B(u,l);return null==e?null:{location:{pathname:e,search:d,hash:f,state:m,key:p},navigationType:s}},[l,u,d,f,m,p,s]);return b(null!=h,'<Router basename="'.concat(l,'"> is not able to match the URL "').concat(u).concat(d).concat(f,"\" because it does not start with the basename, so the <Router> won't render anything.")),null==h?null:t.createElement(se.Provider,{value:c},t.createElement(oe.Provider,{children:r,value:h}))}function Fe(e){let{children:t,location:n}=e;return ye(Le(t),n)}t.Component;function Le(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=[];return t.Children.forEach(e,(e,a)=>{if(!t.isValidElement(e))return;let s=[...n,a];if(e.type===t.Fragment)return void r.push.apply(r,Le(e.props.children,s));v(e.type===Oe,"[".concat("string"===typeof e.type?e.type:e.type.name,"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>")),v(!e.props.index||!e.props.children,"An index route cannot have child routes.");let o={id:e.props.id||s.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(o.children=Le(e.props.children,s)),r.push(o)}),r}var De="get",ze="application/x-www-form-urlencoded";function Me(e){return null!=e&&"string"===typeof e.tagName}var Ie=null;var Ue=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ve(e){return null==e||Ue.has(e)?e:(b(!1,'"'.concat(e,'" is not a valid `encType` for `<Form>`/`<fetcher.Form>` and will default to "').concat(ze,'"')),null)}function Be(e,t){let n,r,a,s,o;if(function(e){return Me(e)&&"form"===e.tagName.toLowerCase()}(e)){let o=e.getAttribute("action");r=o?B(o,t):null,n=e.getAttribute("method")||De,a=Ve(e.getAttribute("enctype"))||ze,s=new FormData(e)}else if(function(e){return Me(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Me(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let o=e.form;if(null==o)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||o.getAttribute("action");if(r=i?B(i,t):null,n=e.getAttribute("formmethod")||o.getAttribute("method")||De,a=Ve(e.getAttribute("formenctype"))||Ve(o.getAttribute("enctype"))||ze,s=new FormData(o,e),!function(){if(null===Ie)try{new FormData(document.createElement("form"),0),Ie=!1}catch(e){Ie=!0}return Ie}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?"".concat(t,"."):"";s.append("".concat(e,"x"),"0"),s.append("".concat(e,"y"),"0")}else t&&s.append(t,r)}}else{if(Me(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=De,r=null,a=ze,o=e}return s&&"text/plain"===a&&(o=s,s=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:s,body:o}}function qe(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}async function $e(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error("Error loading route module `".concat(e.module,"`, reloading page...")),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function He(e){return null!=e&&"string"===typeof e.page}function We(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function Qe(e,t,n,r,a,s){let o=(e,t)=>!n[t]||e.route.id!==n[t].route.id,i=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null===(r=n[t].route.path)||void 0===r?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===s?t.filter((e,t)=>o(e,t)||i(e,t)):"data"===s?t.filter((t,s)=>{let l=r.routes[t.route.id];if(!l||!l.hasLoader)return!1;if(o(t,s)||i(t,s))return!0;if(t.route.shouldRevalidate){var c;let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:(null===(c=n[0])||void 0===c?void 0:c.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0}):[]}function Ke(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map(e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a}).flat(1),[...new Set(r)];var r}function Ye(e,t){let n=new Set,r=new Set(t);return e.reduce((e,a)=>{if(t&&!He(a)&&"script"===a.as&&a.href&&r.has(a.href))return e;let s=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(a));return n.has(s)||(n.add(s),e.push({key:s,link:a})),e},[])}function Xe(e){return{__html:e}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!==typeof window?window:"undefined"!==typeof globalThis&&globalThis;Symbol("SingleFetchRedirect");var Je=new Set([100,101,204,205]);function Ge(e,t){let n="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===B(n.pathname,t)?n.pathname="".concat(t.replace(/\/$/,""),"/_root.data"):n.pathname="".concat(n.pathname.replace(/\/$/,""),".data"),n}t.Component;function Ze(e){let{error:n,isOutsideRemixApp:r}=e;console.error(n);let a,s=t.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(J(n))return t.createElement(et,{title:"Unhandled Thrown Response!"},t.createElement("h1",{style:{fontSize:"24px"}},n.status," ",n.statusText),s);if(n instanceof Error)0;else{let e=null==n?"Unknown Error":"object"===typeof n&&"toString"in n?n.toString():JSON.stringify(n);new Error(e)}return t.createElement(et,{title:"Application Error!",isOutsideRemixApp:r},t.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),t.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},a.stack),s)}function et(e){var n;let{title:r,renderScripts:a,isOutsideRemixApp:s,children:o}=e,{routeModules:i}=st();return null!==i.root&&void 0!==n&&n.Layout&&!s?o:t.createElement("html",{lang:"en"},t.createElement("head",null,t.createElement("meta",{charSet:"utf-8"}),t.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),t.createElement("title",null,r)),t.createElement("body",null,t.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},o,a?t.createElement(ft,null):null)))}function tt(e,t){return"lazy"===e.mode&&!0===t}function nt(){let e=t.useContext(ee);return qe(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function rt(){let e=t.useContext(te);return qe(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var at=t.createContext(void 0);function st(){let e=t.useContext(at);return qe(e,"You must render this element inside a <HydratedRouter> element"),e}function ot(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function it(e,t,n){if(n&&!dt)return[e[0]];if(t){let n=e.findIndex(e=>void 0!==t[e.route.id]);return e.slice(0,n+1)}return e}function lt(e){let{page:n}=e,r=a(e,d),{router:s}=nt(),o=t.useMemo(()=>_(s.routes,n,s.basename),[s.routes,n,s.basename]);return o?t.createElement(ut,c({page:n,matches:o},r)):null}function ct(e){let{manifest:n,routeModules:r}=st(),[a,s]=t.useState([]);return t.useEffect(()=>{let t=!1;return async function(e,t,n){return Ye((await Promise.all(e.map(async e=>{let r=t.routes[e.route.id];if(r){let e=await $e(r,n);return e.links?e.links():[]}return[]}))).flat(1).filter(We).filter(e=>"stylesheet"===e.rel||"preload"===e.rel).map(e=>"stylesheet"===e.rel?c(c({},e),{},{rel:"prefetch",as:"style"}):c(c({},e),{},{rel:"prefetch"})))}(e,n,r).then(e=>{t||s(e)}),()=>{t=!0}},[e,n,r]),a}function ut(e){let{page:n,matches:r}=e,s=a(e,f),o=ue(),{manifest:i,routeModules:l}=st(),{basename:u}=nt(),{loaderData:d,matches:m}=rt(),p=t.useMemo(()=>Qe(n,r,m,i,o,"data"),[n,r,m,i,o]),h=t.useMemo(()=>Qe(n,r,m,i,o,"assets"),[n,r,m,i,o]),g=t.useMemo(()=>{if(n===o.pathname+o.search+o.hash)return[];let e=new Set,t=!1;if(r.forEach(n=>{var r;let a=i.routes[n.route.id];a&&a.hasLoader&&(!p.some(e=>e.route.id===n.route.id)&&n.route.id in d&&null!==(r=l[n.route.id])&&void 0!==r&&r.shouldRevalidate||a.hasClientLoader?t=!0:e.add(n.route.id))}),0===e.size)return[];let a=Ge(n,u);return t&&e.size>0&&a.searchParams.set("_routes",r.filter(t=>e.has(t.route.id)).map(e=>e.route.id).join(",")),[a.pathname+a.search]},[u,d,o,i,p,r,n,l]),y=t.useMemo(()=>Ke(h,i),[h,i]),v=ct(h);return t.createElement(t.Fragment,null,g.map(e=>t.createElement("link",c({key:e,rel:"prefetch",as:"fetch",href:e},s))),y.map(e=>t.createElement("link",c({key:e,rel:"modulepreload",href:e},s))),v.map(e=>{let{key:n,link:r}=e;return t.createElement("link",c({key:n},r))}))}at.displayName="FrameworkContext";var dt=!1;function ft(e){let{manifest:n,serverHandoffString:r,isSpaMode:s,renderMeta:o,routeDiscovery:i,ssr:l}=st(),{router:d,static:f,staticContext:m}=nt(),{matches:p}=rt(),h=tt(i,l);o&&(o.didRenderScripts=!0);let g=it(p,null,s);t.useEffect(()=>{0},[]);let y=t.useMemo(()=>{var s;let o=m?"window.__reactRouterContext = ".concat(r,";").concat("window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());"):" ",i=f?"".concat(null!==n.hmr&&void 0!==s&&s.runtime?"import ".concat(JSON.stringify(n.hmr.runtime),";"):"").concat(h?"":"import ".concat(JSON.stringify(n.url)),";\n").concat(g.map((e,t)=>{let r="route".concat(t),a=n.routes[e.route.id];qe(a,"Route ".concat(e.route.id," not found in manifest"));let{clientActionModule:s,clientLoaderModule:o,clientMiddlewareModule:i,hydrateFallbackModule:l,module:c}=a,u=[...s?[{module:s,varName:"".concat(r,"_clientAction")}]:[],...o?[{module:o,varName:"".concat(r,"_clientLoader")}]:[],...i?[{module:i,varName:"".concat(r,"_clientMiddleware")}]:[],...l?[{module:l,varName:"".concat(r,"_HydrateFallback")}]:[],{module:c,varName:"".concat(r,"_main")}];return 1===u.length?"import * as ".concat(r," from ").concat(JSON.stringify(c),";"):[u.map(e=>"import * as ".concat(e.varName,' from "').concat(e.module,'";')).join("\n"),"const ".concat(r," = {").concat(u.map(e=>"...".concat(e.varName)).join(","),"};")].join("\n")}).join("\n"),"\n  ").concat(h?"window.__reactRouterManifest = ".concat(JSON.stringify(function(e,t){let{sri:n}=e,r=a(e,u),s=new Set(t.state.matches.map(e=>e.route.id)),o=t.state.location.pathname.split("/").filter(Boolean),i=["/"];for(o.pop();o.length>0;)i.push("/".concat(o.join("/"))),o.pop();i.forEach(e=>{let n=_(t.routes,e,t.basename);n&&n.forEach(e=>s.add(e.route.id))});let l=[...s].reduce((e,t)=>Object.assign(e,{[t]:r.routes[t]}),{});return c(c({},r),{},{routes:l,sri:!!n||void 0})}(n,d),null,2),";"):"","\n  window.__reactRouterRouteModules = {").concat(g.map((e,t)=>"".concat(JSON.stringify(e.route.id),":route").concat(t)).join(","),"};\n\nimport(").concat(JSON.stringify(n.entry.module),");"):" ";return t.createElement(t.Fragment,null,t.createElement("script",c(c({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Xe(o),type:void 0})),t.createElement("script",c(c({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Xe(i),type:"module",async:!0})))},[]),v=dt?[]:function(e){return[...new Set(e)]}(n.entry.imports.concat(Ke(g,n,{includeHydrateFallback:!0}))),b="object"===typeof n.sri?n.sri:{};return dt?null:t.createElement(t.Fragment,null,"object"===typeof n.sri?t.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:b})}}):null,h?null:t.createElement("link",{rel:"modulepreload",href:n.url,crossOrigin:e.crossOrigin,integrity:b[n.url],suppressHydrationWarning:!0}),t.createElement("link",{rel:"modulepreload",href:n.entry.module,crossOrigin:e.crossOrigin,integrity:b[n.entry.module],suppressHydrationWarning:!0}),v.map(n=>t.createElement("link",{key:n,rel:"modulepreload",href:n,crossOrigin:e.crossOrigin,integrity:b[n],suppressHydrationWarning:!0})),y)}function mt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach(t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)})}}var pt="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{pt&&(window.__reactRouterVersion="7.6.3")}catch(rc){}function ht(e){let{basename:n,children:r,window:a}=e,s=t.useRef();null==s.current&&(s.current=y({window:a,v5Compat:!0}));let o=s.current,[i,l]=t.useState({action:o.action,location:o.location}),c=t.useCallback(e=>{t.startTransition(()=>l(e))},[l]);return t.useLayoutEffect(()=>o.listen(c),[o,c]),t.createElement(Re,{basename:n,children:r,location:i.location,navigationType:i.action,navigator:o})}var gt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,yt=t.forwardRef(function(e,n){let r,{onClick:s,discover:o="render",prefetch:i="none",relative:l,reloadDocument:u,replace:d,state:f,target:p,to:h,preventScrollReset:g,viewTransition:y}=e,x=a(e,m),{basename:w}=t.useContext(se),N="string"===typeof h&&gt.test(h),j=!1;if("string"===typeof h&&N&&(r=h,pt))try{let e=new URL(window.location.href),t=h.startsWith("//")?new URL(e.protocol+h):new URL(h),n=B(t.pathname,w);t.origin===e.origin&&null!=n?h=n+t.search+t.hash:j=!0}catch(rc){b(!1,'<Link to="'.concat(h,'"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.'))}let S=function(e){let{relative:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};v(ce(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:a}=t.useContext(se),{hash:s,pathname:o,search:i}=ge(e,{relative:n}),l=o;return"/"!==r&&(l="/"===o?r:Q([r,o])),a.createHref({pathname:l,search:i,hash:s})}(h,{relative:l}),[_,E,T]=function(e,n){let r=t.useContext(at),[a,s]=t.useState(!1),[o,i]=t.useState(!1),{onFocus:l,onBlur:c,onMouseEnter:u,onMouseLeave:d,onTouchStart:f}=n,m=t.useRef(null);t.useEffect(()=>{if("render"===e&&i(!0),"viewport"===e){let e=new IntersectionObserver(e=>{e.forEach(e=>{i(e.isIntersecting)})},{threshold:.5});return m.current&&e.observe(m.current),()=>{e.disconnect()}}},[e]),t.useEffect(()=>{if(a){let e=setTimeout(()=>{i(!0)},100);return()=>{clearTimeout(e)}}},[a]);let p=()=>{s(!0)},h=()=>{s(!1),i(!1)};return r?"intent"!==e?[o,m,{}]:[o,m,{onFocus:ot(l,p),onBlur:ot(c,h),onMouseEnter:ot(u,p),onMouseLeave:ot(d,h),onTouchStart:ot(f,p)}]:[!1,m,{}]}(i,x),C=function(e){let{target:n,replace:r,state:a,preventScrollReset:s,relative:o,viewTransition:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},l=me(),c=ue(),u=ge(e,{relative:o});return t.useCallback(t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:k(c)===k(u);l(e,{replace:n,state:a,preventScrollReset:s,relative:o,viewTransition:i})}},[c,l,u,r,a,n,e,s,o,i])}(h,{replace:d,state:f,target:p,preventScrollReset:g,relative:l,viewTransition:y});let A=t.createElement("a",c(c(c({},x),T),{},{href:r||S,onClick:j||u?s:function(e){s&&s(e),e.defaultPrevented||C(e)},ref:mt(n,E),target:p,"data-discover":N||"render"!==o?void 0:"true"}));return _&&!N?t.createElement(t.Fragment,null,A,t.createElement(lt,{page:S})):A});yt.displayName="Link",t.forwardRef(function(e,n){let{"aria-current":r="page",caseSensitive:s=!1,className:o="",end:i=!1,style:l,to:u,viewTransition:d,children:f}=e,m=a(e,p),h=ge(u,{relative:m.relative}),g=ue(),y=t.useContext(te),{navigator:b,basename:x}=t.useContext(se),w=null!=y&&function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.useContext(ne);v(null!=r,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=xt("useViewTransitionState"),s=ge(e,{relative:n.relative});if(!r.isTransitioning)return!1;let o=B(r.currentLocation.pathname,a)||r.currentLocation.pathname,i=B(r.nextLocation.pathname,a)||r.nextLocation.pathname;return null!=I(s.pathname,i)||null!=I(s.pathname,o)}(h)&&!0===d,k=b.encodeLocation?b.encodeLocation(h).pathname:h.pathname,N=g.pathname,j=y&&y.navigation&&y.navigation.location?y.navigation.location.pathname:null;s||(N=N.toLowerCase(),j=j?j.toLowerCase():null,k=k.toLowerCase()),j&&x&&(j=B(j,x)||j);const S="/"!==k&&k.endsWith("/")?k.length-1:k.length;let _,E=N===k||!i&&N.startsWith(k)&&"/"===N.charAt(S),T=null!=j&&(j===k||!i&&j.startsWith(k)&&"/"===j.charAt(k.length)),C={isActive:E,isPending:T,isTransitioning:w},A=E?r:void 0;_="function"===typeof o?o(C):[o,E?"active":null,T?"pending":null,w?"transitioning":null].filter(Boolean).join(" ");let P="function"===typeof l?l(C):l;return t.createElement(yt,c(c({},m),{},{"aria-current":A,className:_,ref:n,style:P,to:u,viewTransition:d}),"function"===typeof f?f(C):f)}).displayName="NavLink";var vt=t.forwardRef((e,n)=>{let{discover:r="render",fetcherKey:s,navigate:o,reloadDocument:i,replace:l,state:u,method:d=De,action:f,onSubmit:m,relative:p,preventScrollReset:g,viewTransition:y}=e,b=a(e,h),x=Nt(),w=function(e){let{relative:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:r}=t.useContext(se),a=t.useContext(ie);v(a,"useFormAction must be used inside a RouteContext");let[s]=a.matches.slice(-1),o=c({},ge(e||".",{relative:n})),i=ue();if(null==e){o.search=i.search;let e=new URLSearchParams(o.search),t=e.getAll("index");if(t.some(e=>""===e)){e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let n=e.toString();o.search=n?"?".concat(n):""}}e&&"."!==e||!s.route.index||(o.search=o.search?o.search.replace(/^\?/,"?index&"):"?index");"/"!==r&&(o.pathname="/"===o.pathname?r:Q([r,o.pathname]));return k(o)}(f,{relative:p}),N="get"===d.toLowerCase()?"get":"post",j="string"===typeof f&&gt.test(f);return t.createElement("form",c(c({ref:n,method:N,action:w,onSubmit:i?m:e=>{if(m&&m(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=(null===t||void 0===t?void 0:t.getAttribute("formmethod"))||d;x(t||e.currentTarget,{fetcherKey:s,method:n,navigate:o,replace:l,state:u,relative:p,preventScrollReset:g,viewTransition:y})}},b),{},{"data-discover":j||"render"!==r?void 0:"true"}))});function bt(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function xt(e){let n=t.useContext(ee);return v(n,bt(e)),n}vt.displayName="Form";var wt=0,kt=()=>"__".concat(String(++wt),"__");function Nt(){let{router:e}=xt("useSubmit"),{basename:n}=t.useContext(se),r=_e("useRouteId");return t.useCallback(async function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:s,method:o,encType:i,formData:l,body:c}=Be(t,n);if(!1===a.navigate){let t=a.fetcherKey||kt();await e.fetch(t,r,a.action||s,{preventScrollReset:a.preventScrollReset,formData:l,body:c,formMethod:a.method||o,formEncType:a.encType||i,flushSync:a.flushSync})}else await e.navigate(a.action||s,{preventScrollReset:a.preventScrollReset,formData:l,body:c,formMethod:a.method||o,formEncType:a.encType||i,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,n,r])}function jt(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=jt(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}const St=function(){for(var e,t,n=0,r="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=jt(e))&&(r&&(r+=" "),r+=t);return r},_t=["theme","type","isLoading"];!function(e){if(!e||"undefined"==typeof document)return;let t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",t.firstChild?t.insertBefore(n,t.firstChild):t.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}(':root{--toastify-color-light: #fff;--toastify-color-dark: #121212;--toastify-color-info: #3498db;--toastify-color-success: #07bc0c;--toastify-color-warning: #f1c40f;--toastify-color-error: hsl(6, 78%, 57%);--toastify-color-transparent: rgba(255, 255, 255, .7);--toastify-icon-color-info: var(--toastify-color-info);--toastify-icon-color-success: var(--toastify-color-success);--toastify-icon-color-warning: var(--toastify-color-warning);--toastify-icon-color-error: var(--toastify-color-error);--toastify-container-width: fit-content;--toastify-toast-width: 320px;--toastify-toast-offset: 16px;--toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));--toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));--toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));--toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));--toastify-toast-background: #fff;--toastify-toast-padding: 14px;--toastify-toast-min-height: 64px;--toastify-toast-max-height: 800px;--toastify-toast-bd-radius: 6px;--toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, .1);--toastify-font-family: sans-serif;--toastify-z-index: 9999;--toastify-text-color-light: #757575;--toastify-text-color-dark: #fff;--toastify-text-color-info: #fff;--toastify-text-color-success: #fff;--toastify-text-color-warning: #fff;--toastify-text-color-error: #fff;--toastify-spinner-color: #616161;--toastify-spinner-color-empty-area: #e0e0e0;--toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);--toastify-color-progress-dark: #bb86fc;--toastify-color-progress-info: var(--toastify-color-info);--toastify-color-progress-success: var(--toastify-color-success);--toastify-color-progress-warning: var(--toastify-color-warning);--toastify-color-progress-error: var(--toastify-color-error);--toastify-color-progress-bgo: .2}.Toastify__toast-container{z-index:var(--toastify-z-index);-webkit-transform:translate3d(0,0,var(--toastify-z-index));position:fixed;width:var(--toastify-container-width);box-sizing:border-box;color:#fff;display:flex;flex-direction:column}.Toastify__toast-container--top-left{top:var(--toastify-toast-top);left:var(--toastify-toast-left)}.Toastify__toast-container--top-center{top:var(--toastify-toast-top);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--top-right{top:var(--toastify-toast-top);right:var(--toastify-toast-right);align-items:end}.Toastify__toast-container--bottom-left{bottom:var(--toastify-toast-bottom);left:var(--toastify-toast-left)}.Toastify__toast-container--bottom-center{bottom:var(--toastify-toast-bottom);left:50%;transform:translate(-50%);align-items:center}.Toastify__toast-container--bottom-right{bottom:var(--toastify-toast-bottom);right:var(--toastify-toast-right);align-items:end}.Toastify__toast{--y: 0;position:relative;touch-action:none;width:var(--toastify-toast-width);min-height:var(--toastify-toast-min-height);box-sizing:border-box;margin-bottom:1rem;padding:var(--toastify-toast-padding);border-radius:var(--toastify-toast-bd-radius);box-shadow:var(--toastify-toast-shadow);max-height:var(--toastify-toast-max-height);font-family:var(--toastify-font-family);z-index:0;display:flex;flex:1 auto;align-items:center;word-break:break-word}@media only screen and (max-width: 480px){.Toastify__toast-container{width:100vw;left:env(safe-area-inset-left);margin:0}.Toastify__toast-container--top-left,.Toastify__toast-container--top-center,.Toastify__toast-container--top-right{top:env(safe-area-inset-top);transform:translate(0)}.Toastify__toast-container--bottom-left,.Toastify__toast-container--bottom-center,.Toastify__toast-container--bottom-right{bottom:env(safe-area-inset-bottom);transform:translate(0)}.Toastify__toast-container--rtl{right:env(safe-area-inset-right);left:initial}.Toastify__toast{--toastify-toast-width: 100%;margin-bottom:0;border-radius:0}}.Toastify__toast-container[data-stacked=true]{width:var(--toastify-toast-width)}.Toastify__toast--stacked{position:absolute;width:100%;transform:translate3d(0,var(--y),0) scale(var(--s));transition:transform .3s}.Toastify__toast--stacked[data-collapsed] .Toastify__toast-body,.Toastify__toast--stacked[data-collapsed] .Toastify__close-button{transition:opacity .1s}.Toastify__toast--stacked[data-collapsed=false]{overflow:visible}.Toastify__toast--stacked[data-collapsed=true]:not(:last-child)>*{opacity:0}.Toastify__toast--stacked:after{content:"";position:absolute;left:0;right:0;height:calc(var(--g) * 1px);bottom:100%}.Toastify__toast--stacked[data-pos=top]{top:0}.Toastify__toast--stacked[data-pos=bot]{bottom:0}.Toastify__toast--stacked[data-pos=bot].Toastify__toast--stacked:before{transform-origin:top}.Toastify__toast--stacked[data-pos=top].Toastify__toast--stacked:before{transform-origin:bottom}.Toastify__toast--stacked:before{content:"";position:absolute;left:0;right:0;bottom:0;height:100%;transform:scaleY(3);z-index:-1}.Toastify__toast--rtl{direction:rtl}.Toastify__toast--close-on-click{cursor:pointer}.Toastify__toast-icon{margin-inline-end:10px;width:22px;flex-shrink:0;display:flex}.Toastify--animate{animation-fill-mode:both;animation-duration:.5s}.Toastify--animate-icon{animation-fill-mode:both;animation-duration:.3s}.Toastify__toast-theme--dark{background:var(--toastify-color-dark);color:var(--toastify-text-color-dark)}.Toastify__toast-theme--light,.Toastify__toast-theme--colored.Toastify__toast--default{background:var(--toastify-color-light);color:var(--toastify-text-color-light)}.Toastify__toast-theme--colored.Toastify__toast--info{color:var(--toastify-text-color-info);background:var(--toastify-color-info)}.Toastify__toast-theme--colored.Toastify__toast--success{color:var(--toastify-text-color-success);background:var(--toastify-color-success)}.Toastify__toast-theme--colored.Toastify__toast--warning{color:var(--toastify-text-color-warning);background:var(--toastify-color-warning)}.Toastify__toast-theme--colored.Toastify__toast--error{color:var(--toastify-text-color-error);background:var(--toastify-color-error)}.Toastify__progress-bar-theme--light{background:var(--toastify-color-progress-light)}.Toastify__progress-bar-theme--dark{background:var(--toastify-color-progress-dark)}.Toastify__progress-bar--info{background:var(--toastify-color-progress-info)}.Toastify__progress-bar--success{background:var(--toastify-color-progress-success)}.Toastify__progress-bar--warning{background:var(--toastify-color-progress-warning)}.Toastify__progress-bar--error{background:var(--toastify-color-progress-error)}.Toastify__progress-bar-theme--colored.Toastify__progress-bar--info,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--success,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--warning,.Toastify__progress-bar-theme--colored.Toastify__progress-bar--error{background:var(--toastify-color-transparent)}.Toastify__close-button{color:#fff;position:absolute;top:6px;right:6px;background:transparent;outline:none;border:none;padding:0;cursor:pointer;opacity:.7;transition:.3s ease;z-index:1}.Toastify__toast--rtl .Toastify__close-button{left:6px;right:unset}.Toastify__close-button--light{color:#000;opacity:.3}.Toastify__close-button>svg{fill:currentColor;height:16px;width:14px}.Toastify__close-button:hover,.Toastify__close-button:focus{opacity:1}@keyframes Toastify__trackProgress{0%{transform:scaleX(1)}to{transform:scaleX(0)}}.Toastify__progress-bar{position:absolute;bottom:0;left:0;width:100%;height:100%;z-index:1;opacity:.7;transform-origin:left}.Toastify__progress-bar--animated{animation:Toastify__trackProgress linear 1 forwards}.Toastify__progress-bar--controlled{transition:transform .2s}.Toastify__progress-bar--rtl{right:0;left:initial;transform-origin:right;border-bottom-left-radius:initial}.Toastify__progress-bar--wrp{position:absolute;overflow:hidden;bottom:0;left:0;width:100%;height:5px;border-bottom-left-radius:var(--toastify-toast-bd-radius);border-bottom-right-radius:var(--toastify-toast-bd-radius)}.Toastify__progress-bar--wrp[data-hidden=true]{opacity:0}.Toastify__progress-bar--bg{opacity:var(--toastify-color-progress-bgo);width:100%;height:100%}.Toastify__spinner{width:20px;height:20px;box-sizing:border-box;border:2px solid;border-radius:100%;border-color:var(--toastify-spinner-color-empty-area);border-right-color:var(--toastify-spinner-color);animation:Toastify__spin .65s linear infinite}@keyframes Toastify__bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0)}60%{opacity:1;transform:translate3d(-25px,0,0)}75%{transform:translate3d(10px,0,0)}90%{transform:translate3d(-5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutRight{20%{opacity:1;transform:translate3d(-20px,var(--y),0)}to{opacity:0;transform:translate3d(2000px,var(--y),0)}}@keyframes Toastify__bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0)}60%{opacity:1;transform:translate3d(25px,0,0)}75%{transform:translate3d(-10px,0,0)}90%{transform:translate3d(5px,0,0)}to{transform:none}}@keyframes Toastify__bounceOutLeft{20%{opacity:1;transform:translate3d(20px,var(--y),0)}to{opacity:0;transform:translate3d(-2000px,var(--y),0)}}@keyframes Toastify__bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0)}60%{opacity:1;transform:translate3d(0,-20px,0)}75%{transform:translate3d(0,10px,0)}90%{transform:translate3d(0,-5px,0)}to{transform:translateZ(0)}}@keyframes Toastify__bounceOutUp{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,-2000px,0)}}@keyframes Toastify__bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0)}60%{opacity:1;transform:translate3d(0,25px,0)}75%{transform:translate3d(0,-10px,0)}90%{transform:translate3d(0,5px,0)}to{transform:none}}@keyframes Toastify__bounceOutDown{20%{transform:translate3d(0,calc(var(--y) - 10px),0)}40%,45%{opacity:1;transform:translate3d(0,calc(var(--y) + 20px),0)}to{opacity:0;transform:translate3d(0,2000px,0)}}.Toastify__bounce-enter--top-left,.Toastify__bounce-enter--bottom-left{animation-name:Toastify__bounceInLeft}.Toastify__bounce-enter--top-right,.Toastify__bounce-enter--bottom-right{animation-name:Toastify__bounceInRight}.Toastify__bounce-enter--top-center{animation-name:Toastify__bounceInDown}.Toastify__bounce-enter--bottom-center{animation-name:Toastify__bounceInUp}.Toastify__bounce-exit--top-left,.Toastify__bounce-exit--bottom-left{animation-name:Toastify__bounceOutLeft}.Toastify__bounce-exit--top-right,.Toastify__bounce-exit--bottom-right{animation-name:Toastify__bounceOutRight}.Toastify__bounce-exit--top-center{animation-name:Toastify__bounceOutUp}.Toastify__bounce-exit--bottom-center{animation-name:Toastify__bounceOutDown}@keyframes Toastify__zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}@keyframes Toastify__zoomOut{0%{opacity:1}50%{opacity:0;transform:translate3d(0,var(--y),0) scale3d(.3,.3,.3)}to{opacity:0}}.Toastify__zoom-enter{animation-name:Toastify__zoomIn}.Toastify__zoom-exit{animation-name:Toastify__zoomOut}@keyframes Toastify__flipIn{0%{transform:perspective(400px) rotateX(90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotateX(-20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotateX(10deg);opacity:1}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}@keyframes Toastify__flipOut{0%{transform:translate3d(0,var(--y),0) perspective(400px)}30%{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(-20deg);opacity:1}to{transform:translate3d(0,var(--y),0) perspective(400px) rotateX(90deg);opacity:0}}.Toastify__flip-enter{animation-name:Toastify__flipIn}.Toastify__flip-exit{animation-name:Toastify__flipOut}@keyframes Toastify__slideInRight{0%{transform:translate3d(110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInLeft{0%{transform:translate3d(-110%,0,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInUp{0%{transform:translate3d(0,110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideInDown{0%{transform:translate3d(0,-110%,0);visibility:visible}to{transform:translate3d(0,var(--y),0)}}@keyframes Toastify__slideOutRight{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(110%,var(--y),0)}}@keyframes Toastify__slideOutLeft{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(-110%,var(--y),0)}}@keyframes Toastify__slideOutDown{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,500px,0)}}@keyframes Toastify__slideOutUp{0%{transform:translate3d(0,var(--y),0)}to{visibility:hidden;transform:translate3d(0,-500px,0)}}.Toastify__slide-enter--top-left,.Toastify__slide-enter--bottom-left{animation-name:Toastify__slideInLeft}.Toastify__slide-enter--top-right,.Toastify__slide-enter--bottom-right{animation-name:Toastify__slideInRight}.Toastify__slide-enter--top-center{animation-name:Toastify__slideInDown}.Toastify__slide-enter--bottom-center{animation-name:Toastify__slideInUp}.Toastify__slide-exit--top-left,.Toastify__slide-exit--bottom-left{animation-name:Toastify__slideOutLeft;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-right,.Toastify__slide-exit--bottom-right{animation-name:Toastify__slideOutRight;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--top-center{animation-name:Toastify__slideOutUp;animation-timing-function:ease-in;animation-duration:.3s}.Toastify__slide-exit--bottom-center{animation-name:Toastify__slideOutDown;animation-timing-function:ease-in;animation-duration:.3s}@keyframes Toastify__spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}\n');var Et=e=>"number"==typeof e&&!isNaN(e),Tt=e=>"string"==typeof e,Ct=e=>"function"==typeof e,At=e=>Tt(e)||Ct(e)?e:null,Pt=(e,t)=>!1===e||Et(e)&&e>0?e:t,Ot=e=>(0,t.isValidElement)(e)||Tt(e)||Ct(e)||Et(e);function Rt(e){let{enter:n,exit:r,appendPosition:a=!1,collapse:s=!0,collapseDuration:o=300}=e;return function(e){let{children:i,position:l,preventExitTransition:c,done:u,nodeRef:d,isIn:f,playToast:m}=e,p=a?"".concat(n,"--").concat(l):n,h=a?"".concat(r,"--").concat(l):r,g=(0,t.useRef)(0);return(0,t.useLayoutEffect)(()=>{let e=d.current,t=p.split(" "),n=r=>{r.target===d.current&&(m(),e.removeEventListener("animationend",n),e.removeEventListener("animationcancel",n),0===g.current&&"animationcancel"!==r.type&&e.classList.remove(...t))};e.classList.add(...t),e.addEventListener("animationend",n),e.addEventListener("animationcancel",n)},[]),(0,t.useEffect)(()=>{let e=d.current,t=()=>{e.removeEventListener("animationend",t),s?function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:300,{scrollHeight:r,style:a}=e;requestAnimationFrame(()=>{a.minHeight="initial",a.height=r+"px",a.transition="all ".concat(n,"ms"),requestAnimationFrame(()=>{a.height="0",a.padding="0",a.margin="0",setTimeout(t,n)})})}(e,u,o):u()};f||(c?t():(g.current=1,e.className+=" ".concat(h),e.addEventListener("animationend",t)))},[f]),t.createElement(t.Fragment,null,i)}}function Ft(e,t){return{content:Lt(e.content,e.props),containerId:e.props.containerId,id:e.props.toastId,theme:e.props.theme,type:e.props.type,data:e.props.data||{},isLoading:e.props.isLoading,icon:e.props.icon,reason:e.removalReason,status:t}}function Lt(e,n){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return(0,t.isValidElement)(e)&&!Tt(e.type)?(0,t.cloneElement)(e,{closeToast:n.closeToast,toastProps:n,data:n.data,isPaused:r}):Ct(e)?e({closeToast:n.closeToast,toastProps:n,data:n.data,isPaused:r}):e}function Dt(e){let{delay:n,isRunning:r,closeToast:a,type:s="default",hide:o,className:i,controlledProgress:l,progress:u,rtl:d,isIn:f,theme:m}=e,p=o||l&&0===u,h={animationDuration:"".concat(n,"ms"),animationPlayState:r?"running":"paused"};l&&(h.transform="scaleX(".concat(u,")"));let g=St("Toastify__progress-bar",l?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated","Toastify__progress-bar-theme--".concat(m),"Toastify__progress-bar--".concat(s),{"Toastify__progress-bar--rtl":d}),y=Ct(i)?i({rtl:d,type:s,defaultClassName:g}):St(g,i),v={[l&&u>=1?"onTransitionEnd":"onAnimationEnd"]:l&&u<1?null:()=>{f&&a()}};return t.createElement("div",{className:"Toastify__progress-bar--wrp","data-hidden":p},t.createElement("div",{className:"Toastify__progress-bar--bg Toastify__progress-bar-theme--".concat(m," Toastify__progress-bar--").concat(s)}),t.createElement("div",c({role:"progressbar","aria-hidden":p?"true":"false","aria-label":"notification timer",className:y,style:h},v)))}var zt=1,Mt=()=>"".concat(zt++);function It(e,t,n){let r=1,a=0,s=[],o=[],i=t,l=new Map,u=new Set,d=()=>{o=Array.from(l.values()),u.forEach(e=>e())},f=e=>{var t,n;null==(n=null==(t=e.props)?void 0:t.onClose)||n.call(t,e.removalReason),e.isActive=!1},m=e=>{if(null==e)l.forEach(f);else{let t=l.get(e);t&&f(t)}d()},p=e=>{var t,r;let{toastId:a,updateId:s}=e.props,o=null==s;e.staleId&&l.delete(e.staleId),e.isActive=!0,l.set(a,e),d(),n(Ft(e,o?"added":"updated")),o&&(null==(r=(t=e.props).onOpen)||r.call(t))};return{id:e,props:i,observe:e=>(u.add(e),()=>u.delete(e)),toggle:(e,t)=>{l.forEach(n=>{var r;(null==t||t===n.props.toastId)&&(null==(r=n.toggle)||r.call(n,e))})},removeToast:m,toasts:l,clearQueue:()=>{a-=s.length,s=[]},buildToast:(t,o)=>{if((t=>{let{containerId:n,toastId:r,updateId:a}=t,s=n?n!==e:1!==e,o=l.has(r)&&null==a;return s||o})(o))return;let{toastId:u,updateId:f,data:h,staleId:g,delay:y}=o,v=null==f;v&&a++;let b=c(c(c({},i),{},{style:i.toastStyle,key:r++},Object.fromEntries(Object.entries(o).filter(e=>{let[t,n]=e;return null!=n}))),{},{toastId:u,updateId:f,data:h,isIn:!1,className:At(o.className||i.toastClassName),progressClassName:At(o.progressClassName||i.progressClassName),autoClose:!o.isLoading&&Pt(o.autoClose,i.autoClose),closeToast(e){l.get(u).removalReason=e,m(u)},deleteToast(){let e=l.get(u);if(null!=e){if(n(Ft(e,"removed")),l.delete(u),a--,a<0&&(a=0),s.length>0)return void p(s.shift());d()}}});b.closeButton=i.closeButton,!1===o.closeButton||Ot(o.closeButton)?b.closeButton=o.closeButton:!0===o.closeButton&&(b.closeButton=!Ot(i.closeButton)||i.closeButton);let x={content:t,props:b,staleId:g};i.limit&&i.limit>0&&a>i.limit&&v?s.push(x):Et(y)?setTimeout(()=>{p(x)},y):p(x)},setProps(e){i=e},setToggle:(e,t)=>{let n=l.get(e);n&&(n.toggle=t)},isToastActive:e=>{var t;return null==(t=l.get(e))?void 0:t.isActive},getSnapshot:()=>o}}var Ut=new Map,Vt=[],Bt=new Set,qt=e=>Bt.forEach(t=>t(e)),$t=()=>Ut.size>0;function Ht(e,t){var n;if(t)return!(null==(n=Ut.get(t))||!n.isToastActive(e));let r=!1;return Ut.forEach(t=>{t.isToastActive(e)&&(r=!0)}),r}function Wt(e){if($t()){if(null==e||(e=>Tt(e)||Et(e))(e))Ut.forEach(t=>{t.removeToast(e)});else if(e&&("containerId"in e||"id"in e)){let t=Ut.get(e.containerId);t?t.removeToast(e.id):Ut.forEach(t=>{t.removeToast(e.id)})}}else Vt=Vt.filter(t=>null!=e&&t.options.toastId!==e)}function Qt(e,t){Ot(e)&&($t()||Vt.push({content:e,options:t}),Ut.forEach(n=>{n.buildToast(e,t)}))}function Kt(e,t){Ut.forEach(n=>{(null==t||null==t||!t.containerId||(null==t?void 0:t.containerId)===n.id)&&n.toggle(e,null==t?void 0:t.id)})}function Yt(e){let t=e.containerId||1;return{subscribe(n){let r=It(t,e,qt);Ut.set(t,r);let a=r.observe(n);return Vt.forEach(e=>Qt(e.content,e.options)),Vt=[],()=>{a(),Ut.delete(t)}},setProps(e){var n;null==(n=Ut.get(t))||n.setProps(e)},getSnapshot(){var e;return null==(e=Ut.get(t))?void 0:e.getSnapshot()}}}function Xt(e){return e&&(Tt(e.toastId)||Et(e.toastId))?e.toastId:Mt()}function Jt(e,t){return Qt(e,t),t.toastId}function Gt(e,t){return c(c({},t),{},{type:t&&t.type||e,toastId:Xt(t)})}function Zt(e){return(t,n)=>Jt(t,Gt(e,n))}function en(e,t){return Jt(e,Gt("default",t))}function tn(e){let[n,r]=(0,t.useState)(!1),[a,s]=(0,t.useState)(!1),o=(0,t.useRef)(null),i=(0,t.useRef)({start:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,didMove:!1}).current,{autoClose:l,pauseOnHover:c,closeToast:u,onClick:d,closeOnClick:f}=e;function m(){r(!0)}function p(){r(!1)}function h(t){let r=o.current;if(i.canDrag&&r){i.didMove=!0,n&&p(),"x"===e.draggableDirection?i.delta=t.clientX-i.start:i.delta=t.clientY-i.start,i.start!==t.clientX&&(i.canCloseOnClick=!1);let a="x"===e.draggableDirection?"".concat(i.delta,"px, var(--y)"):"0, calc(".concat(i.delta,"px + var(--y))");r.style.transform="translate3d(".concat(a,",0)"),r.style.opacity="".concat(1-Math.abs(i.delta/i.removalDistance))}}function g(){document.removeEventListener("pointermove",h),document.removeEventListener("pointerup",g);let t=o.current;if(i.canDrag&&i.didMove&&t){if(i.canDrag=!1,Math.abs(i.delta)>i.removalDistance)return s(!0),e.closeToast(!0),void e.collapseAll();t.style.transition="transform 0.2s, opacity 0.2s",t.style.removeProperty("transform"),t.style.removeProperty("opacity")}}(function(e){var t;null==(t=Ut.get(e.containerId||1))||t.setToggle(e.id,e.fn)})({id:e.toastId,containerId:e.containerId,fn:r}),(0,t.useEffect)(()=>{if(e.pauseOnFocusLoss)return document.hasFocus()||p(),window.addEventListener("focus",m),window.addEventListener("blur",p),()=>{window.removeEventListener("focus",m),window.removeEventListener("blur",p)}},[e.pauseOnFocusLoss]);let y={onPointerDown:function(t){if(!0===e.draggable||e.draggable===t.pointerType){i.didMove=!1,document.addEventListener("pointermove",h),document.addEventListener("pointerup",g);let n=o.current;i.canCloseOnClick=!0,i.canDrag=!0,n.style.transition="none","x"===e.draggableDirection?(i.start=t.clientX,i.removalDistance=n.offsetWidth*(e.draggablePercent/100)):(i.start=t.clientY,i.removalDistance=n.offsetHeight*(80===e.draggablePercent?1.5*e.draggablePercent:e.draggablePercent)/100)}},onPointerUp:function(t){let{top:n,bottom:r,left:a,right:s}=o.current.getBoundingClientRect();"touchend"!==t.nativeEvent.type&&e.pauseOnHover&&t.clientX>=a&&t.clientX<=s&&t.clientY>=n&&t.clientY<=r?p():m()}};return l&&c&&(y.onMouseEnter=p,e.stacked||(y.onMouseLeave=m)),f&&(y.onClick=e=>{d&&d(e),i.canCloseOnClick&&u(!0)}),{playToast:m,pauseToast:p,isRunning:n,preventExitTransition:a,toastRef:o,eventHandlers:y}}en.loading=(e,t)=>Jt(e,Gt("default",c({isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1},t))),en.promise=function(e,t,n){let r,{pending:a,error:s,success:o}=t;a&&(r=Tt(a)?en.loading(a,n):en.loading(a.render,c(c({},n),a)));let i={isLoading:null,autoClose:null,closeOnClick:null,closeButton:null,draggable:null},l=(e,t,a)=>{if(null==t)return void en.dismiss(r);let s=c(c(c({type:e},i),n),{},{data:a}),o=Tt(t)?{render:t}:t;return r?en.update(r,c(c({},s),o)):en(o.render,c(c({},s),o)),a},u=Ct(e)?e():e;return u.then(e=>l("success",o,e)).catch(e=>l("error",s,e)),u},en.success=Zt("success"),en.info=Zt("info"),en.error=Zt("error"),en.warning=Zt("warning"),en.warn=en.warning,en.dark=(e,t)=>Jt(e,Gt("default",c({theme:"dark"},t))),en.dismiss=function(e){Wt(e)},en.clearWaitingQueue=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Ut.forEach(t=>{t.props.limit&&(!e.containerId||t.id===e.containerId)&&t.clearQueue()})},en.isActive=Ht,en.update=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=((e,t)=>{let{containerId:n}=t;var r;return null==(r=Ut.get(n||1))?void 0:r.toasts.get(e)})(e,t);if(n){let{props:r,content:a}=n,s=c(c(c({delay:100},r),t),{},{toastId:t.toastId||e,updateId:Mt()});s.toastId!==e&&(s.staleId=e);let o=s.render||a;delete s.render,Jt(o,s)}},en.done=e=>{en.update(e,{progress:1})},en.onChange=function(e){return Bt.add(e),()=>{Bt.delete(e)}},en.play=e=>Kt(!0,e),en.pause=e=>Kt(!1,e);var nn="undefined"!=typeof window?t.useLayoutEffect:t.useEffect,rn=e=>{let{theme:n,type:r,isLoading:s}=e,o=a(e,_t);return t.createElement("svg",c({viewBox:"0 0 24 24",width:"100%",height:"100%",fill:"colored"===n?"currentColor":"var(--toastify-icon-color-".concat(r,")")},o))};var an={info:function(e){return t.createElement(rn,c({},e),t.createElement("path",{d:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}))},warning:function(e){return t.createElement(rn,c({},e),t.createElement("path",{d:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}))},success:function(e){return t.createElement(rn,c({},e),t.createElement("path",{d:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}))},error:function(e){return t.createElement(rn,c({},e),t.createElement("path",{d:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}))},spinner:function(){return t.createElement("div",{className:"Toastify__spinner"})}};function sn(e){let{theme:n,type:r,isLoading:a,icon:s}=e,o=null,i={theme:n,type:r};return!1===s||(Ct(s)?o=s(c(c({},i),{},{isLoading:a})):(0,t.isValidElement)(s)?o=(0,t.cloneElement)(s,i):a?o=an.spinner():(e=>e in an)(r)&&(o=an[r](i))),o}var on=e=>{let{isRunning:n,preventExitTransition:r,toastRef:a,eventHandlers:s,playToast:o}=tn(e),{closeButton:i,children:l,autoClose:u,onClick:d,type:f,hideProgressBar:m,closeToast:p,transition:h,position:g,className:y,style:v,progressClassName:b,updateId:x,role:w,progress:k,rtl:N,toastId:j,deleteToast:S,isIn:_,isLoading:E,closeOnClick:T,theme:C,ariaLabel:A}=e,P=St("Toastify__toast","Toastify__toast-theme--".concat(C),"Toastify__toast--".concat(f),{"Toastify__toast--rtl":N},{"Toastify__toast--close-on-click":T}),O=Ct(y)?y({rtl:N,position:g,type:f,defaultClassName:P}):St(P,y),R=sn(e),F=!!k||!u,L={closeToast:p,type:f,theme:C},D=null;return!1===i||(D=Ct(i)?i(L):(0,t.isValidElement)(i)?(0,t.cloneElement)(i,L):function(e){let{closeToast:n,theme:r,ariaLabel:a="close"}=e;return t.createElement("button",{className:"Toastify__close-button Toastify__close-button--".concat(r),type:"button",onClick:e=>{e.stopPropagation(),n(!0)},"aria-label":a},t.createElement("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},t.createElement("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}(L)),t.createElement(h,{isIn:_,done:S,position:g,preventExitTransition:r,nodeRef:a,playToast:o},t.createElement("div",c(c({id:j,tabIndex:0,onClick:d,"data-in":_,className:O},s),{},{style:v,ref:a},_&&{role:w,"aria-label":A}),null!=R&&t.createElement("div",{className:St("Toastify__toast-icon",{"Toastify--animate-icon Toastify__zoom-enter":!E})},R),Lt(l,e,!n),D,!e.customProgressBar&&t.createElement(Dt,c(c({},x&&!F?{key:"p-".concat(x)}:{}),{},{rtl:N,theme:C,delay:u,isRunning:n,isIn:_,closeToast:p,hide:m,type:f,className:b,controlledProgress:F,progress:k||0}))))},ln=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return{enter:"Toastify--animate Toastify__".concat(e,"-enter"),exit:"Toastify--animate Toastify__".concat(e,"-exit"),appendPosition:t}},cn=Rt(ln("bounce",!0)),un=(Rt(ln("slide",!0)),Rt(ln("zoom")),Rt(ln("flip")),{position:"top-right",transition:cn,autoClose:5e3,closeButton:!0,pauseOnHover:!0,pauseOnFocusLoss:!0,draggable:"touch",draggablePercent:80,draggableDirection:"x",role:"alert",theme:"light","aria-label":"Notifications Alt+T",hotKeys:e=>e.altKey&&"KeyT"===e.code});function dn(e){let n=c(c({},un),e),r=e.stacked,[a,s]=(0,t.useState)(!0),o=(0,t.useRef)(null),{getToastToRender:i,isToastActive:l,count:u}=function(e){var n;let{subscribe:r,getSnapshot:a,setProps:s}=(0,t.useRef)(Yt(e)).current;s(e);let o=null==(n=(0,t.useSyncExternalStore)(r,a,a))?void 0:n.slice();return{getToastToRender:function(t){if(!o)return[];let n=new Map;return e.newestOnTop&&o.reverse(),o.forEach(e=>{let{position:t}=e.props;n.has(t)||n.set(t,[]),n.get(t).push(e)}),Array.from(n,e=>t(e[0],e[1]))},isToastActive:Ht,count:null==o?void 0:o.length}}(n),{className:d,style:f,rtl:m,containerId:p,hotKeys:h}=n;function g(e){let t=St("Toastify__toast-container","Toastify__toast-container--".concat(e),{"Toastify__toast-container--rtl":m});return Ct(d)?d({position:e,rtl:m,defaultClassName:t}):St(t,At(d))}function y(){r&&(s(!0),en.play())}return nn(()=>{var e;if(r){let t=o.current.querySelectorAll('[data-in="true"]'),r=12,s=null==(e=n.position)?void 0:e.includes("top"),i=0,l=0;Array.from(t).reverse().forEach((e,t)=>{let n=e;n.classList.add("Toastify__toast--stacked"),t>0&&(n.dataset.collapsed="".concat(a)),n.dataset.pos||(n.dataset.pos=s?"top":"bot");let o=i*(a?.2:1)+(a?0:r*t);n.style.setProperty("--y","".concat(s?o:-1*o,"px")),n.style.setProperty("--g","".concat(r)),n.style.setProperty("--s","".concat(1-(a?l:0))),i+=n.offsetHeight,l+=.025})}},[a,u,r]),(0,t.useEffect)(()=>{function e(e){var t;let n=o.current;h(e)&&(null==(t=n.querySelector('[tabIndex="0"]'))||t.focus(),s(!1),en.pause()),"Escape"===e.key&&(document.activeElement===n||null!=n&&n.contains(document.activeElement))&&(s(!0),en.play())}return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[h]),t.createElement("section",{ref:o,className:"Toastify",id:p,onMouseEnter:()=>{r&&(s(!1),en.pause())},onMouseLeave:y,"aria-live":"polite","aria-atomic":"false","aria-relevant":"additions text","aria-label":n["aria-label"]},i((e,n)=>{let a=n.length?c({},f):c(c({},f),{},{pointerEvents:"none"});return t.createElement("div",{tabIndex:-1,className:g(e),"data-stacked":r,style:a,key:"c-".concat(e)},n.map(e=>{let{content:n,props:a}=e;return t.createElement(on,c(c({},a),{},{stacked:r,collapseAll:y,isIn:l(a.toastId,a.containerId),key:"t-".concat(a.key)}),n)}))}))}function fn(e,t){return function(){return e.apply(t,arguments)}}const{toString:mn}=Object.prototype,{getPrototypeOf:pn}=Object,{iterator:hn,toStringTag:gn}=Symbol,yn=(vn=Object.create(null),e=>{const t=mn.call(e);return vn[t]||(vn[t]=t.slice(8,-1).toLowerCase())});var vn;const bn=e=>(e=e.toLowerCase(),t=>yn(t)===e),xn=e=>t=>typeof t===e,{isArray:wn}=Array,kn=xn("undefined");const Nn=bn("ArrayBuffer");const jn=xn("string"),Sn=xn("function"),_n=xn("number"),En=e=>null!==e&&"object"===typeof e,Tn=e=>{if("object"!==yn(e))return!1;const t=pn(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(gn in e)&&!(hn in e)},Cn=bn("Date"),An=bn("File"),Pn=bn("Blob"),On=bn("FileList"),Rn=bn("URLSearchParams"),[Fn,Ln,Dn,zn]=["ReadableStream","Request","Response","Headers"].map(bn);function Mn(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),wn(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),s=r.length;let o;for(n=0;n<s;n++)o=r[n],t.call(null,e[o],o,e)}}function In(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const Un="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,Vn=e=>!kn(e)&&e!==Un;const Bn=(qn="undefined"!==typeof Uint8Array&&pn(Uint8Array),e=>qn&&e instanceof qn);var qn;const $n=bn("HTMLFormElement"),Hn=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),Wn=bn("RegExp"),Qn=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Mn(n,(n,a)=>{let s;!1!==(s=t(n,a,e))&&(r[a]=s||n)}),Object.defineProperties(e,r)};const Kn=bn("AsyncFunction"),Yn=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],Un.addEventListener("message",e=>{let{source:t,data:a}=e;t===Un&&a===n&&r.length&&r.shift()()},!1),e=>{r.push(e),Un.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,Sn(Un.postMessage)),Xn="undefined"!==typeof queueMicrotask?queueMicrotask.bind(Un):"undefined"!==typeof process&&process.nextTick||Yn,Jn={isArray:wn,isArrayBuffer:Nn,isBuffer:function(e){return null!==e&&!kn(e)&&null!==e.constructor&&!kn(e.constructor)&&Sn(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||Sn(e.append)&&("formdata"===(t=yn(e))||"object"===t&&Sn(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Nn(e.buffer),t},isString:jn,isNumber:_n,isBoolean:e=>!0===e||!1===e,isObject:En,isPlainObject:Tn,isReadableStream:Fn,isRequest:Ln,isResponse:Dn,isHeaders:zn,isUndefined:kn,isDate:Cn,isFile:An,isBlob:Pn,isRegExp:Wn,isFunction:Sn,isStream:e=>En(e)&&Sn(e.pipe),isURLSearchParams:Rn,isTypedArray:Bn,isFileList:On,forEach:Mn,merge:function e(){const{caseless:t}=Vn(this)&&this||{},n={},r=(r,a)=>{const s=t&&In(n,a)||a;Tn(n[s])&&Tn(r)?n[s]=e(n[s],r):Tn(r)?n[s]=e({},r):wn(r)?n[s]=r.slice():n[s]=r};for(let a=0,s=arguments.length;a<s;a++)arguments[a]&&Mn(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return Mn(t,(t,r)=>{n&&Sn(t)?e[r]=fn(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,s,o;const i={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),s=a.length;s-- >0;)o=a[s],r&&!r(o,e,t)||i[o]||(t[o]=e[o],i[o]=!0);e=!1!==n&&pn(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:yn,kindOfTest:bn,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(wn(e))return e;let t=e.length;if(!_n(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[hn]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:$n,hasOwnProperty:Hn,hasOwnProp:Hn,reduceDescriptors:Qn,freezeMethods:e=>{Qn(e,(t,n)=>{if(Sn(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];Sn(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return wn(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:In,global:Un,isContextDefined:Vn,isSpecCompliantForm:function(e){return!!(e&&Sn(e.append)&&"FormData"===e[gn]&&e[hn])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(En(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=wn(e)?[]:{};return Mn(e,(e,t)=>{const s=n(e,r+1);!kn(s)&&(a[t]=s)}),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:Kn,isThenable:e=>e&&(En(e)||Sn(e))&&Sn(e.then)&&Sn(e.catch),setImmediate:Yn,asap:Xn,isIterable:e=>null!=e&&Sn(e[hn])};function Gn(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}Jn.inherits(Gn,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Jn.toJSONObject(this.config),code:this.code,status:this.status}}});const Zn=Gn.prototype,er={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{er[e]={value:e}}),Object.defineProperties(Gn,er),Object.defineProperty(Zn,"isAxiosError",{value:!0}),Gn.from=(e,t,n,r,a,s)=>{const o=Object.create(Zn);return Jn.toFlatObject(e,o,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),Gn.call(o,e.message,t,n,r,a),o.cause=e,o.name=e.name,s&&Object.assign(o,s),o};const tr=Gn;function nr(e){return Jn.isPlainObject(e)||Jn.isArray(e)}function rr(e){return Jn.endsWith(e,"[]")?e.slice(0,-2):e}function ar(e,t,n){return e?e.concat(t).map(function(e,t){return e=rr(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const sr=Jn.toFlatObject(Jn,{},null,function(e){return/^is[A-Z]/.test(e)});const or=function(e,t,n){if(!Jn.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Jn.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!Jn.isUndefined(t[e])})).metaTokens,a=n.visitor||c,s=n.dots,o=n.indexes,i=(n.Blob||"undefined"!==typeof Blob&&Blob)&&Jn.isSpecCompliantForm(t);if(!Jn.isFunction(a))throw new TypeError("visitor must be a function");function l(e){if(null===e)return"";if(Jn.isDate(e))return e.toISOString();if(Jn.isBoolean(e))return e.toString();if(!i&&Jn.isBlob(e))throw new tr("Blob is not supported. Use a Buffer instead.");return Jn.isArrayBuffer(e)||Jn.isTypedArray(e)?i&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,n,a){let i=e;if(e&&!a&&"object"===typeof e)if(Jn.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Jn.isArray(e)&&function(e){return Jn.isArray(e)&&!e.some(nr)}(e)||(Jn.isFileList(e)||Jn.endsWith(n,"[]"))&&(i=Jn.toArray(e)))return n=rr(n),i.forEach(function(e,r){!Jn.isUndefined(e)&&null!==e&&t.append(!0===o?ar([n],r,s):null===o?n:n+"[]",l(e))}),!1;return!!nr(e)||(t.append(ar(a,n,s),l(e)),!1)}const u=[],d=Object.assign(sr,{defaultVisitor:c,convertValue:l,isVisitable:nr});if(!Jn.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Jn.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),Jn.forEach(n,function(n,s){!0===(!(Jn.isUndefined(n)||null===n)&&a.call(t,n,Jn.isString(s)?s.trim():s,r,d))&&e(n,r?r.concat(s):[s])}),u.pop()}}(e),t};function ir(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function lr(e,t){this._pairs=[],e&&or(e,this,t)}const cr=lr.prototype;cr.append=function(e,t){this._pairs.push([e,t])},cr.toString=function(e){const t=e?function(t){return e.call(this,t,ir)}:ir;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const ur=lr;function dr(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function fr(e,t,n){if(!t)return e;const r=n&&n.encode||dr;Jn.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let s;if(s=a?a(t,n):Jn.isURLSearchParams(t)?t.toString():new ur(t,n).toString(r),s){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}const mr=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Jn.forEach(this.handlers,function(t){null!==t&&e(t)})}},pr={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},hr={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:ur,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},gr="undefined"!==typeof window&&"undefined"!==typeof document,yr="object"===typeof navigator&&navigator||void 0,vr=gr&&(!yr||["ReactNative","NativeScript","NS"].indexOf(yr.product)<0),br="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,xr=gr&&window.location.href||"http://localhost",wr=c(c({},e),hr);const kr=function(e){function t(e,n,r,a){let s=e[a++];if("__proto__"===s)return!0;const o=Number.isFinite(+s),i=a>=e.length;if(s=!s&&Jn.isArray(r)?r.length:s,i)return Jn.hasOwnProp(r,s)?r[s]=[r[s],n]:r[s]=n,!o;r[s]&&Jn.isObject(r[s])||(r[s]=[]);return t(e,n,r[s],a)&&Jn.isArray(r[s])&&(r[s]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let s;for(r=0;r<a;r++)s=n[r],t[s]=e[s];return t}(r[s])),!o}if(Jn.isFormData(e)&&Jn.isFunction(e.entries)){const n={};return Jn.forEachEntry(e,(e,r)=>{t(function(e){return Jn.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const Nr={transitional:pr,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=Jn.isObject(e);a&&Jn.isHTMLForm(e)&&(e=new FormData(e));if(Jn.isFormData(e))return r?JSON.stringify(kr(e)):e;if(Jn.isArrayBuffer(e)||Jn.isBuffer(e)||Jn.isStream(e)||Jn.isFile(e)||Jn.isBlob(e)||Jn.isReadableStream(e))return e;if(Jn.isArrayBufferView(e))return e.buffer;if(Jn.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let s;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return or(e,new wr.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return wr.isNode&&Jn.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((s=Jn.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return or(s?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(Jn.isString(e))try{return(t||JSON.parse)(e),Jn.trim(e)}catch(rc){if("SyntaxError"!==rc.name)throw rc}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Nr.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Jn.isResponse(e)||Jn.isReadableStream(e))return e;if(e&&Jn.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(rc){if(n){if("SyntaxError"===rc.name)throw tr.from(rc,tr.ERR_BAD_RESPONSE,this,null,this.response);throw rc}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:wr.classes.FormData,Blob:wr.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Jn.forEach(["delete","get","head","post","put","patch"],e=>{Nr.headers[e]={}});const jr=Nr,Sr=Jn.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),_r=Symbol("internals");function Er(e){return e&&String(e).trim().toLowerCase()}function Tr(e){return!1===e||null==e?e:Jn.isArray(e)?e.map(Tr):String(e)}function Cr(e,t,n,r,a){return Jn.isFunction(r)?r.call(this,t,n):(a&&(t=n),Jn.isString(t)?Jn.isString(r)?-1!==t.indexOf(r):Jn.isRegExp(r)?r.test(t):void 0:void 0)}class Ar{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=Er(t);if(!a)throw new Error("header name must be a non-empty string");const s=Jn.findKey(r,a);(!s||void 0===r[s]||!0===n||void 0===n&&!1!==r[s])&&(r[s||t]=Tr(e))}const s=(e,t)=>Jn.forEach(e,(e,n)=>a(e,n,t));if(Jn.isPlainObject(e)||e instanceof this.constructor)s(e,t);else if(Jn.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))s((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach(function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&Sr[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(Jn.isObject(e)&&Jn.isIterable(e)){let n,r,a={};for(const t of e){if(!Jn.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?Jn.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}s(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=Er(e)){const n=Jn.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Jn.isFunction(t))return t.call(this,e,n);if(Jn.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Er(e)){const n=Jn.findKey(this,e);return!(!n||void 0===this[n]||t&&!Cr(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=Er(e)){const a=Jn.findKey(n,e);!a||t&&!Cr(0,n[a],a,t)||(delete n[a],r=!0)}}return Jn.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!Cr(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return Jn.forEach(this,(r,a)=>{const s=Jn.findKey(n,a);if(s)return t[s]=Tr(r),void delete t[a];const o=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(a):String(a).trim();o!==a&&delete t[a],t[o]=Tr(r),n[o]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return Jn.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Jn.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[_r]=this[_r]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Er(e);t[r]||(!function(e,t){const n=Jn.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})})}(n,e),t[r]=!0)}return Jn.isArray(e)?e.forEach(r):r(e),this}}Ar.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Jn.reduceDescriptors(Ar.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),Jn.freezeMethods(Ar);const Pr=Ar;function Or(e,t){const n=this||jr,r=t||n,a=Pr.from(r.headers);let s=r.data;return Jn.forEach(e,function(e){s=e.call(n,s,a.normalize(),t?t.status:void 0)}),a.normalize(),s}function Rr(e){return!(!e||!e.__CANCEL__)}function Fr(e,t,n){tr.call(this,null==e?"canceled":e,tr.ERR_CANCELED,t,n),this.name="CanceledError"}Jn.inherits(Fr,tr,{__CANCEL__:!0});const Lr=Fr;function Dr(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new tr("Request failed with status code "+n.status,[tr.ERR_BAD_REQUEST,tr.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const zr=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,s=0,o=0;return t=void 0!==t?t:1e3,function(i){const l=Date.now(),c=r[o];a||(a=l),n[s]=i,r[s]=l;let u=o,d=0;for(;u!==s;)d+=n[u++],u%=e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),l-a<t)return;const f=c&&l-c;return f?Math.round(1e3*d/f):void 0}};const Mr=function(e,t){let n,r,a=0,s=1e3/t;const o=function(t){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=s,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var i=arguments.length,l=new Array(i),c=0;c<i;c++)l[c]=arguments[c];t>=s?o(l,e):(n=l,r||(r=setTimeout(()=>{r=null,o(n)},s-t)))},()=>n&&o(n)]},Ir=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=zr(50,250);return Mr(n=>{const s=n.loaded,o=n.lengthComputable?n.total:void 0,i=s-r,l=a(i);r=s;e({loaded:s,total:o,progress:o?s/o:void 0,bytes:i,rate:l||void 0,estimated:l&&o&&s<=o?(o-s)/l:void 0,event:n,lengthComputable:null!=o,[t?"download":"upload"]:!0})},n)},Ur=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Vr=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Jn.asap(()=>e(...n))},Br=wr.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,wr.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(wr.origin),wr.navigator&&/(msie|trident)/i.test(wr.navigator.userAgent)):()=>!0,qr=wr.hasStandardBrowserEnv?{write(e,t,n,r,a,s){const o=[e+"="+encodeURIComponent(t)];Jn.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),Jn.isString(r)&&o.push("path="+r),Jn.isString(a)&&o.push("domain="+a),!0===s&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function $r(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Hr=e=>e instanceof Pr?c({},e):e;function Wr(e,t){t=t||{};const n={};function r(e,t,n,r){return Jn.isPlainObject(e)&&Jn.isPlainObject(t)?Jn.merge.call({caseless:r},e,t):Jn.isPlainObject(t)?Jn.merge({},t):Jn.isArray(t)?t.slice():t}function a(e,t,n,a){return Jn.isUndefined(t)?Jn.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function s(e,t){if(!Jn.isUndefined(t))return r(void 0,t)}function o(e,t){return Jn.isUndefined(t)?Jn.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(n,a,s){return s in t?r(n,a):s in e?r(void 0,n):void 0}const l={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:i,headers:(e,t,n)=>a(Hr(e),Hr(t),0,!0)};return Jn.forEach(Object.keys(Object.assign({},e,t)),function(r){const s=l[r]||a,o=s(e[r],t[r],r);Jn.isUndefined(o)&&s!==i||(n[r]=o)}),n}const Qr=e=>{const t=Wr({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:l}=t;if(t.headers=i=Pr.from(i),t.url=fr($r(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),Jn.isFormData(r))if(wr.hasStandardBrowserEnv||wr.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(wr.hasStandardBrowserEnv&&(a&&Jn.isFunction(a)&&(a=a(t)),a||!1!==a&&Br(t.url))){const e=s&&o&&qr.read(o);e&&i.set(s,e)}return t},Kr="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=Qr(e);let a=r.data;const s=Pr.from(r.headers).normalize();let o,i,l,c,u,{responseType:d,onUploadProgress:f,onDownloadProgress:m}=r;function p(){c&&c(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(o),r.signal&&r.signal.removeEventListener("abort",o)}let h=new XMLHttpRequest;function g(){if(!h)return;const r=Pr.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders());Dr(function(e){t(e),p()},function(e){n(e),p()},{data:d&&"text"!==d&&"json"!==d?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:r,config:e,request:h}),h=null}h.open(r.method.toUpperCase(),r.url,!0),h.timeout=r.timeout,"onloadend"in h?h.onloadend=g:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(g)},h.onabort=function(){h&&(n(new tr("Request aborted",tr.ECONNABORTED,e,h)),h=null)},h.onerror=function(){n(new tr("Network Error",tr.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||pr;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new tr(t,a.clarifyTimeoutError?tr.ETIMEDOUT:tr.ECONNABORTED,e,h)),h=null},void 0===a&&s.setContentType(null),"setRequestHeader"in h&&Jn.forEach(s.toJSON(),function(e,t){h.setRequestHeader(t,e)}),Jn.isUndefined(r.withCredentials)||(h.withCredentials=!!r.withCredentials),d&&"json"!==d&&(h.responseType=r.responseType),m&&([l,u]=Ir(m,!0),h.addEventListener("progress",l)),f&&h.upload&&([i,c]=Ir(f),h.upload.addEventListener("progress",i),h.upload.addEventListener("loadend",c)),(r.cancelToken||r.signal)&&(o=t=>{h&&(n(!t||t.type?new Lr(null,e,h):t),h.abort(),h=null)},r.cancelToken&&r.cancelToken.subscribe(o),r.signal&&(r.signal.aborted?o():r.signal.addEventListener("abort",o)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===wr.protocols.indexOf(y)?n(new tr("Unsupported protocol "+y+":",tr.ERR_BAD_REQUEST,e)):h.send(a||null)})},Yr=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,o();const t=e instanceof Error?e:this.reason;r.abort(t instanceof tr?t:new Lr(t instanceof Error?t.message:t))}};let s=t&&setTimeout(()=>{s=null,a(new tr("timeout ".concat(t," of ms exceeded"),tr.ETIMEDOUT))},t);const o=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)}),e=null)};e.forEach(e=>e.addEventListener("abort",a));const{signal:i}=r;return i.unsubscribe=()=>Jn.asap(o),i}};function Xr(e,t){this.v=e,this.k=t}function Jr(e){return function(){return new Gr(e.apply(this,arguments))}}function Gr(e){var t,n;function r(t,n){try{var s=e[t](n),o=s.value,i=o instanceof Xr;Promise.resolve(i?o.v:o).then(function(n){if(i){var l="return"===t?"return":"next";if(!o.k||n.done)return r(l,n);n=e[l](n).value}a(s.done?"return":"normal",n)},function(e){r("throw",e)})}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise(function(s,o){var i={key:e,arg:a,resolve:s,reject:o,next:null};n?n=n.next=i:(t=n=i,r(e,a))})},"function"!=typeof e.return&&(this.return=void 0)}function Zr(e){return new Xr(e,0)}function ea(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise(function(n){n(e[t](r))}),{done:!1,value:new Xr(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function ta(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new na(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function na(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return na=function(e){this.s=e,this.n=e.next},na.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new na(e)}Gr.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},Gr.prototype.next=function(e){return this._invoke("next",e)},Gr.prototype.throw=function(e){return this._invoke("throw",e)},Gr.prototype.return=function(e){return this._invoke("return",e)};const ra=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},aa=function(){var e=Jr(function*(e,t){var n,r=!1,a=!1;try{for(var s,o=ta(sa(e));r=!(s=yield Zr(o.next())).done;r=!1){const e=s.value;yield*ea(ta(ra(e,t)))}}catch(i){a=!0,n=i}finally{try{r&&null!=o.return&&(yield Zr(o.return()))}finally{if(a)throw n}}});return function(t,n){return e.apply(this,arguments)}}(),sa=function(){var e=Jr(function*(e){if(e[Symbol.asyncIterator])return void(yield*ea(ta(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield Zr(t.read());if(e)break;yield n}}finally{yield Zr(t.cancel())}});return function(t){return e.apply(this,arguments)}}(),oa=(e,t,n,r)=>{const a=aa(e,t);let s,o=0,i=e=>{s||(s=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return i(),void e.close();let s=r.byteLength;if(n){let e=o+=s;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw i(t),t}},cancel:e=>(i(e),a.return())},{highWaterMark:2})},ia="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,la=ia&&"function"===typeof ReadableStream,ca=ia&&("function"===typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ua=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(rc){return!1}},da=la&&ua(()=>{let e=!1;const t=new Request(wr.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),fa=la&&ua(()=>Jn.isReadableStream(new Response("").body)),ma={stream:fa&&(e=>e.body)};var pa;ia&&(pa=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!ma[e]&&(ma[e]=Jn.isFunction(pa[e])?t=>t[e]():(t,n)=>{throw new tr("Response type '".concat(e,"' is not supported"),tr.ERR_NOT_SUPPORT,n)})}));const ha=async(e,t)=>{const n=Jn.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(Jn.isBlob(e))return e.size;if(Jn.isSpecCompliantForm(e)){const t=new Request(wr.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Jn.isArrayBufferView(e)||Jn.isArrayBuffer(e)?e.byteLength:(Jn.isURLSearchParams(e)&&(e+=""),Jn.isString(e)?(await ca(e)).byteLength:void 0)})(t):n},ga=ia&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:s,timeout:o,onDownloadProgress:i,onUploadProgress:l,responseType:u,headers:d,withCredentials:f="same-origin",fetchOptions:m}=Qr(e);u=u?(u+"").toLowerCase():"text";let p,h=Yr([a,s&&s.toAbortSignal()],o);const g=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let y;try{if(l&&da&&"get"!==n&&"head"!==n&&0!==(y=await ha(d,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(Jn.isFormData(r)&&(e=n.headers.get("content-type"))&&d.setContentType(e),n.body){const[e,t]=Ur(y,Ir(Vr(l)));r=oa(n.body,65536,e,t)}}Jn.isString(f)||(f=f?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,c(c({},m),{},{signal:h,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:a?f:void 0}));let s=await fetch(p,m);const o=fa&&("stream"===u||"response"===u);if(fa&&(i||o&&g)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=s[t]});const t=Jn.toFiniteNumber(s.headers.get("content-length")),[n,r]=i&&Ur(t,Ir(Vr(i),!0))||[];s=new Response(oa(s.body,65536,n,()=>{r&&r(),g&&g()}),e)}u=u||"text";let v=await ma[Jn.findKey(ma,u)||"text"](s,e);return!o&&g&&g(),await new Promise((t,n)=>{Dr(t,n,{data:v,headers:Pr.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:p})})}catch(v){if(g&&g(),v&&"TypeError"===v.name&&/Load failed|fetch/i.test(v.message))throw Object.assign(new tr("Network Error",tr.ERR_NETWORK,e,p),{cause:v.cause||v});throw tr.from(v,v&&v.code,e,p)}}),ya={http:null,xhr:Kr,fetch:ga};Jn.forEach(ya,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(rc){}Object.defineProperty(e,"adapterName",{value:t})}});const va=e=>"- ".concat(e),ba=e=>Jn.isFunction(e)||null===e||!1===e,xa=e=>{e=Jn.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let s=0;s<t;s++){let t;if(n=e[s],r=n,!ba(n)&&(r=ya[(t=String(n)).toLowerCase()],void 0===r))throw new tr("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+s]=r}if(!r){const e=Object.entries(a).map(e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")});let n=t?e.length>1?"since :\n"+e.map(va).join("\n"):" "+va(e[0]):"as no adapter specified";throw new tr("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function wa(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Lr(null,e)}function ka(e){wa(e),e.headers=Pr.from(e.headers),e.data=Or.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return xa(e.adapter||jr.adapter)(e).then(function(t){return wa(e),t.data=Or.call(e,e.transformResponse,t),t.headers=Pr.from(t.headers),t},function(t){return Rr(t)||(wa(e),t&&t.response&&(t.response.data=Or.call(e,e.transformResponse,t.response),t.response.headers=Pr.from(t.response.headers))),Promise.reject(t)})}const Na="1.10.0",ja={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ja[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Sa={};ja.transitional=function(e,t,n){function r(e,t){return"[Axios v"+Na+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,s)=>{if(!1===e)throw new tr(r(a," has been removed"+(t?" in "+t:"")),tr.ERR_DEPRECATED);return t&&!Sa[a]&&(Sa[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,s)}},ja.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const _a={assertOptions:function(e,t,n){if("object"!==typeof e)throw new tr("options must be an object",tr.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const s=r[a],o=t[s];if(o){const t=e[s],n=void 0===t||o(t,s,e);if(!0!==n)throw new tr("option "+s+" must be "+n,tr.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new tr("Unknown option "+s,tr.ERR_BAD_OPTION)}},validators:ja},Ea=_a.validators;class Ta{constructor(e){this.defaults=e||{},this.interceptors={request:new mr,response:new mr}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(rc){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=Wr(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&_a.assertOptions(n,{silentJSONParsing:Ea.transitional(Ea.boolean),forcedJSONParsing:Ea.transitional(Ea.boolean),clarifyTimeoutError:Ea.transitional(Ea.boolean)},!1),null!=r&&(Jn.isFunction(r)?t.paramsSerializer={serialize:r}:_a.assertOptions(r,{encode:Ea.function,serialize:Ea.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),_a.assertOptions(t,{baseUrl:Ea.spelling("baseURL"),withXsrfToken:Ea.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=a&&Jn.merge(a.common,a[t.method]);a&&Jn.forEach(["delete","get","head","post","put","patch","common"],e=>{delete a[e]}),t.headers=Pr.concat(s,a);const o=[];let i=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,o.unshift(e.fulfilled,e.rejected))});const l=[];let c;this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u,d=0;if(!i){const e=[ka.bind(this),void 0];for(e.unshift.apply(e,o),e.push.apply(e,l),u=e.length,c=Promise.resolve(t);d<u;)c=c.then(e[d++],e[d++]);return c}u=o.length;let f=t;for(d=0;d<u;){const e=o[d++],t=o[d++];try{f=e(f)}catch(m){t.call(this,m);break}}try{c=ka.call(this,f)}catch(m){return Promise.reject(m)}for(d=0,u=l.length;d<u;)c=c.then(l[d++],l[d++]);return c}getUri(e){return fr($r((e=Wr(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Jn.forEach(["delete","get","head","options"],function(e){Ta.prototype[e]=function(t,n){return this.request(Wr(n||{},{method:e,url:t,data:(n||{}).data}))}}),Jn.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,a){return this.request(Wr(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Ta.prototype[e]=t(),Ta.prototype[e+"Form"]=t(!0)});const Ca=Ta;class Aa{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,a){n.reason||(n.reason=new Lr(e,r,a),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new Aa(function(t){e=t}),cancel:e}}}const Pa=Aa;const Oa={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Oa).forEach(e=>{let[t,n]=e;Oa[n]=t});const Ra=Oa;const Fa=function e(t){const n=new Ca(t),r=fn(Ca.prototype.request,n);return Jn.extend(r,Ca.prototype,n,{allOwnKeys:!0}),Jn.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Wr(t,n))},r}(jr);Fa.Axios=Ca,Fa.CanceledError=Lr,Fa.CancelToken=Pa,Fa.isCancel=Rr,Fa.VERSION=Na,Fa.toFormData=or,Fa.AxiosError=tr,Fa.Cancel=Fa.CanceledError,Fa.all=function(e){return Promise.all(e)},Fa.spread=function(e){return function(t){return e.apply(null,t)}},Fa.isAxiosError=function(e){return Jn.isObject(e)&&!0===e.isAxiosError},Fa.mergeConfig=Wr,Fa.AxiosHeaders=Pr,Fa.formToJSON=e=>kr(Jn.isHTMLForm(e)?new FormData(e):e),Fa.getAdapter=xa,Fa.HttpStatusCode=Ra,Fa.default=Fa;const La=Fa.create({baseURL:"https://localhost:7093/api",headers:{"Content-Type":"application/json"}});La.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),La.interceptors.response.use(e=>e,e=>{var t,n,r;401===(null===(t=e.response)||void 0===t?void 0:t.status)&&(localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.href="/login");const a=(null===(n=e.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.message)||"An error occurred";return en.error(a),Promise.reject(e)});const Da=async e=>(await La.post("/auth/login",e)).data,za=async()=>(await La.post("/auth/logout")).data,Ma=async()=>(await La.get("/auth/profile")).data,Ia=async e=>(await La.post("/auth/change-password",e)).data,Ua=async e=>(await La.post("/agents/register",e)).data,Va=async function(){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;const t={page:arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,pageSize:arguments.length>1&&void 0!==arguments[1]?arguments[1]:10};e&&(t.status=e);return(await La.get("/agents",{params:t})).data},Ba=async e=>(await La.get("/agents/".concat(e))).data,qa=async function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return(await La.patch("/agents/".concat(e,"/status"),{status:t,comments:n})).data},$a=async e=>(await La.get("/workflow/agents/".concat(e,"/summary"))).data,Ha=async e=>(await La.get("/workflow/agents/".concat(e,"/valid-statuses"))).data,Wa=async(e,t)=>(await La.post("/workflow/agents/".concat(e,"/validate-transition"),{newStatus:t})).data,Qa=async(e,t,n)=>(await La.post("/workflow/agents/".concat(e,"/transition"),{newStatus:t,comments:n})).data,Ka=async e=>(await La.post("/workflow/agents/".concat(e,"/auto-progress"))).data,Ya=async e=>(await La.get("/documents/status/".concat(e))).data,Xa=async e=>(await La.get("/documents/".concat(e,"/download"),{responseType:"blob"})).data,Ja=async function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return(await La.patch("/documents/".concat(e,"/status"),{status:t,comments:n})).data},Ga=async function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return(await La.patch("/documents/bulk-status",{documentIds:e,status:t,comments:n})).data};var Za=n(579);const es=(0,t.createContext)(),ts=()=>{const e=(0,t.useContext)(es);if(!e)throw new Error("useAuth must be used within an AuthProvider");return e},ns=e=>{let{children:n}=e;const[r,a]=(0,t.useState)(null),[s,o]=(0,t.useState)(!0),[i,l]=(0,t.useState)(!1);(0,t.useEffect)(()=>{(async()=>{const e=localStorage.getItem("token"),t=localStorage.getItem("user");if(e&&t)try{a(JSON.parse(t)),l(!0),await Ma()}catch(n){localStorage.removeItem("token"),localStorage.removeItem("user"),a(null),l(!1)}o(!1)})()},[]);const c=e=>(null===r||void 0===r?void 0:r.role)===e,u={user:r,loading:s,isAuthenticated:i,login:async e=>{try{o(!0);const t=await Da(e),{token:n,user:r}=t;return localStorage.setItem("token",n),localStorage.setItem("user",JSON.stringify(r)),a(r),l(!0),en.success("Login successful!"),{success:!0}}catch(r){var t,n;const e=(null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.message)||"Login failed";return en.error(e),{success:!1,error:e}}finally{o(!1)}},logout:async()=>{try{await za()}catch(e){console.error("Logout API error:",e)}finally{localStorage.removeItem("token"),localStorage.removeItem("user"),a(null),l(!1),en.success("Logged out successfully")}},updateUser:e=>{a(e),localStorage.setItem("user",JSON.stringify(e))},hasRole:c,hasAnyRole:e=>e.includes(null===r||void 0===r?void 0:r.role),isAgent:()=>c("Agent"),isReviewer:()=>c("Reviewer"),isAdmin:()=>c("Admin"),isSuperAdmin:()=>c("SuperAdmin")};return(0,Za.jsx)(es.Provider,{value:u,children:n})},rs=e=>{let{children:t,requiredRole:n=null}=e;const{isAuthenticated:r,user:a,loading:s}=ts();return s?(0,Za.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,Za.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):r?n&&(null===a||void 0===a?void 0:a.role)!==n?(0,Za.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,Za.jsxs)("div",{className:"text-center",children:[(0,Za.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Access Denied"}),(0,Za.jsx)("p",{className:"text-gray-600",children:"You don't have permission to access this page."})]})}):t:(0,Za.jsx)(Ae,{to:"/login",replace:!0})},as=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ss=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()))(e);return t.charAt(0).toUpperCase()+t.slice(1)},os=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var is={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const ls=["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"],cs=(0,t.forwardRef)((e,n)=>{let{color:r="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:l="",children:u,iconNode:d}=e,f=a(e,ls);return(0,t.createElement)("svg",c(c(c({ref:n},is),{},{width:s,height:s,stroke:r,strokeWidth:i?24*Number(o)/Number(s):o,className:os("lucide",l)},!u&&!(e=>{for(const t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"}),f),[...d.map(e=>{let[n,r]=e;return(0,t.createElement)(n,r)}),...Array.isArray(u)?u:[u]])}),us=["className"],ds=(e,n)=>{const r=(0,t.forwardRef)((r,s)=>{let{className:o}=r,i=a(r,us);return(0,t.createElement)(cs,c({ref:s,iconNode:n,className:os("lucide-".concat(as(ss(e))),"lucide-".concat(e),o)},i))});return r.displayName=ss(e),r},fs=ds("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),ms=ds("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),ps=ds("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),hs=ds("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),gs=ds("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),ys=ds("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),vs=ds("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),bs=ds("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]),xs=()=>{const{user:e,logout:n,isAgent:r,isAdmin:a,isReviewer:s,isSuperAdmin:o}=ts(),i=ue(),l=me(),[c,u]=(0,t.useState)(!1),d=[{name:"Dashboard",href:"/dashboard",icon:fs,show:!0},{name:"Register Agent",href:"/admin/register",icon:ms,show:a()||o()},{name:"Agents",href:"/agents",icon:ps,show:a()||s()},{name:"Documents",href:"/documents",icon:hs,show:a()||s()},{name:"Profile",href:"/profile",icon:gs,show:!0}].filter(e=>e.show),f=e=>i.pathname===e;return(0,Za.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,Za.jsxs)("div",{className:"fixed inset-0 z-40 lg:hidden ".concat(c?"":"hidden"),children:[(0,Za.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>u(!1)}),(0,Za.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white",children:[(0,Za.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,Za.jsx)("button",{className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>u(!1),children:(0,Za.jsx)(ys,{className:"h-6 w-6 text-white"})})}),(0,Za.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,Za.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,Za.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"IDFC Agent Portal"})}),(0,Za.jsx)("nav",{className:"mt-5 px-2 space-y-1",children:d.map(e=>{const t=e.icon;return(0,Za.jsxs)(yt,{to:e.href,className:"".concat(f(e.href)?"bg-blue-100 text-blue-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"," group flex items-center px-2 py-2 text-base font-medium rounded-md"),onClick:()=>u(!1),children:[(0,Za.jsx)(t,{className:"mr-4 h-6 w-6"}),e.name]},e.name)})})]})]})]}),(0,Za.jsx)("div",{className:"hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0",children:(0,Za.jsx)("div",{className:"flex-1 flex flex-col min-h-0 bg-white border-r border-gray-200",children:(0,Za.jsxs)("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[(0,Za.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,Za.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"IDFC Agent Portal"})}),(0,Za.jsx)("nav",{className:"mt-5 flex-1 px-2 space-y-1",children:d.map(e=>{const t=e.icon;return(0,Za.jsxs)(yt,{to:e.href,className:"".concat(f(e.href)?"bg-blue-100 text-blue-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"," group flex items-center px-2 py-2 text-sm font-medium rounded-md"),children:[(0,Za.jsx)(t,{className:"mr-3 h-6 w-6"}),e.name]},e.name)})})]})})}),(0,Za.jsxs)("div",{className:"lg:pl-64 flex flex-col flex-1",children:[(0,Za.jsx)("div",{className:"sticky top-0 z-10 lg:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50",children:(0,Za.jsx)("button",{className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",onClick:()=>u(!0),children:(0,Za.jsx)(vs,{className:"h-6 w-6"})})}),(0,Za.jsx)("div",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,Za.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,Za.jsxs)("div",{className:"flex justify-between h-16",children:[(0,Za.jsx)("div",{className:"flex items-center",children:(0,Za.jsxs)("h2",{className:"text-lg font-semibold text-gray-900",children:["Welcome, ",null===e||void 0===e?void 0:e.username]})}),(0,Za.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,Za.jsxs)("span",{className:"text-sm text-gray-500",children:["Role: ",null===e||void 0===e?void 0:e.role]}),(0,Za.jsxs)("button",{onClick:async()=>{await n(),l("/login")},className:"flex items-center text-gray-500 hover:text-gray-700",children:[(0,Za.jsx)(bs,{className:"h-5 w-5 mr-1"}),"Logout"]})]})]})})}),(0,Za.jsx)("main",{className:"flex-1",children:(0,Za.jsx)("div",{className:"py-6",children:(0,Za.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,Za.jsx)(Pe,{})})})})]})]})},ws=["_f"],ks=["name"],Ns=["_f"],js=["ref","message","type"],Ss=["formControl"];var _s=e=>"checkbox"===e.type,Es=e=>e instanceof Date,Ts=e=>null==e;const Cs=e=>"object"===typeof e;var As=e=>!Ts(e)&&!Array.isArray(e)&&Cs(e)&&!Es(e),Ps=e=>As(e)&&e.target?_s(e.target)?e.target.checked:e.target.value:e,Os=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),Rs="undefined"!==typeof window&&"undefined"!==typeof window.HTMLElement&&"undefined"!==typeof document;function Fs(e){let t;const n=Array.isArray(e),r="undefined"!==typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(Rs&&(e instanceof Blob||r)||!n&&!As(e))return e;if(t=n?[]:{},n||(e=>{const t=e.constructor&&e.constructor.prototype;return As(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const n in e)e.hasOwnProperty(n)&&(t[n]=Fs(e[n]));else t=e}return t}var Ls=e=>/^\w*$/.test(e),Ds=e=>void 0===e,zs=e=>Array.isArray(e)?e.filter(Boolean):[],Ms=e=>zs(e.replace(/["|']|\]/g,"").split(/\.|\[/)),Is=(e,t,n)=>{if(!t||!As(e))return n;const r=(Ls(t)?[t]:Ms(t)).reduce((e,t)=>Ts(e)?e:e[t],e);return Ds(r)||r===e?Ds(e[t])?n:e[t]:r},Us=e=>"boolean"===typeof e,Vs=(e,t,n)=>{let r=-1;const a=Ls(t)?[t]:Ms(t),s=a.length,o=s-1;for(;++r<s;){const t=a[r];let s=n;if(r!==o){const n=e[t];s=As(n)||Array.isArray(n)?n:isNaN(+a[r+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=s,e=e[t]}};const Bs="blur",qs="focusout",$s="onBlur",Hs="onChange",Ws="onSubmit",Qs="onTouched",Ks="all",Ys="max",Xs="min",Js="maxLength",Gs="minLength",Zs="pattern",eo="required",to="validate",no=t.createContext(null);no.displayName="HookFormContext";var ro=function(e,t,n){let r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];const a={defaultValues:t._defaultValues};for(const s in e)Object.defineProperty(a,s,{get:()=>{const a=s;return t._proxyFormState[a]!==Ks&&(t._proxyFormState[a]=!r||Ks),n&&(n[a]=!0),e[a]}});return a};const ao="undefined"!==typeof window?t.useLayoutEffect:t.useEffect;var so=e=>"string"===typeof e,oo=(e,t,n,r,a)=>so(e)?(r&&t.watch.add(e),Is(n,e,a)):Array.isArray(e)?e.map(e=>(r&&t.watch.add(e),Is(n,e))):(r&&(t.watchAll=!0),n);var io=(e,t,n,r,a)=>t?c(c({},n[e]),{},{types:c(c({},n[e]&&n[e].types?n[e].types:{}),{},{[r]:a||!0})}):{},lo=e=>Array.isArray(e)?e:[e],co=()=>{let e=[];return{get observers(){return e},next:t=>{for(const n of e)n.next&&n.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},uo=e=>Ts(e)||!Cs(e);function fo(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new WeakSet;if(uo(e)||uo(t))return e===t;if(Es(e)&&Es(t))return e.getTime()===t.getTime();const r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;if(n.has(e)||n.has(t))return!0;n.add(e),n.add(t);for(const s of r){const r=e[s];if(!a.includes(s))return!1;if("ref"!==s){const e=t[s];if(Es(r)&&Es(e)||As(r)&&As(e)||Array.isArray(r)&&Array.isArray(e)?!fo(r,e,n):r!==e)return!1}}return!0}var mo=e=>As(e)&&!Object.keys(e).length,po=e=>"file"===e.type,ho=e=>"function"===typeof e,go=e=>{if(!Rs)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},yo=e=>"select-multiple"===e.type,vo=e=>"radio"===e.type,bo=e=>go(e)&&e.isConnected;function xo(e,t){const n=Array.isArray(t)?t:Ls(t)?[t]:Ms(t),r=1===n.length?e:function(e,t){const n=t.slice(0,-1).length;let r=0;for(;r<n;)e=Ds(e)?r++:e[t[r++]];return e}(e,n),a=n.length-1,s=n[a];return r&&delete r[s],0!==a&&(As(r)&&mo(r)||Array.isArray(r)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!Ds(e[t]))return!1;return!0}(r))&&xo(e,n.slice(0,-1)),e}var wo=e=>{for(const t in e)if(ho(e[t]))return!0;return!1};function ko(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=Array.isArray(e);if(As(e)||n)for(const r in e)Array.isArray(e[r])||As(e[r])&&!wo(e[r])?(t[r]=Array.isArray(e[r])?[]:{},ko(e[r],t[r])):Ts(e[r])||(t[r]=!0);return t}function No(e,t,n){const r=Array.isArray(e);if(As(e)||r)for(const a in e)Array.isArray(e[a])||As(e[a])&&!wo(e[a])?Ds(t)||uo(n[a])?n[a]=Array.isArray(e[a])?ko(e[a],[]):c({},ko(e[a])):No(e[a],Ts(t)?{}:t[a],n[a]):n[a]=!fo(e[a],t[a]);return n}var jo=(e,t)=>No(e,t,ko(t));const So={value:!1,isValid:!1},_o={value:!0,isValid:!0};var Eo=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!Ds(e[0].attributes.value)?Ds(e[0].value)||""===e[0].value?_o:{value:e[0].value,isValid:!0}:_o:So}return So},To=(e,t)=>{let{valueAsNumber:n,valueAsDate:r,setValueAs:a}=t;return Ds(e)?e:n?""===e?NaN:e?+e:e:r&&so(e)?new Date(e):a?a(e):e};const Co={isValid:!1,value:null};var Ao=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,Co):Co;function Po(e){const t=e.ref;return po(t)?t.files:vo(t)?Ao(e.refs).value:yo(t)?[...t.selectedOptions].map(e=>{let{value:t}=e;return t}):_s(t)?Eo(e.refs).value:To(Ds(t.value)?e.ref.value:t.value,e)}var Oo=e=>e instanceof RegExp,Ro=e=>Ds(e)?e:Oo(e)?e.source:As(e)?Oo(e.value)?e.value.source:e.value:e,Fo=e=>({isOnSubmit:!e||e===Ws,isOnBlur:e===$s,isOnChange:e===Hs,isOnAll:e===Ks,isOnTouch:e===Qs});const Lo="AsyncFunction";var Do=e=>!!e&&!!e.validate&&!!(ho(e.validate)&&e.validate.constructor.name===Lo||As(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===Lo)),zo=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));const Mo=(e,t,n,r)=>{for(const s of n||Object.keys(e)){const n=Is(e,s);if(n){const{_f:e}=n,o=a(n,ws);if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],s)&&!r)return!0;if(e.ref&&t(e.ref,e.name)&&!r)return!0;if(Mo(o,t))break}else if(As(o)&&Mo(o,t))break}}};function Io(e,t,n){const r=Is(e,n);if(r||Ls(n))return{error:r,name:n};const a=n.split(".");for(;a.length;){const r=a.join("."),s=Is(t,r),o=Is(e,r);if(s&&!Array.isArray(s)&&n!==r)return{name:n};if(o&&o.type)return{name:r,error:o};if(o&&o.root&&o.root.type)return{name:"".concat(r,".root"),error:o.root};a.pop()}return{name:n}}var Uo=(e,t,n)=>{const r=lo(Is(e,n));return Vs(r,"root",t[n]),Vs(e,n,r),e},Vo=e=>so(e);function Bo(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"validate";if(Vo(e)||Array.isArray(e)&&e.every(Vo)||Us(e)&&!e)return{type:n,message:Vo(e)?e:"",ref:t}}var qo=e=>As(e)&&!Oo(e)?e:{value:e,message:""},$o=async(e,t,n,r,a,s)=>{const{ref:o,refs:i,required:l,maxLength:u,minLength:d,min:f,max:m,pattern:p,validate:h,name:g,valueAsNumber:y,mount:v}=e._f,b=Is(n,g);if(!v||t.has(g))return{};const x=i?i[0]:o,w=e=>{a&&x.reportValidity&&(x.setCustomValidity(Us(e)?"":e||""),x.reportValidity())},k={},N=vo(o),j=_s(o),S=N||j,_=(y||po(o))&&Ds(o.value)&&Ds(b)||go(o)&&""===o.value||""===b||Array.isArray(b)&&!b.length,E=io.bind(null,g,r,k),T=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Js,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:Gs;const s=e?t:n;k[g]=c({type:e?r:a,message:s,ref:o},E(e?r:a,s))};if(s?!Array.isArray(b)||!b.length:l&&(!S&&(_||Ts(b))||Us(b)&&!b||j&&!Eo(i).isValid||N&&!Ao(i).isValid)){const{value:e,message:t}=Vo(l)?{value:!!l,message:l}:qo(l);if(e&&(k[g]=c({type:eo,message:t,ref:x},E(eo,t)),!r))return w(t),k}if(!_&&(!Ts(f)||!Ts(m))){let e,t;const n=qo(m),a=qo(f);if(Ts(b)||isNaN(b)){const r=o.valueAsDate||new Date(b),s=e=>new Date((new Date).toDateString()+" "+e),i="time"==o.type,l="week"==o.type;so(n.value)&&b&&(e=i?s(b)>s(n.value):l?b>n.value:r>new Date(n.value)),so(a.value)&&b&&(t=i?s(b)<s(a.value):l?b<a.value:r<new Date(a.value))}else{const r=o.valueAsNumber||(b?+b:b);Ts(n.value)||(e=r>n.value),Ts(a.value)||(t=r<a.value)}if((e||t)&&(T(!!e,n.message,a.message,Ys,Xs),!r))return w(k[g].message),k}if((u||d)&&!_&&(so(b)||s&&Array.isArray(b))){const e=qo(u),t=qo(d),n=!Ts(e.value)&&b.length>+e.value,a=!Ts(t.value)&&b.length<+t.value;if((n||a)&&(T(n,e.message,t.message),!r))return w(k[g].message),k}if(p&&!_&&so(b)){const{value:e,message:t}=qo(p);if(Oo(e)&&!b.match(e)&&(k[g]=c({type:Zs,message:t,ref:o},E(Zs,t)),!r))return w(t),k}if(h)if(ho(h)){const e=Bo(await h(b,n),x);if(e&&(k[g]=c(c({},e),E(to,e.message)),!r))return w(e.message),k}else if(As(h)){let e={};for(const t in h){if(!mo(e)&&!r)break;const a=Bo(await h[t](b,n),x,t);a&&(e=c(c({},a),E(t,a.message)),w(a.message),r&&(k[g]=e))}if(!mo(e)&&(k[g]=c({ref:x},e),!r))return k}return w(!0),k};const Ho={mode:Ws,reValidateMode:Hs,shouldFocusError:!0};function Wo(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=c(c({},Ho),e),n={submitCount:0,isDirty:!1,isReady:!1,isLoading:ho(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1};const r={};let s,o=(As(t.defaultValues)||As(t.values))&&Fs(t.defaultValues||t.values)||{},i=t.shouldUnregister?{}:Fs(o),l={action:!1,mount:!1,watch:!1},u={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},d=0;const f={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let m=c({},f);const p={array:co(),state:co()},h=t.criteriaMode===Ks,g=async e=>{if(!t.disabled&&(f.isValid||m.isValid||e)){const e=t.resolver?mo((await w()).errors):await k(r,!0);e!==n.isValid&&p.state.next({isValid:e})}},y=(e,r)=>{!t.disabled&&(f.isValidating||f.validatingFields||m.isValidating||m.validatingFields)&&((e||Array.from(u.mount)).forEach(e=>{e&&(r?Vs(n.validatingFields,e,r):xo(n.validatingFields,e))}),p.state.next({validatingFields:n.validatingFields,isValidating:!mo(n.validatingFields)}))},v=(e,t,n,a)=>{const s=Is(r,e);if(s){const r=Is(i,e,Ds(n)?Is(o,e):n);Ds(r)||a&&a.defaultChecked||t?Vs(i,e,t?r:Po(s._f)):S(e,r),l.mount&&g()}},b=(e,r,a,s,i)=>{let l=!1,c=!1;const u={name:e};if(!t.disabled){if(!a||s){(f.isDirty||m.isDirty)&&(c=n.isDirty,n.isDirty=u.isDirty=N(),l=c!==u.isDirty);const t=fo(Is(o,e),r);c=!!Is(n.dirtyFields,e),t?xo(n.dirtyFields,e):Vs(n.dirtyFields,e,!0),u.dirtyFields=n.dirtyFields,l=l||(f.dirtyFields||m.dirtyFields)&&c!==!t}if(a){const t=Is(n.touchedFields,e);t||(Vs(n.touchedFields,e,a),u.touchedFields=n.touchedFields,l=l||(f.touchedFields||m.touchedFields)&&t!==a)}l&&i&&p.state.next(u)}return l?u:{}},x=(e,r,a,o)=>{const i=Is(n.errors,e),l=(f.isValid||m.isValid)&&Us(r)&&n.isValid!==r;var u;if(t.delayError&&a?(u=()=>((e,t)=>{Vs(n.errors,e,t),p.state.next({errors:n.errors})})(e,a),s=e=>{clearTimeout(d),d=setTimeout(u,e)},s(t.delayError)):(clearTimeout(d),s=null,a?Vs(n.errors,e,a):xo(n.errors,e)),(a?!fo(i,a):i)||!mo(o)||l){const t=c(c(c({},o),l&&Us(r)?{isValid:r}:{}),{},{errors:n.errors,name:e});n=c(c({},n),t),p.state.next(t)}},w=async e=>{y(e,!0);const n=await t.resolver(i,t.context,((e,t,n,r)=>{const a={};for(const s of e){const e=Is(t,s);e&&Vs(a,s,e._f)}return{criteriaMode:n,names:[...e],fields:a,shouldUseNativeValidation:r}})(e||u.mount,r,t.criteriaMode,t.shouldUseNativeValidation));return y(e),n},k=async function(e,r){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{valid:!0};for(const o in e){const l=e[o];if(l){const{_f:e}=l,c=a(l,Ns);if(e){const a=u.array.has(e.name),c=l._f&&Do(l._f);c&&f.validatingFields&&y([o],!0);const d=await $o(l,u.disabled,i,h,t.shouldUseNativeValidation&&!r,a);if(c&&f.validatingFields&&y([o]),d[e.name]&&(s.valid=!1,r))break;!r&&(Is(d,e.name)?a?Uo(n.errors,d,e.name):Vs(n.errors,e.name,d[e.name]):xo(n.errors,e.name))}!mo(c)&&await k(c,r,s)}}return s.valid},N=(e,n)=>!t.disabled&&(e&&n&&Vs(i,e,n),!fo(P(),o)),j=(e,t,n)=>oo(e,u,c({},l.mount?i:Ds(t)?o:so(e)?{[e]:t}:t),n,t),S=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const a=Is(r,e);let s=t;if(a){const n=a._f;n&&(!n.disabled&&Vs(i,e,To(t,n)),s=go(n.ref)&&Ts(t)?"":t,yo(n.ref)?[...n.ref.options].forEach(e=>e.selected=s.includes(e.value)):n.refs?_s(n.ref)?n.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(s)?e.checked=!!s.find(t=>t===e.value):e.checked=s===e.value||!!s)}):n.refs.forEach(e=>e.checked=e.value===s):po(n.ref)?n.ref.value="":(n.ref.value=s,n.ref.type||p.state.next({name:e,values:Fs(i)})))}(n.shouldDirty||n.shouldTouch)&&b(e,s,n.shouldTouch,n.shouldDirty,!0),n.shouldValidate&&A(e)},_=(e,t,n)=>{for(const a in t){if(!t.hasOwnProperty(a))return;const s=t[a],o=e+"."+a,i=Is(r,o);(u.array.has(e)||As(s)||i&&!i._f)&&!Es(s)?_(o,s,n):S(o,s,n)}},E=function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const s=Is(r,e),d=u.array.has(e),h=Fs(t);Vs(i,e,h),d?(p.array.next({name:e,values:Fs(i)}),(f.isDirty||f.dirtyFields||m.isDirty||m.dirtyFields)&&a.shouldDirty&&p.state.next({name:e,dirtyFields:jo(o,i),isDirty:N(e,h)})):!s||s._f||Ts(h)?S(e,h,a):_(e,h,a),zo(e,u)&&p.state.next(c({},n)),p.state.next({name:l.mount?e:void 0,values:Fs(i)})},T=async e=>{l.mount=!0;const a=e.target;let o=a.name,d=!0;const v=Is(r,o),N=e=>{d=Number.isNaN(e)||Es(e)&&isNaN(e.getTime())||fo(e,Is(i,o,e))},j=Fo(t.mode),S=Fo(t.reValidateMode);if(v){let l,E;const T=a.type?Po(v._f):Ps(e),C=e.type===Bs||e.type===qs,P=!((_=v._f).mount&&(_.required||_.min||_.max||_.maxLength||_.minLength||_.pattern||_.validate))&&!t.resolver&&!Is(n.errors,o)&&!v._f.deps||((e,t,n,r,a)=>!a.isOnAll&&(!n&&a.isOnTouch?!(t||e):(n?r.isOnBlur:a.isOnBlur)?!e:!(n?r.isOnChange:a.isOnChange)||e))(C,Is(n.touchedFields,o),n.isSubmitted,S,j),O=zo(o,u,C);Vs(i,o,T),C?(v._f.onBlur&&v._f.onBlur(e),s&&s(0)):v._f.onChange&&v._f.onChange(e);const R=b(o,T,C),F=!mo(R)||O;if(!C&&p.state.next({name:o,type:e.type,values:Fs(i)}),P)return(f.isValid||m.isValid)&&("onBlur"===t.mode?C&&g():C||g()),F&&p.state.next(c({name:o},O?{}:R));if(!C&&O&&p.state.next(c({},n)),t.resolver){const{errors:e}=await w([o]);if(N(T),d){const t=Io(n.errors,r,o),a=Io(e,r,t.name||o);l=a.error,o=a.name,E=mo(e)}}else y([o],!0),l=(await $o(v,u.disabled,i,h,t.shouldUseNativeValidation))[o],y([o]),N(T),d&&(l?E=!1:(f.isValid||m.isValid)&&(E=await k(r,!0)));d&&(v._f.deps&&A(v._f.deps),x(o,E,l,R))}var _},C=(e,t)=>{if(Is(n.errors,t)&&e.focus)return e.focus(),1},A=async function(e){let a,s,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=lo(e);if(t.resolver){const t=await(async e=>{const{errors:t}=await w(e);if(e)for(const r of e){const e=Is(t,r);e?Vs(n.errors,r,e):xo(n.errors,r)}else n.errors=t;return t})(Ds(e)?e:i);a=mo(t),s=e?!i.some(e=>Is(t,e)):a}else e?(s=(await Promise.all(i.map(async e=>{const t=Is(r,e);return await k(t&&t._f?{[e]:t}:t)}))).every(Boolean),(s||n.isValid)&&g()):s=a=await k(r);return p.state.next(c(c(c({},!so(e)||(f.isValid||m.isValid)&&a!==n.isValid?{}:{name:e}),t.resolver||!e?{isValid:a}:{}),{},{errors:n.errors})),o.shouldFocus&&!s&&Mo(r,C,e?i:u.mount),s},P=e=>{const t=c({},l.mount?i:o);return Ds(e)?t:so(e)?Is(t,e):e.map(e=>Is(t,e))},O=(e,t)=>({invalid:!!Is((t||n).errors,e),isDirty:!!Is((t||n).dirtyFields,e),error:Is((t||n).errors,e),isValidating:!!Is(n.validatingFields,e),isTouched:!!Is((t||n).touchedFields,e)}),R=(e,t,s)=>{const o=(Is(r,e,{_f:{}})._f||{}).ref,i=Is(n.errors,e)||{},{ref:l,message:u,type:d}=i,f=a(i,js);Vs(n.errors,e,c(c(c({},f),t),{},{ref:o})),p.state.next({name:e,errors:n.errors,isValid:!1}),s&&s.shouldFocus&&o&&o.focus&&o.focus()},F=e=>p.state.subscribe({next:t=>{var r,s,o;r=e.name,s=t.name,o=e.exact,r&&s&&r!==s&&!lo(r).some(e=>e&&(o?e===s:e.startsWith(s)||s.startsWith(e)))||!((e,t,n,r)=>{n(e);const{name:s}=e,o=a(e,ks);return mo(o)||Object.keys(o).length>=Object.keys(t).length||Object.keys(o).find(e=>t[e]===(!r||Ks))})(t,e.formState||f,B,e.reRenderRoot)||e.callback(c(c({values:c({},i)},n),t))}}).unsubscribe,L=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(const s of e?lo(e):u.mount)u.mount.delete(s),u.array.delete(s),a.keepValue||(xo(r,s),xo(i,s)),!a.keepError&&xo(n.errors,s),!a.keepDirty&&xo(n.dirtyFields,s),!a.keepTouched&&xo(n.touchedFields,s),!a.keepIsValidating&&xo(n.validatingFields,s),!t.shouldUnregister&&!a.keepDefaultValue&&xo(o,s);p.state.next({values:Fs(i)}),p.state.next(c(c({},n),a.keepDirty?{isDirty:N()}:{})),!a.keepIsValid&&g()},D=e=>{let{disabled:t,name:n}=e;(Us(t)&&l.mount||t||u.disabled.has(n))&&(t?u.disabled.add(n):u.disabled.delete(n))},z=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=Is(r,e);const s=Us(n.disabled)||Us(t.disabled);return Vs(r,e,c(c({},a||{}),{},{_f:c(c({},a&&a._f?a._f:{ref:{name:e}}),{},{name:e,mount:!0},n)})),u.mount.add(e),a?D({disabled:Us(n.disabled)?n.disabled:t.disabled,name:e}):v(e,!0,n.value),c(c(c({},s?{disabled:n.disabled||t.disabled}:{}),t.progressive?{required:!!n.required,min:Ro(n.min),max:Ro(n.max),minLength:Ro(n.minLength),maxLength:Ro(n.maxLength),pattern:Ro(n.pattern)}:{}),{},{name:e,onChange:T,onBlur:T,ref:s=>{if(s){z(e,n),a=Is(r,e);const t=Ds(s.value)&&s.querySelectorAll&&s.querySelectorAll("input,select,textarea")[0]||s,i=(e=>vo(e)||_s(e))(t),l=a._f.refs||[];if(i?l.find(e=>e===t):t===a._f.ref)return;Vs(r,e,{_f:c(c({},a._f),i?{refs:[...l.filter(bo),t,...Array.isArray(Is(o,e))?[{}]:[]],ref:{type:t.type,name:e}}:{ref:t})}),v(e,!1,void 0,t)}else a=Is(r,e,{}),a._f&&(a._f.mount=!1),(t.shouldUnregister||n.shouldUnregister)&&(!Os(u.array,e)||!l.action)&&u.unMount.add(e)}})},M=()=>t.shouldFocusError&&Mo(r,C,u.mount),I=(e,a)=>async s=>{let o;s&&(s.preventDefault&&s.preventDefault(),s.persist&&s.persist());let l=Fs(i);if(p.state.next({isSubmitting:!0}),t.resolver){const{errors:e,values:t}=await w();n.errors=e,l=Fs(t)}else await k(r);if(u.disabled.size)for(const e of u.disabled)xo(l,e);if(xo(n.errors,"root"),mo(n.errors)){p.state.next({errors:{}});try{await e(l,s)}catch(d){o=d}}else a&&await a(c({},n.errors),s),M(),setTimeout(M);if(p.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:mo(n.errors)&&!o,submitCount:n.submitCount+1,errors:n.errors}),o)throw o},U=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const s=e?Fs(e):o,d=Fs(s),m=mo(e),h=m?o:d;if(a.keepDefaultValues||(o=s),!a.keepValues){if(a.keepDirtyValues){const e=new Set([...u.mount,...Object.keys(jo(o,i))]);for(const t of Array.from(e))Is(n.dirtyFields,t)?Vs(h,t,Is(i,t)):E(t,Is(h,t))}else{if(Rs&&Ds(e))for(const e of u.mount){const t=Is(r,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(go(e)){const t=e.closest("form");if(t){t.reset();break}}}}for(const e of u.mount){const t=Is(h,e,Is(o,e));Ds(t)||(Vs(h,e,t),E(e,Is(h,e)))}}i=Fs(h),p.array.next({values:c({},h)}),p.state.next({values:c({},h)})}u={mount:a.keepDirtyValues?u.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},l.mount=!f.isValid||!!a.keepIsValid||!!a.keepDirtyValues,l.watch=!!t.shouldUnregister,p.state.next({submitCount:a.keepSubmitCount?n.submitCount:0,isDirty:!m&&(a.keepDirty?n.isDirty:!(!a.keepDefaultValues||fo(e,o))),isSubmitted:!!a.keepIsSubmitted&&n.isSubmitted,dirtyFields:m?{}:a.keepDirtyValues?a.keepDefaultValues&&i?jo(o,i):n.dirtyFields:a.keepDefaultValues&&e?jo(o,e):a.keepDirty?n.dirtyFields:{},touchedFields:a.keepTouched?n.touchedFields:{},errors:a.keepErrors?n.errors:{},isSubmitSuccessful:!!a.keepIsSubmitSuccessful&&n.isSubmitSuccessful,isSubmitting:!1})},V=(e,t)=>U(ho(e)?e(i):e,t),B=e=>{n=c(c({},n),e)},q={control:{register:z,unregister:L,getFieldState:O,handleSubmit:I,setError:R,_subscribe:F,_runSchema:w,_focusError:M,_getWatch:j,_getDirty:N,_setValid:g,_setFieldArray:function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=arguments.length>2?arguments[2]:void 0,c=arguments.length>3?arguments[3]:void 0,u=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],d=!(arguments.length>5&&void 0!==arguments[5])||arguments[5];if(c&&s&&!t.disabled){if(l.action=!0,d&&Array.isArray(Is(r,e))){const t=s(Is(r,e),c.argA,c.argB);u&&Vs(r,e,t)}if(d&&Array.isArray(Is(n.errors,e))){const t=s(Is(n.errors,e),c.argA,c.argB);u&&Vs(n.errors,e,t),((e,t)=>{!zs(Is(e,t)).length&&xo(e,t)})(n.errors,e)}if((f.touchedFields||m.touchedFields)&&d&&Array.isArray(Is(n.touchedFields,e))){const t=s(Is(n.touchedFields,e),c.argA,c.argB);u&&Vs(n.touchedFields,e,t)}(f.dirtyFields||m.dirtyFields)&&(n.dirtyFields=jo(o,i)),p.state.next({name:e,isDirty:N(e,a),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else Vs(i,e,a)},_setDisabledField:D,_setErrors:e=>{n.errors=e,p.state.next({errors:n.errors,isValid:!1})},_getFieldArray:e=>zs(Is(l.mount?i:o,e,t.shouldUnregister?Is(o,e,[]):[])),_reset:U,_resetDefaultValues:()=>ho(t.defaultValues)&&t.defaultValues().then(e=>{V(e,t.resetOptions),p.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(const e of u.unMount){const t=Is(r,e);t&&(t._f.refs?t._f.refs.every(e=>!bo(e)):!bo(t._f.ref))&&L(e)}u.unMount=new Set},_disableForm:e=>{Us(e)&&(p.state.next({disabled:e}),Mo(r,(t,n)=>{const a=Is(r,n);a&&(t.disabled=a._f.disabled||e,Array.isArray(a._f.refs)&&a._f.refs.forEach(t=>{t.disabled=a._f.disabled||e}))},0,!1))},_subjects:p,_proxyFormState:f,get _fields(){return r},get _formValues(){return i},get _state(){return l},set _state(e){l=e},get _defaultValues(){return o},get _names(){return u},set _names(e){u=e},get _formState(){return n},get _options(){return t},set _options(e){t=c(c({},t),e)}},subscribe:e=>(l.mount=!0,m=c(c({},m),e.formState),F(c(c({},e),{},{formState:m}))),trigger:A,register:z,handleSubmit:I,watch:(e,t)=>ho(e)?p.state.subscribe({next:n=>e(j(void 0,t),n)}):j(e,t,!0),setValue:E,getValues:P,reset:V,resetField:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Is(r,e)&&(Ds(t.defaultValue)?E(e,Fs(Is(o,e))):(E(e,t.defaultValue),Vs(o,e,Fs(t.defaultValue))),t.keepTouched||xo(n.touchedFields,e),t.keepDirty||(xo(n.dirtyFields,e),n.isDirty=t.defaultValue?N(e,Fs(Is(o,e))):N()),t.keepError||(xo(n.errors,e),f.isValid&&g()),p.state.next(c({},n)))},clearErrors:e=>{e&&lo(e).forEach(e=>xo(n.errors,e)),p.state.next({errors:e?n.errors:{}})},unregister:L,setError:R,setFocus:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=Is(r,e),a=n&&n._f;if(a){const e=a.refs?a.refs[0]:a.ref;e.focus&&(e.focus(),t.shouldSelect&&ho(e.select)&&e.select())}},getFieldState:O};return c(c({},q),{},{formControl:q})}function Qo(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const n=t.useRef(void 0),r=t.useRef(void 0),[s,o]=t.useState({isDirty:!1,isValidating:!1,isLoading:ho(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:ho(e.defaultValues)?void 0:e.defaultValues});if(!n.current)if(e.formControl)n.current=c(c({},e.formControl),{},{formState:s}),e.defaultValues&&!ho(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const t=Wo(e),{formControl:r}=t,o=a(t,Ss);n.current=c(c({},o),{},{formState:s})}const i=n.current.control;return i._options=e,ao(()=>{const e=i._subscribe({formState:i._proxyFormState,callback:()=>o(c({},i._formState)),reRenderRoot:!0});return o(e=>c(c({},e),{},{isReady:!0})),i._formState.isReady=!0,e},[i]),t.useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),t.useEffect(()=>{e.mode&&(i._options.mode=e.mode),e.reValidateMode&&(i._options.reValidateMode=e.reValidateMode)},[i,e.mode,e.reValidateMode]),t.useEffect(()=>{e.errors&&(i._setErrors(e.errors),i._focusError())},[i,e.errors]),t.useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[i,e.shouldUnregister]),t.useEffect(()=>{if(i._proxyFormState.isDirty){const e=i._getDirty();e!==s.isDirty&&i._subjects.state.next({isDirty:e})}},[i,s.isDirty]),t.useEffect(()=>{e.values&&!fo(e.values,r.current)?(i._reset(e.values,i._options.resetOptions),r.current=e.values,o(e=>c({},e))):i._resetDefaultValues()},[i,e.values]),t.useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next(c({},i._formState))),i._removeUnmounted()}),n.current.formState=ro(s,i),n.current}const Ko=(e,t,n)=>{if(e&&"reportValidity"in e){const r=Is(n,t);e.setCustomValidity(r&&r.message||""),e.reportValidity()}},Yo=(e,t)=>{for(const n in t.fields){const r=t.fields[n];r&&r.ref&&"reportValidity"in r.ref?Ko(r.ref,n,e):r&&r.refs&&r.refs.forEach(t=>Ko(t,n,e))}},Xo=(e,t)=>{t.shouldUseNativeValidation&&Yo(e,t);const n={};for(const r in e){const a=Is(t.fields,r),s=Object.assign(e[r]||{},{ref:a&&a.ref});if(Jo(t.names||Object.keys(e),r)){const e=Object.assign({},Is(n,r));Vs(e,"root",s),Vs(n,r,e)}else Vs(n,r,s)}return n},Jo=(e,t)=>{const n=Go(t);return e.some(e=>Go(e).match("^".concat(n,"\\.\\d+")))};function Go(e){return e.replace(/\]|\[/g,"")}function Zo(e,t,n){return void 0===n&&(n={}),function(r,a,s){try{return Promise.resolve(function(o,i){try{var l=(null!=t&&t.context,Promise.resolve(e["sync"===n.mode?"validateSync":"validate"](r,Object.assign({abortEarly:!1},t,{context:a}))).then(function(e){return s.shouldUseNativeValidation&&Yo({},s),{values:n.raw?Object.assign({},r):e,errors:{}}}))}catch(rc){return i(rc)}return l&&l.then?l.then(void 0,i):l}(0,function(e){if(!e.inner)throw e;return{values:{},errors:Xo((t=e,n=!s.shouldUseNativeValidation&&"all"===s.criteriaMode,(t.inner||[]).reduce(function(e,t){if(e[t.path]||(e[t.path]={message:t.message,type:t.type}),n){var r=e[t.path].types,a=r&&r[t.type];e[t.path]=io(t.path,n,e,t.type,a?[].concat(a,t.message):t.message)}return e},{})),s)};var t,n}))}catch(rc){return Promise.reject(rc)}}}var ei=n(575),ti=n(517),ni=n(704),ri=n.n(ni);const ai=Object.prototype.toString,si=Error.prototype.toString,oi=RegExp.prototype.toString,ii="undefined"!==typeof Symbol?Symbol.prototype.toString:()=>"",li=/^Symbol\((.*)\)(.*)$/;function ci(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null==e||!0===e||!1===e)return""+e;const n=typeof e;if("number"===n)return function(e){return e!=+e?"NaN":0===e&&1/e<0?"-0":""+e}(e);if("string"===n)return t?'"'.concat(e,'"'):e;if("function"===n)return"[Function "+(e.name||"anonymous")+"]";if("symbol"===n)return ii.call(e).replace(li,"Symbol($1)");const r=ai.call(e).slice(8,-1);return"Date"===r?isNaN(e.getTime())?""+e:e.toISOString(e):"Error"===r||e instanceof Error?"["+si.call(e)+"]":"RegExp"===r?oi.call(e):null}function ui(e,t){let n=ci(e,t);return null!==n?n:JSON.stringify(e,function(e,n){let r=ci(this[e],t);return null!==r?r:n},2)}function di(e){return null==e?[]:[].concat(e)}let fi,mi,pi,hi=/\$\{\s*(\w+)\s*\}/g;fi=Symbol.toStringTag;class gi{constructor(e,t,n,r){this.name=void 0,this.message=void 0,this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=void 0,this.inner=void 0,this[fi]="Error",this.name="ValidationError",this.value=t,this.path=n,this.type=r,this.errors=[],this.inner=[],di(e).forEach(e=>{if(yi.isError(e)){this.errors.push(...e.errors);const t=e.inner.length?e.inner:[e];this.inner.push(...t)}else this.errors.push(e)}),this.message=this.errors.length>1?"".concat(this.errors.length," errors occurred"):this.errors[0]}}mi=Symbol.hasInstance,pi=Symbol.toStringTag;class yi extends Error{static formatError(e,t){const n=t.label||t.path||"this";return t=Object.assign({},t,{path:n,originalPath:t.path}),"string"===typeof e?e.replace(hi,(e,n)=>ui(t[n])):"function"===typeof e?e(t):e}static isError(e){return e&&"ValidationError"===e.name}constructor(e,t,n,r,a){const s=new gi(e,t,n,r);if(a)return s;super(),this.value=void 0,this.path=void 0,this.type=void 0,this.params=void 0,this.errors=[],this.inner=[],this[pi]="Error",this.name=s.name,this.message=s.message,this.type=s.type,this.value=s.value,this.path=s.path,this.errors=s.errors,this.inner=s.inner,Error.captureStackTrace&&Error.captureStackTrace(this,yi)}static[mi](e){return gi[Symbol.hasInstance](e)||super[Symbol.hasInstance](e)}}let vi={default:"${path} is invalid",required:"${path} is a required field",defined:"${path} must be defined",notNull:"${path} cannot be null",oneOf:"${path} must be one of the following values: ${values}",notOneOf:"${path} must not be one of the following values: ${values}",notType:e=>{let{path:t,type:n,value:r,originalValue:a}=e;const s=null!=a&&a!==r?" (cast from the value `".concat(ui(a,!0),"`)."):".";return"mixed"!==n?"".concat(t," must be a `").concat(n,"` type, ")+"but the final value was: `".concat(ui(r,!0),"`")+s:"".concat(t," must match the configured type. ")+"The validated value was: `".concat(ui(r,!0),"`")+s}},bi={length:"${path} must be exactly ${length} characters",min:"${path} must be at least ${min} characters",max:"${path} must be at most ${max} characters",matches:'${path} must match the following: "${regex}"',email:"${path} must be a valid email",url:"${path} must be a valid URL",uuid:"${path} must be a valid UUID",datetime:"${path} must be a valid ISO date-time",datetime_precision:"${path} must be a valid ISO date-time with a sub-second precision of exactly ${precision} digits",datetime_offset:'${path} must be a valid ISO date-time with UTC "Z" timezone',trim:"${path} must be a trimmed string",lowercase:"${path} must be a lowercase string",uppercase:"${path} must be a upper case string"},xi={min:"${path} must be greater than or equal to ${min}",max:"${path} must be less than or equal to ${max}",lessThan:"${path} must be less than ${less}",moreThan:"${path} must be greater than ${more}",positive:"${path} must be a positive number",negative:"${path} must be a negative number",integer:"${path} must be an integer"},wi={min:"${path} field must be later than ${min}",max:"${path} field must be at earlier than ${max}"},ki={isValue:"${path} field must be ${value}"},Ni={noUnknown:"${path} field has unspecified keys: ${unknown}",exact:"${path} object contains unknown properties: ${properties}"},ji={min:"${path} field must have at least ${min} items",max:"${path} field must have less than or equal to ${max} items",length:"${path} must have ${length} items"},Si={notType:e=>{const{path:t,value:n,spec:r}=e,a=r.types.length;if(Array.isArray(n)){if(n.length<a)return"".concat(t," tuple value has too few items, expected a length of ").concat(a," but got ").concat(n.length," for value: `").concat(ui(n,!0),"`");if(n.length>a)return"".concat(t," tuple value has too many items, expected a length of ").concat(a," but got ").concat(n.length," for value: `").concat(ui(n,!0),"`")}return yi.formatError(vi.notType,e)}};Object.assign(Object.create(null),{mixed:vi,string:bi,number:xi,date:wi,object:Ni,array:ji,boolean:ki,tuple:Si});const _i=e=>e&&e.__isYupSchema__;class Ei{static fromOptions(e,t){if(!t.then&&!t.otherwise)throw new TypeError("either `then:` or `otherwise:` is required for `when()` conditions");let{is:n,then:r,otherwise:a}=t,s="function"===typeof n?n:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.every(e=>e===n)};return new Ei(e,(e,t)=>{var n;let o=s(...e)?r:a;return null!=(n=null==o?void 0:o(t))?n:t})}constructor(e,t){this.fn=void 0,this.refs=e,this.refs=e,this.fn=t}resolve(e,t){let n=this.refs.map(e=>e.getValue(null==t?void 0:t.value,null==t?void 0:t.parent,null==t?void 0:t.context)),r=this.fn(n,e,t);if(void 0===r||r===e)return e;if(!_i(r))throw new TypeError("conditions must return a schema object");return r.resolve(t)}}const Ti="$",Ci=".";class Ai{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.key=void 0,this.isContext=void 0,this.isValue=void 0,this.isSibling=void 0,this.path=void 0,this.getter=void 0,this.map=void 0,"string"!==typeof e)throw new TypeError("ref must be a string, got: "+e);if(this.key=e.trim(),""===e)throw new TypeError("ref must be a non-empty string");this.isContext=this.key[0]===Ti,this.isValue=this.key[0]===Ci,this.isSibling=!this.isContext&&!this.isValue;let n=this.isContext?Ti:this.isValue?Ci:"";this.path=this.key.slice(n.length),this.getter=this.path&&(0,ei.getter)(this.path,!0),this.map=t.map}getValue(e,t,n){let r=this.isContext?n:this.isValue?e:t;return this.getter&&(r=this.getter(r||{})),this.map&&(r=this.map(r)),r}cast(e,t){return this.getValue(e,null==t?void 0:t.parent,null==t?void 0:t.context)}resolve(){return this}describe(){return{type:"ref",key:this.key}}toString(){return"Ref(".concat(this.key,")")}static isRef(e){return e&&e.__isYupRef}}Ai.prototype.__isYupRef=!0;const Pi=e=>null==e;function Oi(e){function t(t,n,r){let{value:a,path:s="",options:o,originalValue:i,schema:l}=t;const{name:c,test:u,params:d,message:f,skipAbsent:m}=e;let{parent:p,context:h,abortEarly:g=l.spec.abortEarly,disableStackTrace:y=l.spec.disableStackTrace}=o;function v(e){return Ai.isRef(e)?e.getValue(a,p,h):e}function b(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=Object.assign({value:a,originalValue:i,label:l.spec.label,path:e.path||s,spec:l.spec,disableStackTrace:e.disableStackTrace||y},d,e.params);for(const r of Object.keys(t))t[r]=v(t[r]);const n=new yi(yi.formatError(e.message||f,t),a,t.path,e.type||c,t.disableStackTrace);return n.params=t,n}const x=g?n:r;let w={path:s,parent:p,type:c,from:o.from,createError:b,resolve:v,options:o,originalValue:i,schema:l};const k=e=>{yi.isError(e)?x(e):e?r(null):x(b())},N=e=>{yi.isError(e)?x(e):n(e)};if(m&&Pi(a))return k(!0);let j;try{var S;if(j=u.call(w,a,w),"function"===typeof(null==(S=j)?void 0:S.then)){if(o.sync)throw new Error('Validation test of type: "'.concat(w.type,'" returned a Promise during a synchronous validate. ')+"This test will finish after the validate call has returned");return Promise.resolve(j).then(k,N)}}catch(_){return void N(_)}k(j)}return t.OPTIONS=e,t}function Ri(e,t,n){let r,a,s,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n;return t?((0,ei.forEach)(t,(i,l,c)=>{let u=l?i.slice(1,i.length-1):i,d="tuple"===(e=e.resolve({context:o,parent:r,value:n})).type,f=c?parseInt(u,10):0;if(e.innerType||d){if(d&&!c)throw new Error('Yup.reach cannot implicitly index into a tuple type. the path part "'.concat(s,'" must contain an index to the tuple element, e.g. "').concat(s,'[0]"'));if(n&&f>=n.length)throw new Error("Yup.reach cannot resolve an array item at index: ".concat(i,", in the path: ").concat(t,". ")+"because there is no value at that index. ");r=n,n=n&&n[f],e=d?e.spec.types[f]:e.innerType}if(!c){if(!e.fields||!e.fields[u])throw new Error("The schema does not contain the path: ".concat(t,". ")+"(failed at: ".concat(s,' which is a type: "').concat(e.type,'")'));r=n,n=n&&n[u],e=e.fields[u]}a=u,s=l?"["+i+"]":"."+i}),{schema:e,parent:r,parentPath:a}):{parent:r,parentPath:t,schema:e}}class Fi extends Set{describe(){const e=[];for(const t of this.values())e.push(Ai.isRef(t)?t.describe():t);return e}resolveAll(e){let t=[];for(const n of this.values())t.push(e(n));return t}clone(){return new Fi(this.values())}merge(e,t){const n=this.clone();return e.forEach(e=>n.add(e)),t.forEach(e=>n.delete(e)),n}}function Li(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;if(_i(e)||!e||"object"!==typeof e)return e;if(n.has(e))return n.get(e);if(e instanceof Date)t=new Date(e.getTime()),n.set(e,t);else if(e instanceof RegExp)t=new RegExp(e),n.set(e,t);else if(Array.isArray(e)){t=new Array(e.length),n.set(e,t);for(let r=0;r<e.length;r++)t[r]=Li(e[r],n)}else if(e instanceof Map){t=new Map,n.set(e,t);for(const[r,a]of e.entries())t.set(r,Li(a,n))}else if(e instanceof Set){t=new Set,n.set(e,t);for(const r of e)t.add(Li(r,n))}else{if(!(e instanceof Object))throw Error("Unable to clone ".concat(e));t={},n.set(e,t);for(const[r,a]of Object.entries(e))t[r]=Li(a,n)}return t}class Di{constructor(e){this.type=void 0,this.deps=[],this.tests=void 0,this.transforms=void 0,this.conditions=[],this._mutate=void 0,this.internalTests={},this._whitelist=new Fi,this._blacklist=new Fi,this.exclusiveTests=Object.create(null),this._typeCheck=void 0,this.spec=void 0,this.tests=[],this.transforms=[],this.withMutation(()=>{this.typeError(vi.notType)}),this.type=e.type,this._typeCheck=e.check,this.spec=Object.assign({strip:!1,strict:!1,abortEarly:!0,recursive:!0,disableStackTrace:!1,nullable:!1,optional:!0,coerce:!0},null==e?void 0:e.spec),this.withMutation(e=>{e.nonNullable()})}get _type(){return this.type}clone(e){if(this._mutate)return e&&Object.assign(this.spec,e),this;const t=Object.create(Object.getPrototypeOf(this));return t.type=this.type,t._typeCheck=this._typeCheck,t._whitelist=this._whitelist.clone(),t._blacklist=this._blacklist.clone(),t.internalTests=Object.assign({},this.internalTests),t.exclusiveTests=Object.assign({},this.exclusiveTests),t.deps=[...this.deps],t.conditions=[...this.conditions],t.tests=[...this.tests],t.transforms=[...this.transforms],t.spec=Li(Object.assign({},this.spec,e)),t}label(e){let t=this.clone();return t.spec.label=e,t}meta(){if(0===arguments.length)return this.spec.meta;let e=this.clone();return e.spec.meta=Object.assign(e.spec.meta||{},arguments.length<=0?void 0:arguments[0]),e}withMutation(e){let t=this._mutate;this._mutate=!0;let n=e(this);return this._mutate=t,n}concat(e){if(!e||e===this)return this;if(e.type!==this.type&&"mixed"!==this.type)throw new TypeError("You cannot `concat()` schema's of different types: ".concat(this.type," and ").concat(e.type));let t=this,n=e.clone();const r=Object.assign({},t.spec,n.spec);return n.spec=r,n.internalTests=Object.assign({},t.internalTests,n.internalTests),n._whitelist=t._whitelist.merge(e._whitelist,e._blacklist),n._blacklist=t._blacklist.merge(e._blacklist,e._whitelist),n.tests=t.tests,n.exclusiveTests=t.exclusiveTests,n.withMutation(t=>{e.tests.forEach(e=>{t.test(e.OPTIONS)})}),n.transforms=[...t.transforms,...n.transforms],n}isType(e){return null==e?!(!this.spec.nullable||null!==e)||!(!this.spec.optional||void 0!==e):this._typeCheck(e)}resolve(e){let t=this;if(t.conditions.length){let n=t.conditions;t=t.clone(),t.conditions=[],t=n.reduce((t,n)=>n.resolve(t,e),t),t=t.resolve(e)}return t}resolveOptions(e){var t,n,r,a;return Object.assign({},e,{from:e.from||[],strict:null!=(t=e.strict)?t:this.spec.strict,abortEarly:null!=(n=e.abortEarly)?n:this.spec.abortEarly,recursive:null!=(r=e.recursive)?r:this.spec.recursive,disableStackTrace:null!=(a=e.disableStackTrace)?a:this.spec.disableStackTrace})}cast(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.resolve(Object.assign({value:e},t)),r="ignore-optionality"===t.assert,a=n._cast(e,t);if(!1!==t.assert&&!n.isType(a)){if(r&&Pi(a))return a;let s=ui(e),o=ui(a);throw new TypeError("The value of ".concat(t.path||"field"," could not be cast to a value ")+'that satisfies the schema type: "'.concat(n.type,'". \n\n')+"attempted value: ".concat(s," \n")+(o!==s?"result of cast: ".concat(o):""))}return a}_cast(e,t){let n=void 0===e?e:this.transforms.reduce((t,n)=>n.call(this,t,e,this),e);return void 0===n&&(n=this.getDefault(t)),n}_validate(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,{path:a,originalValue:s=e,strict:o=this.spec.strict}=t,i=e;o||(i=this._cast(i,Object.assign({assert:!1},t)));let l=[];for(let c of Object.values(this.internalTests))c&&l.push(c);this.runTests({path:a,value:i,originalValue:s,options:t,tests:l},n,e=>{if(e.length)return r(e,i);this.runTests({path:a,value:i,originalValue:s,options:t,tests:this.tests},n,r)})}runTests(e,t,n){let r=!1,{tests:a,value:s,originalValue:o,path:i,options:l}=e,c=e=>{r||(r=!0,t(e,s))},u=e=>{r||(r=!0,n(e,s))},d=a.length,f=[];if(!d)return u([]);let m={value:s,originalValue:o,path:i,options:l,schema:this};for(let p=0;p<a.length;p++){(0,a[p])(m,c,function(e){e&&(Array.isArray(e)?f.push(...e):f.push(e)),--d<=0&&u(f)})}}asNestedTest(e){let{key:t,index:n,parent:r,parentPath:a,originalParent:s,options:o}=e;const i=null!=t?t:n;if(null==i)throw TypeError("Must include `key` or `index` for nested validations");const l="number"===typeof i;let c=r[i];const u=Object.assign({},o,{strict:!0,parent:r,value:c,originalValue:s[i],key:void 0,[l?"index":"key"]:i,path:l||i.includes(".")?"".concat(a||"","[").concat(l?i:'"'.concat(i,'"'),"]"):(a?"".concat(a,"."):"")+t});return(e,t,n)=>this.resolve(u)._validate(c,u,t,n)}validate(e,t){var n;let r=this.resolve(Object.assign({},t,{value:e})),a=null!=(n=null==t?void 0:t.disableStackTrace)?n:r.spec.disableStackTrace;return new Promise((n,s)=>r._validate(e,t,(e,t)=>{yi.isError(e)&&(e.value=t),s(e)},(e,t)=>{e.length?s(new yi(e,t,void 0,void 0,a)):n(t)}))}validateSync(e,t){var n;let r,a=this.resolve(Object.assign({},t,{value:e})),s=null!=(n=null==t?void 0:t.disableStackTrace)?n:a.spec.disableStackTrace;return a._validate(e,Object.assign({},t,{sync:!0}),(e,t)=>{throw yi.isError(e)&&(e.value=t),e},(t,n)=>{if(t.length)throw new yi(t,e,void 0,void 0,s);r=n}),r}isValid(e,t){return this.validate(e,t).then(()=>!0,e=>{if(yi.isError(e))return!1;throw e})}isValidSync(e,t){try{return this.validateSync(e,t),!0}catch(n){if(yi.isError(n))return!1;throw n}}_getDefault(e){let t=this.spec.default;return null==t?t:"function"===typeof t?t.call(this,e):Li(t)}getDefault(e){return this.resolve(e||{})._getDefault(e)}default(e){if(0===arguments.length)return this._getDefault();return this.clone({default:e})}strict(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this.clone({strict:e})}nullability(e,t){const n=this.clone({nullable:e});return n.internalTests.nullable=Oi({message:t,name:"nullable",test(e){return null!==e||this.schema.spec.nullable}}),n}optionality(e,t){const n=this.clone({optional:e});return n.internalTests.optionality=Oi({message:t,name:"optionality",test(e){return void 0!==e||this.schema.spec.optional}}),n}optional(){return this.optionality(!0)}defined(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:vi.defined;return this.optionality(!1,e)}nullable(){return this.nullability(!0)}nonNullable(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:vi.notNull;return this.nullability(!1,e)}required(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:vi.required;return this.clone().withMutation(t=>t.nonNullable(e).defined(e))}notRequired(){return this.clone().withMutation(e=>e.nullable().optional())}transform(e){let t=this.clone();return t.transforms.push(e),t}test(){let e;if(e=1===arguments.length?"function"===typeof(arguments.length<=0?void 0:arguments[0])?{test:arguments.length<=0?void 0:arguments[0]}:arguments.length<=0?void 0:arguments[0]:2===arguments.length?{name:arguments.length<=0?void 0:arguments[0],test:arguments.length<=1?void 0:arguments[1]}:{name:arguments.length<=0?void 0:arguments[0],message:arguments.length<=1?void 0:arguments[1],test:arguments.length<=2?void 0:arguments[2]},void 0===e.message&&(e.message=vi.default),"function"!==typeof e.test)throw new TypeError("`test` is a required parameters");let t=this.clone(),n=Oi(e),r=e.exclusive||e.name&&!0===t.exclusiveTests[e.name];if(e.exclusive&&!e.name)throw new TypeError("Exclusive tests must provide a unique `name` identifying the test");return e.name&&(t.exclusiveTests[e.name]=!!e.exclusive),t.tests=t.tests.filter(t=>{if(t.OPTIONS.name===e.name){if(r)return!1;if(t.OPTIONS.test===n.OPTIONS.test)return!1}return!0}),t.tests.push(n),t}when(e,t){Array.isArray(e)||"string"===typeof e||(t=e,e=".");let n=this.clone(),r=di(e).map(e=>new Ai(e));return r.forEach(e=>{e.isSibling&&n.deps.push(e.key)}),n.conditions.push("function"===typeof t?new Ei(r,t):Ei.fromOptions(r,t)),n}typeError(e){let t=this.clone();return t.internalTests.typeError=Oi({message:e,name:"typeError",skipAbsent:!0,test(e){return!!this.schema._typeCheck(e)||this.createError({params:{type:this.schema.type}})}}),t}oneOf(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:vi.oneOf,n=this.clone();return e.forEach(e=>{n._whitelist.add(e),n._blacklist.delete(e)}),n.internalTests.whiteList=Oi({message:t,name:"oneOf",skipAbsent:!0,test(e){let t=this.schema._whitelist,n=t.resolveAll(this.resolve);return!!n.includes(e)||this.createError({params:{values:Array.from(t).join(", "),resolved:n}})}}),n}notOneOf(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:vi.notOneOf,n=this.clone();return e.forEach(e=>{n._blacklist.add(e),n._whitelist.delete(e)}),n.internalTests.blacklist=Oi({message:t,name:"notOneOf",test(e){let t=this.schema._blacklist,n=t.resolveAll(this.resolve);return!n.includes(e)||this.createError({params:{values:Array.from(t).join(", "),resolved:n}})}}),n}strip(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=this.clone();return t.spec.strip=e,t}describe(e){const t=(e?this.resolve(e):this).clone(),{label:n,meta:r,optional:a,nullable:s}=t.spec,o={meta:r,label:n,optional:a,nullable:s,default:t.getDefault(e),type:t.type,oneOf:t._whitelist.describe(),notOneOf:t._blacklist.describe(),tests:t.tests.map(e=>({name:e.OPTIONS.name,params:e.OPTIONS.params})).filter((e,t,n)=>n.findIndex(t=>t.name===e.name)===t)};return o}}Di.prototype.__isYupSchema__=!0;for(const n of["validate","validateSync"])Di.prototype["".concat(n,"At")]=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{parent:a,parentPath:s,schema:o}=Ri(this,e,t,r.context);return o[n](a&&a[s],Object.assign({},r,{parent:a,path:e}))};for(const n of["equals","is"])Di.prototype[n]=Di.prototype.oneOf;for(const n of["not","nope"])Di.prototype[n]=Di.prototype.notOneOf;const zi=()=>!0;class Mi extends Di{constructor(e){super("function"===typeof e?{type:"mixed",check:e}:Object.assign({type:"mixed",check:zi},e))}}Mi.prototype;class Ii extends Di{constructor(){super({type:"boolean",check:e=>(e instanceof Boolean&&(e=e.valueOf()),"boolean"===typeof e)}),this.withMutation(()=>{this.transform((e,t,n)=>{if(n.spec.coerce&&!n.isType(e)){if(/^(true|1)$/i.test(String(e)))return!0;if(/^(false|0)$/i.test(String(e)))return!1}return e})})}isTrue(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ki.isValue;return this.test({message:e,name:"is-value",exclusive:!0,params:{value:"true"},test:e=>Pi(e)||!0===e})}isFalse(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ki.isValue;return this.test({message:e,name:"is-value",exclusive:!0,params:{value:"false"},test:e=>Pi(e)||!1===e})}default(e){return super.default(e)}defined(e){return super.defined(e)}optional(){return super.optional()}required(e){return super.required(e)}notRequired(){return super.notRequired()}nullable(){return super.nullable()}nonNullable(e){return super.nonNullable(e)}strip(e){return super.strip(e)}}Ii.prototype;const Ui=/^(\d{4}|[+-]\d{6})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:[ T]?(\d{2}):?(\d{2})(?::?(\d{2})(?:[,.](\d{1,}))?)?(?:(Z)|([+-])(\d{2})(?::?(\d{2}))?)?)?$/;function Vi(e){var t,n;const r=Ui.exec(e);return r?{year:Bi(r[1]),month:Bi(r[2],1)-1,day:Bi(r[3],1),hour:Bi(r[4]),minute:Bi(r[5]),second:Bi(r[6]),millisecond:r[7]?Bi(r[7].substring(0,3)):0,precision:null!=(t=null==(n=r[7])?void 0:n.length)?t:void 0,z:r[8]||void 0,plusMinus:r[9]||void 0,hourOffset:Bi(r[10]),minuteOffset:Bi(r[11])}:null}function Bi(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Number(e)||t}let qi=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,$i=/^((https?|ftp):)?\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i,Hi=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Wi=new RegExp("".concat("^\\d{4}-\\d{2}-\\d{2}","T").concat("\\d{2}:\\d{2}:\\d{2}","(\\.\\d+)?").concat("(([+-]\\d{2}(:?\\d{2})?)|Z)","$")),Qi=e=>Pi(e)||e===e.trim(),Ki={}.toString();function Yi(){return new Xi}class Xi extends Di{constructor(){super({type:"string",check:e=>(e instanceof String&&(e=e.valueOf()),"string"===typeof e)}),this.withMutation(()=>{this.transform((e,t,n)=>{if(!n.spec.coerce||n.isType(e))return e;if(Array.isArray(e))return e;const r=null!=e&&e.toString?e.toString():e;return r===Ki?e:r})})}required(e){return super.required(e).withMutation(t=>t.test({message:e||vi.required,name:"required",skipAbsent:!0,test:e=>!!e.length}))}notRequired(){return super.notRequired().withMutation(e=>(e.tests=e.tests.filter(e=>"required"!==e.OPTIONS.name),e))}length(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:bi.length;return this.test({message:t,name:"length",exclusive:!0,params:{length:e},skipAbsent:!0,test(t){return t.length===this.resolve(e)}})}min(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:bi.min;return this.test({message:t,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(t){return t.length>=this.resolve(e)}})}max(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:bi.max;return this.test({name:"max",exclusive:!0,message:t,params:{max:e},skipAbsent:!0,test(t){return t.length<=this.resolve(e)}})}matches(e,t){let n,r,a=!1;return t&&("object"===typeof t?({excludeEmptyString:a=!1,message:n,name:r}=t):n=t),this.test({name:r||"matches",message:n||bi.matches,params:{regex:e},skipAbsent:!0,test:t=>""===t&&a||-1!==t.search(e)})}email(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:bi.email;return this.matches(qi,{name:"email",message:e,excludeEmptyString:!0})}url(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:bi.url;return this.matches($i,{name:"url",message:e,excludeEmptyString:!0})}uuid(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:bi.uuid;return this.matches(Hi,{name:"uuid",message:e,excludeEmptyString:!1})}datetime(e){let t,n,r="";return e&&("object"===typeof e?({message:r="",allowOffset:t=!1,precision:n}=e):r=e),this.matches(Wi,{name:"datetime",message:r||bi.datetime,excludeEmptyString:!0}).test({name:"datetime_offset",message:r||bi.datetime_offset,params:{allowOffset:t},skipAbsent:!0,test:e=>{if(!e||t)return!0;const n=Vi(e);return!!n&&!!n.z}}).test({name:"datetime_precision",message:r||bi.datetime_precision,params:{precision:n},skipAbsent:!0,test:e=>{if(!e||void 0==n)return!0;const t=Vi(e);return!!t&&t.precision===n}})}ensure(){return this.default("").transform(e=>null===e?"":e)}trim(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:bi.trim;return this.transform(e=>null!=e?e.trim():e).test({message:e,name:"trim",test:Qi})}lowercase(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:bi.lowercase;return this.transform(e=>Pi(e)?e:e.toLowerCase()).test({message:e,name:"string_case",exclusive:!0,skipAbsent:!0,test:e=>Pi(e)||e===e.toLowerCase()})}uppercase(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:bi.uppercase;return this.transform(e=>Pi(e)?e:e.toUpperCase()).test({message:e,name:"string_case",exclusive:!0,skipAbsent:!0,test:e=>Pi(e)||e===e.toUpperCase()})}}Yi.prototype=Xi.prototype;class Ji extends Di{constructor(){super({type:"number",check:e=>(e instanceof Number&&(e=e.valueOf()),"number"===typeof e&&!(e=>e!=+e)(e))}),this.withMutation(()=>{this.transform((e,t,n)=>{if(!n.spec.coerce)return e;let r=e;if("string"===typeof r){if(r=r.replace(/\s/g,""),""===r)return NaN;r=+r}return n.isType(r)||null===r?r:parseFloat(r)})})}min(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:xi.min;return this.test({message:t,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(t){return t>=this.resolve(e)}})}max(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:xi.max;return this.test({message:t,name:"max",exclusive:!0,params:{max:e},skipAbsent:!0,test(t){return t<=this.resolve(e)}})}lessThan(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:xi.lessThan;return this.test({message:t,name:"max",exclusive:!0,params:{less:e},skipAbsent:!0,test(t){return t<this.resolve(e)}})}moreThan(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:xi.moreThan;return this.test({message:t,name:"min",exclusive:!0,params:{more:e},skipAbsent:!0,test(t){return t>this.resolve(e)}})}positive(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:xi.positive;return this.moreThan(0,e)}negative(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:xi.negative;return this.lessThan(0,e)}integer(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:xi.integer;return this.test({name:"integer",message:e,skipAbsent:!0,test:e=>Number.isInteger(e)})}truncate(){return this.transform(e=>Pi(e)?e:0|e)}round(e){var t;let n=["ceil","floor","round","trunc"];if("trunc"===(e=(null==(t=e)?void 0:t.toLowerCase())||"round"))return this.truncate();if(-1===n.indexOf(e.toLowerCase()))throw new TypeError("Only valid options for round() are: "+n.join(", "));return this.transform(t=>Pi(t)?t:Math[e](t))}}Ji.prototype;let Gi=new Date("");function Zi(){return new el}class el extends Di{constructor(){super({type:"date",check(e){return t=e,"[object Date]"===Object.prototype.toString.call(t)&&!isNaN(e.getTime());var t}}),this.withMutation(()=>{this.transform((e,t,n)=>!n.spec.coerce||n.isType(e)||null===e?e:(e=function(e){const t=Vi(e);if(!t)return Date.parse?Date.parse(e):Number.NaN;if(void 0===t.z&&void 0===t.plusMinus)return new Date(t.year,t.month,t.day,t.hour,t.minute,t.second,t.millisecond).valueOf();let n=0;return"Z"!==t.z&&void 0!==t.plusMinus&&(n=60*t.hourOffset+t.minuteOffset,"+"===t.plusMinus&&(n=0-n)),Date.UTC(t.year,t.month,t.day,t.hour,t.minute+n,t.second,t.millisecond)}(e),isNaN(e)?el.INVALID_DATE:new Date(e)))})}prepareParam(e,t){let n;if(Ai.isRef(e))n=e;else{let r=this.cast(e);if(!this._typeCheck(r))throw new TypeError("`".concat(t,"` must be a Date or a value that can be `cast()` to a Date"));n=r}return n}min(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wi.min,n=this.prepareParam(e,"min");return this.test({message:t,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(e){return e>=this.resolve(n)}})}max(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wi.max,n=this.prepareParam(e,"max");return this.test({message:t,name:"max",exclusive:!0,params:{max:e},skipAbsent:!0,test(e){return e<=this.resolve(n)}})}}function tl(e,t){let n=1/0;return e.some((e,r)=>{var a;if(null!=(a=t.path)&&a.includes(e))return n=r,!0}),n}function nl(e){return(t,n)=>tl(e,t)-tl(e,n)}el.INVALID_DATE=Gi,Zi.prototype=el.prototype,Zi.INVALID_DATE=Gi;const rl=(e,t,n)=>{if("string"!==typeof e)return e;let r=e;try{r=JSON.parse(e)}catch(a){}return n.isType(r)?r:e};function al(e){if("fields"in e){const t={};for(const[n,r]of Object.entries(e.fields))t[n]=al(r);return e.setFields(t)}if("array"===e.type){const t=e.optional();return t.innerType&&(t.innerType=al(t.innerType)),t}return"tuple"===e.type?e.optional().clone({types:e.spec.types.map(al)}):"optional"in e?e.optional():e}let sl=e=>"[object Object]"===Object.prototype.toString.call(e);function ol(e,t){let n=Object.keys(e.fields);return Object.keys(t).filter(e=>-1===n.indexOf(e))}const il=nl([]);function ll(e){return new cl(e)}class cl extends Di{constructor(e){super({type:"object",check:e=>sl(e)||"function"===typeof e}),this.fields=Object.create(null),this._sortErrors=il,this._nodes=[],this._excludedEdges=[],this.withMutation(()=>{e&&this.shape(e)})}_cast(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var n;let r=super._cast(e,t);if(void 0===r)return this.getDefault(t);if(!this._typeCheck(r))return r;let a=this.fields,s=null!=(n=t.stripUnknown)?n:this.spec.noUnknown,o=[].concat(this._nodes,Object.keys(r).filter(e=>!this._nodes.includes(e))),i={},l=Object.assign({},t,{parent:i,__validating:t.__validating||!1}),c=!1;for(const u of o){let e=a[u],n=u in r;if(e){let n,a=r[u];l.path=(t.path?"".concat(t.path,"."):"")+u,e=e.resolve({value:a,context:t.context,parent:i});let s=e instanceof Di?e.spec:void 0,o=null==s?void 0:s.strict;if(null!=s&&s.strip){c=c||u in r;continue}n=t.__validating&&o?r[u]:e.cast(r[u],l),void 0!==n&&(i[u]=n)}else n&&!s&&(i[u]=r[u]);n===u in i&&i[u]===r[u]||(c=!0)}return c?i:r}_validate(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,{from:a=[],originalValue:s=e,recursive:o=this.spec.recursive}=t;t.from=[{schema:this,value:s},...a],t.__validating=!0,t.originalValue=s,super._validate(e,t,n,(e,a)=>{if(!o||!sl(a))return void r(e,a);s=s||a;let i=[];for(let n of this._nodes){let e=this.fields[n];e&&!Ai.isRef(e)&&i.push(e.asNestedTest({options:t,key:n,parent:a,parentPath:t.path,originalParent:s}))}this.runTests({tests:i,value:a,originalValue:s,options:t},n,t=>{r(t.sort(this._sortErrors).concat(e),a)})})}clone(e){const t=super.clone(e);return t.fields=Object.assign({},this.fields),t._nodes=this._nodes,t._excludedEdges=this._excludedEdges,t._sortErrors=this._sortErrors,t}concat(e){let t=super.concat(e),n=t.fields;for(let[r,a]of Object.entries(this.fields)){const e=n[r];n[r]=void 0===e?a:e}return t.withMutation(t=>t.setFields(n,[...this._excludedEdges,...e._excludedEdges]))}_getDefault(e){if("default"in this.spec)return super._getDefault(e);if(!this._nodes.length)return;let t={};return this._nodes.forEach(n=>{var r;const a=this.fields[n];let s=e;null!=(r=s)&&r.value&&(s=Object.assign({},s,{parent:s.value,value:s.value[n]})),t[n]=a&&"getDefault"in a?a.getDefault(s):void 0}),t}setFields(e,t){let n=this.clone();return n.fields=e,n._nodes=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[],r=new Set,a=new Set(t.map(e=>{let[t,n]=e;return"".concat(t,"-").concat(n)}));function s(e,t){let s=(0,ei.split)(e)[0];r.add(s),a.has("".concat(t,"-").concat(s))||n.push([t,s])}for(const o of Object.keys(e)){let t=e[o];r.add(o),Ai.isRef(t)&&t.isSibling?s(t.path,o):_i(t)&&"deps"in t&&t.deps.forEach(e=>s(e,o))}return ri().array(Array.from(r),n).reverse()}(e,t),n._sortErrors=nl(Object.keys(e)),t&&(n._excludedEdges=t),n}shape(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return this.clone().withMutation(n=>{let r=n._excludedEdges;return t.length&&(Array.isArray(t[0])||(t=[t]),r=[...n._excludedEdges,...t]),n.setFields(Object.assign(n.fields,e),r)})}partial(){const e={};for(const[t,n]of Object.entries(this.fields))e[t]="optional"in n&&n.optional instanceof Function?n.optional():n;return this.setFields(e)}deepPartial(){return al(this)}pick(e){const t={};for(const n of e)this.fields[n]&&(t[n]=this.fields[n]);return this.setFields(t,this._excludedEdges.filter(t=>{let[n,r]=t;return e.includes(n)&&e.includes(r)}))}omit(e){const t=[];for(const n of Object.keys(this.fields))e.includes(n)||t.push(n);return this.pick(t)}from(e,t,n){let r=(0,ei.getter)(e,!0);return this.transform(a=>{if(!a)return a;let s=a;return((e,t)=>{const n=[...(0,ei.normalizePath)(t)];if(1===n.length)return n[0]in e;let r=n.pop(),a=(0,ei.getter)((0,ei.join)(n),!0)(e);return!(!a||!(r in a))})(a,e)&&(s=Object.assign({},a),n||delete s[e],s[t]=r(a)),s})}json(){return this.transform(rl)}exact(e){return this.test({name:"exact",exclusive:!0,message:e||Ni.exact,test(e){if(null==e)return!0;const t=ol(this.schema,e);return 0===t.length||this.createError({params:{properties:t.join(", ")}})}})}stripUnknown(){return this.clone({noUnknown:!0})}noUnknown(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Ni.noUnknown;"boolean"!==typeof e&&(t=e,e=!0);let n=this.test({name:"noUnknown",exclusive:!0,message:t,test(t){if(null==t)return!0;const n=ol(this.schema,t);return!e||0===n.length||this.createError({params:{unknown:n.join(", ")}})}});return n.spec.noUnknown=e,n}unknown(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Ni.noUnknown;return this.noUnknown(!e,t)}transformKeys(e){return this.transform(t=>{if(!t)return t;const n={};for(const r of Object.keys(t))n[e(r)]=t[r];return n})}camelCase(){return this.transformKeys(ti.camelCase)}snakeCase(){return this.transformKeys(ti.snakeCase)}constantCase(){return this.transformKeys(e=>(0,ti.snakeCase)(e).toUpperCase())}describe(e){const t=(e?this.resolve(e):this).clone(),n=super.describe(e);n.fields={};for(const[a,s]of Object.entries(t.fields)){var r;let t=e;null!=(r=t)&&r.value&&(t=Object.assign({},t,{parent:t.value,value:t.value[a]})),n.fields[a]=s.describe(t)}return n}}ll.prototype=cl.prototype;class ul extends Di{constructor(e){super({type:"array",spec:{types:e},check:e=>Array.isArray(e)}),this.innerType=void 0,this.innerType=e}_cast(e,t){const n=super._cast(e,t);if(!this._typeCheck(n)||!this.innerType)return n;let r=!1;const a=n.map((e,n)=>{const a=this.innerType.cast(e,Object.assign({},t,{path:"".concat(t.path||"","[").concat(n,"]")}));return a!==e&&(r=!0),a});return r?a:n}_validate(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;var a;let s=this.innerType,o=null!=(a=t.recursive)?a:this.spec.recursive;null!=t.originalValue&&t.originalValue,super._validate(e,t,n,(a,i)=>{var l;if(!o||!s||!this._typeCheck(i))return void r(a,i);let c=new Array(i.length);for(let n=0;n<i.length;n++){var u;c[n]=s.asNestedTest({options:t,index:n,parent:i,parentPath:t.path,originalParent:null!=(u=t.originalValue)?u:e})}this.runTests({value:i,tests:c,originalValue:null!=(l=t.originalValue)?l:e,options:t},n,e=>r(e.concat(a),i))})}clone(e){const t=super.clone(e);return t.innerType=this.innerType,t}json(){return this.transform(rl)}concat(e){let t=super.concat(e);return t.innerType=this.innerType,e.innerType&&(t.innerType=t.innerType?t.innerType.concat(e.innerType):e.innerType),t}of(e){let t=this.clone();if(!_i(e))throw new TypeError("`array.of()` sub-schema must be a valid yup schema not: "+ui(e));return t.innerType=e,t.spec=Object.assign({},t.spec,{types:e}),t}length(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ji.length;return this.test({message:t,name:"length",exclusive:!0,params:{length:e},skipAbsent:!0,test(t){return t.length===this.resolve(e)}})}min(e,t){return t=t||ji.min,this.test({message:t,name:"min",exclusive:!0,params:{min:e},skipAbsent:!0,test(t){return t.length>=this.resolve(e)}})}max(e,t){return t=t||ji.max,this.test({message:t,name:"max",exclusive:!0,params:{max:e},skipAbsent:!0,test(t){return t.length<=this.resolve(e)}})}ensure(){return this.default(()=>[]).transform((e,t)=>this._typeCheck(e)?e:null==t?[]:[].concat(t))}compact(e){let t=e?(t,n,r)=>!e(t,n,r):e=>!!e;return this.transform(e=>null!=e?e.filter(t):e)}describe(e){const t=(e?this.resolve(e):this).clone(),n=super.describe(e);if(t.innerType){var r;let a=e;null!=(r=a)&&r.value&&(a=Object.assign({},a,{parent:a.value,value:a.value[0]})),n.innerType=t.innerType.describe(a)}return n}}ul.prototype;class dl extends Di{constructor(e){super({type:"tuple",spec:{types:e},check(e){const t=this.spec.types;return Array.isArray(e)&&e.length===t.length}}),this.withMutation(()=>{this.typeError(Si.notType)})}_cast(e,t){const{types:n}=this.spec,r=super._cast(e,t);if(!this._typeCheck(r))return r;let a=!1;const s=n.map((e,n)=>{const s=e.cast(r[n],Object.assign({},t,{path:"".concat(t.path||"","[").concat(n,"]")}));return s!==r[n]&&(a=!0),s});return a?s:r}_validate(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,a=this.spec.types;super._validate(e,t,n,(s,o)=>{var i;if(!this._typeCheck(o))return void r(s,o);let l=[];for(let[n,r]of a.entries()){var c;l[n]=r.asNestedTest({options:t,index:n,parent:o,parentPath:t.path,originalParent:null!=(c=t.originalValue)?c:e})}this.runTests({value:o,tests:l,originalValue:null!=(i=t.originalValue)?i:e,options:t},n,e=>r(e.concat(s),o))})}describe(e){const t=(e?this.resolve(e):this).clone(),n=super.describe(e);return n.innerType=t.spec.types.map((t,n)=>{var r;let a=e;return null!=(r=a)&&r.value&&(a=Object.assign({},a,{parent:a.value,value:a.value[n]})),t.describe(a)}),n}}dl.prototype;const fl=ds("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]),ml=ds("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),pl=ds("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),hl=ll({username:Yi().required("Username is required"),password:Yi().required("Password is required")}),gl=()=>{const{login:e,isAuthenticated:n,loading:r}=ts(),a=me(),s=ue(),[o,i]=(0,t.useState)(!1),[l,u]=(0,t.useState)(!1),{register:d,handleSubmit:f,formState:{errors:m}}=Qo({resolver:Zo(hl)});if((0,t.useEffect)(()=>{n&&a("/dashboard")},[n,a]),(0,t.useEffect)(()=>{var e;null!==(e=s.state)&&void 0!==e&&e.message&&(en.success(s.state.message),a(s.pathname,{replace:!0}))},[s.state,a,s.pathname]),r)return(0,Za.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,Za.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})});if(n)return(0,Za.jsx)(Ae,{to:"/dashboard",replace:!0});return(0,Za.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,Za.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100",children:(0,Za.jsx)(fl,{className:"h-6 w-6 text-blue-600"})}),(0,Za.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"IDFC Agent Portal"}),(0,Za.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Sign in to your account"})]}),(0,Za.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:f(async t=>{u(!0);try{(await e(t)).success&&a("/dashboard")}catch(n){console.error("Login error:",n)}finally{u(!1)}}),children:[(0,Za.jsxs)("div",{className:"space-y-4",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700",children:"Username"}),(0,Za.jsx)("input",c(c({},d("username")),{},{type:"text",className:"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your username"})),m.username&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:m.username.message})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,Za.jsxs)("div",{className:"mt-1 relative",children:[(0,Za.jsx)("input",c(c({},d("password")),{},{type:o?"text":"password",className:"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Enter your password"})),(0,Za.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>i(!o),children:o?(0,Za.jsx)(ml,{className:"h-5 w-5 text-gray-400"}):(0,Za.jsx)(pl,{className:"h-5 w-5 text-gray-400"})})]}),m.password&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:m.password.message})]})]}),(0,Za.jsx)("div",{children:(0,Za.jsx)("button",{type:"submit",disabled:l,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:l?(0,Za.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):"Sign in"})}),(0,Za.jsxs)("div",{className:"text-center space-y-4",children:[(0,Za.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,Za.jsxs)("p",{className:"text-sm text-gray-600",children:["New agent?",(0,Za.jsx)(yt,{to:"/register",className:"ml-1 font-medium text-blue-600 hover:text-blue-500",children:"Register here"})]})}),(0,Za.jsxs)("p",{className:"text-sm text-gray-600",children:["Demo credentials: ",(0,Za.jsx)("br",{}),"Username: ",(0,Za.jsx)("strong",{children:"admin"})," ",(0,Za.jsx)("br",{}),"Password: ",(0,Za.jsx)("strong",{children:"Admin@123"})]})]})]})]})})},yl=ds("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),vl=ds("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),bl=ds("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),xl=ds("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),wl=ds("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),kl=()=>{const{user:e,isAgent:n,isAdmin:r,isReviewer:a}=ts(),[s,o]=(0,t.useState)({totalAgents:0,pendingAgents:0,approvedAgents:0,rejectedAgents:0}),[i,l]=(0,t.useState)(!0),[c,u]=(0,t.useState)([]);(0,t.useEffect)(()=>{(async()=>{try{if(r()||a()){const e=(await Va(1,50)).data||[];o({totalAgents:e.length,pendingAgents:e.filter(e=>"Pending"===e.status).length,approvedAgents:e.filter(e=>"Approved"===e.status).length,rejectedAgents:e.filter(e=>"Rejected"===e.status).length}),u(e.slice(0,5))}else if(n()&&e.agentId){const t=await Ba(e.agentId);u([t])}}catch(t){console.error("Error fetching dashboard data:",t)}finally{l(!1)}})()},[e,n,r,a]);const d=e=>{switch(e){case"Approved":return(0,Za.jsx)(yl,{className:"h-5 w-5 text-green-500"});case"Pending":return(0,Za.jsx)(vl,{className:"h-5 w-5 text-yellow-500"});case"Rejected":return(0,Za.jsx)(bl,{className:"h-5 w-5 text-red-500"});default:return(0,Za.jsx)(xl,{className:"h-5 w-5 text-gray-500"})}},f=e=>{switch(e){case"Approved":return"bg-green-100 text-green-800";case"Pending":return"bg-yellow-100 text-yellow-800";case"Rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return i?(0,Za.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,Za.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):(0,Za.jsxs)("div",{className:"space-y-6",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,Za.jsxs)("p",{className:"mt-1 text-sm text-gray-600",children:["Welcome back, ",null===e||void 0===e?void 0:e.username,"! Here's what's happening with your agents."]})]}),(r()||a())&&(0,Za.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,Za.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,Za.jsx)("div",{className:"p-5",children:(0,Za.jsxs)("div",{className:"flex items-center",children:[(0,Za.jsx)("div",{className:"flex-shrink-0",children:(0,Za.jsx)(ps,{className:"h-6 w-6 text-gray-400"})}),(0,Za.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,Za.jsxs)("dl",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Total Agents"}),(0,Za.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:s.totalAgents})]})})]})})}),(0,Za.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,Za.jsx)("div",{className:"p-5",children:(0,Za.jsxs)("div",{className:"flex items-center",children:[(0,Za.jsx)("div",{className:"flex-shrink-0",children:(0,Za.jsx)(vl,{className:"h-6 w-6 text-yellow-400"})}),(0,Za.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,Za.jsxs)("dl",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Pending Review"}),(0,Za.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:s.pendingAgents})]})})]})})}),(0,Za.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,Za.jsx)("div",{className:"p-5",children:(0,Za.jsxs)("div",{className:"flex items-center",children:[(0,Za.jsx)("div",{className:"flex-shrink-0",children:(0,Za.jsx)(yl,{className:"h-6 w-6 text-green-400"})}),(0,Za.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,Za.jsxs)("dl",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Approved"}),(0,Za.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:s.approvedAgents})]})})]})})}),(0,Za.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,Za.jsx)("div",{className:"p-5",children:(0,Za.jsxs)("div",{className:"flex items-center",children:[(0,Za.jsx)("div",{className:"flex-shrink-0",children:(0,Za.jsx)(bl,{className:"h-6 w-6 text-red-400"})}),(0,Za.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,Za.jsxs)("dl",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Rejected"}),(0,Za.jsx)("dd",{className:"text-lg font-medium text-gray-900",children:s.rejectedAgents})]})})]})})})]}),(0,Za.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,Za.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,Za.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Quick Actions"}),(0,Za.jsxs)("div",{className:"mt-5 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:[n()&&(0,Za.jsxs)(yt,{to:"/register",className:"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-300 hover:border-blue-500",children:[(0,Za.jsx)("div",{children:(0,Za.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white",children:(0,Za.jsx)(ms,{className:"h-6 w-6"})})}),(0,Za.jsxs)("div",{className:"mt-8",children:[(0,Za.jsxs)("h3",{className:"text-lg font-medium",children:[(0,Za.jsx)("span",{className:"absolute inset-0","aria-hidden":"true"}),"Register as Agent"]}),(0,Za.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Complete your agent registration process"})]})]}),(r()||a())&&(0,Za.jsxs)(yt,{to:"/agents",className:"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-300 hover:border-blue-500",children:[(0,Za.jsx)("div",{children:(0,Za.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white",children:(0,Za.jsx)(ps,{className:"h-6 w-6"})})}),(0,Za.jsxs)("div",{className:"mt-8",children:[(0,Za.jsxs)("h3",{className:"text-lg font-medium",children:[(0,Za.jsx)("span",{className:"absolute inset-0","aria-hidden":"true"}),"Manage Agents"]}),(0,Za.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Review and manage agent applications"})]})]}),(0,Za.jsxs)(yt,{to:"/profile",className:"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-300 hover:border-blue-500",children:[(0,Za.jsx)("div",{children:(0,Za.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white",children:(0,Za.jsx)(wl,{className:"h-6 w-6"})})}),(0,Za.jsxs)("div",{className:"mt-8",children:[(0,Za.jsxs)("h3",{className:"text-lg font-medium",children:[(0,Za.jsx)("span",{className:"absolute inset-0","aria-hidden":"true"}),"View Profile"]}),(0,Za.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Manage your account settings"})]})]})]})]})}),c.length>0&&(0,Za.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,Za.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,Za.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:n()?"Your Application Status":"Recent Agents"}),(0,Za.jsx)("div",{className:"mt-5",children:(0,Za.jsx)("div",{className:"flow-root",children:(0,Za.jsx)("ul",{className:"-my-5 divide-y divide-gray-200",children:c.map(e=>(0,Za.jsx)("li",{className:"py-4",children:(0,Za.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,Za.jsx)("div",{className:"flex-shrink-0",children:d(e.status)}),(0,Za.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,Za.jsxs)("p",{className:"text-sm font-medium text-gray-900 truncate",children:[e.firstName," ",e.lastName]}),(0,Za.jsx)("p",{className:"text-sm text-gray-500 truncate",children:e.email})]}),(0,Za.jsx)("div",{children:(0,Za.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(f(e.status)),children:e.status})})]})},e.agentId))})})})]})})]})},Nl=ds("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),jl=ds("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),Sl=ds("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),_l=ds("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),El=ds("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),Tl=ds("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),Cl=ll({firstName:Yi().required("First name is required").min(2,"Minimum 2 characters"),lastName:Yi().required("Last name is required").min(2,"Minimum 2 characters"),email:Yi().email("Invalid email format").required("Email is required"),phoneNumber:Yi().matches(/^[0-9]{10}$/,"Phone number must be 10 digits").required("Phone number is required"),aadharNumber:Yi().matches(/^[0-9]{12}$/,"Aadhar number must be 12 digits").required("Aadhar number is required"),panNumber:Yi().matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,"Invalid PAN format").required("PAN number is required"),dateOfBirth:Zi().max(new Date(Date.now()-567648e6),"Must be at least 18 years old").required("Date of birth is required"),address:Yi().required("Address is required").min(10,"Address too short"),city:Yi().required("City is required"),state:Yi().required("State is required"),pinCode:Yi().matches(/^[0-9]{6}$/,"PIN code must be 6 digits").required("PIN code is required")}),Al=()=>{const e=me(),n=ue(),[r,a]=(0,t.useState)(!1),[s,o]=(0,t.useState)(1),[i,l]=(0,t.useState)({aadharCard:null,panCard:null,photo:null,bankPassbook:null,businessProof:null}),{register:u,handleSubmit:d,formState:{errors:f},trigger:m,getValues:p}=Qo({resolver:Zo(Cl),mode:"onChange"}),h=[{id:1,name:"Personal Information",icon:gs},{id:2,name:"Address Details",icon:Nl},{id:3,name:"Document Upload",icon:jl},{id:4,name:"Review & Submit",icon:hs}];return(0,Za.jsxs)("div",{className:"space-y-6",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Agent Registration"}),(0,Za.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Complete your agent registration to become an IDFC FASTag agent."})]}),(0,Za.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,Za.jsx)("nav",{"aria-label":"Progress",children:(0,Za.jsx)("ol",{className:"flex items-center",children:h.map((e,t)=>{const n=e.icon,r=s>e.id,a=s===e.id;return(0,Za.jsx)("li",{className:"".concat(t!==h.length-1?"flex-1":""),children:(0,Za.jsxs)("div",{className:"flex items-center ".concat(t!==h.length-1?"w-full":""),children:[(0,Za.jsxs)("div",{className:"flex items-center",children:[(0,Za.jsx)("div",{className:"\n                        flex items-center justify-center w-10 h-10 rounded-full border-2\n                        ".concat(r?"bg-blue-600 border-blue-600 text-white":a?"border-blue-600 text-blue-600":"border-gray-300 text-gray-500","\n                      "),children:(0,Za.jsx)(n,{className:"w-5 h-5"})}),(0,Za.jsx)("span",{className:"ml-3 text-sm font-medium ".concat(a?"text-blue-600":r?"text-gray-900":"text-gray-500"),children:e.name})]}),t!==h.length-1&&(0,Za.jsx)("div",{className:"flex-1 ml-4 h-0.5 ".concat(r?"bg-blue-600":"bg-gray-300")})]})},e.name)})})})}),(0,Za.jsxs)("form",{onSubmit:d(async t=>{a(!0);try{const r=c(c({},t),{},{dateOfBirth:new Date(t.dateOfBirth).toISOString()});(await Ua(r)).success&&(en.success("Agent registration submitted successfully!"),"/register"===n.pathname?e("/login",{state:{message:"Registration successful! Please login with your credentials."}}):e("/agents"))}catch(o){var r,s;console.error("Registration error:",o);const e=(null===(r=o.response)||void 0===r||null===(s=r.data)||void 0===s?void 0:s.message)||"Registration failed. Please try again.";en.error(e)}finally{a(!1)}}),className:"bg-white shadow rounded-lg p-6",children:[1===s&&(0,Za.jsxs)("div",{className:"space-y-6",children:[(0,Za.jsx)("div",{children:(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,Za.jsx)(gs,{className:"w-5 h-5 mr-2"}),"Personal Information"]})}),(0,Za.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"First Name *"}),(0,Za.jsx)("input",c(c({},u("firstName")),{},{type:"text",className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your first name"})),f.firstName&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f.firstName.message})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Last Name *"}),(0,Za.jsx)("input",c(c({},u("lastName")),{},{type:"text",className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your last name"})),f.lastName&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f.lastName.message})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Email Address *"}),(0,Za.jsxs)("div",{className:"mt-1 relative",children:[(0,Za.jsx)("input",c(c({},u("email")),{},{type:"email",className:"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your email"})),(0,Za.jsx)(Sl,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),f.email&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f.email.message})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Phone Number *"}),(0,Za.jsxs)("div",{className:"mt-1 relative",children:[(0,Za.jsx)("input",c(c({},u("phoneNumber")),{},{type:"tel",className:"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"10-digit mobile number"})),(0,Za.jsx)(_l,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),f.phoneNumber&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f.phoneNumber.message})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Date of Birth *"}),(0,Za.jsxs)("div",{className:"mt-1 relative",children:[(0,Za.jsx)("input",c(c({},u("dateOfBirth")),{},{type:"date",className:"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})),(0,Za.jsx)(El,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),f.dateOfBirth&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f.dateOfBirth.message})]})]})]}),2===s&&(0,Za.jsxs)("div",{className:"space-y-6",children:[(0,Za.jsx)("div",{children:(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,Za.jsx)(Nl,{className:"w-5 h-5 mr-2"}),"Address & Identity Details"]})}),(0,Za.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Aadhar Number *"}),(0,Za.jsxs)("div",{className:"mt-1 relative",children:[(0,Za.jsx)("input",c(c({},u("aadharNumber")),{},{type:"text",maxLength:"12",className:"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"12-digit Aadhar number"})),(0,Za.jsx)(Tl,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),f.aadharNumber&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f.aadharNumber.message})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"PAN Number *"}),(0,Za.jsxs)("div",{className:"mt-1 relative",children:[(0,Za.jsx)("input",c(c({},u("panNumber")),{},{type:"text",maxLength:"10",className:"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"**********",style:{textTransform:"uppercase"}})),(0,Za.jsx)(Tl,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),f.panNumber&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f.panNumber.message})]}),(0,Za.jsxs)("div",{className:"sm:col-span-2",children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Address *"}),(0,Za.jsx)("textarea",c(c({},u("address")),{},{rows:3,className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your complete address"})),f.address&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f.address.message})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"City *"}),(0,Za.jsx)("input",c(c({},u("city")),{},{type:"text",className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your city"})),f.city&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f.city.message})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"State *"}),(0,Za.jsxs)("select",c(c({},u("state")),{},{className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:[(0,Za.jsx)("option",{value:"",children:"Select State"}),(0,Za.jsx)("option",{value:"Andhra Pradesh",children:"Andhra Pradesh"}),(0,Za.jsx)("option",{value:"Arunachal Pradesh",children:"Arunachal Pradesh"}),(0,Za.jsx)("option",{value:"Assam",children:"Assam"}),(0,Za.jsx)("option",{value:"Bihar",children:"Bihar"}),(0,Za.jsx)("option",{value:"Chhattisgarh",children:"Chhattisgarh"}),(0,Za.jsx)("option",{value:"Goa",children:"Goa"}),(0,Za.jsx)("option",{value:"Gujarat",children:"Gujarat"}),(0,Za.jsx)("option",{value:"Haryana",children:"Haryana"}),(0,Za.jsx)("option",{value:"Himachal Pradesh",children:"Himachal Pradesh"}),(0,Za.jsx)("option",{value:"Jharkhand",children:"Jharkhand"}),(0,Za.jsx)("option",{value:"Karnataka",children:"Karnataka"}),(0,Za.jsx)("option",{value:"Kerala",children:"Kerala"}),(0,Za.jsx)("option",{value:"Madhya Pradesh",children:"Madhya Pradesh"}),(0,Za.jsx)("option",{value:"Maharashtra",children:"Maharashtra"}),(0,Za.jsx)("option",{value:"Manipur",children:"Manipur"}),(0,Za.jsx)("option",{value:"Meghalaya",children:"Meghalaya"}),(0,Za.jsx)("option",{value:"Mizoram",children:"Mizoram"}),(0,Za.jsx)("option",{value:"Nagaland",children:"Nagaland"}),(0,Za.jsx)("option",{value:"Odisha",children:"Odisha"}),(0,Za.jsx)("option",{value:"Punjab",children:"Punjab"}),(0,Za.jsx)("option",{value:"Rajasthan",children:"Rajasthan"}),(0,Za.jsx)("option",{value:"Sikkim",children:"Sikkim"}),(0,Za.jsx)("option",{value:"Tamil Nadu",children:"Tamil Nadu"}),(0,Za.jsx)("option",{value:"Telangana",children:"Telangana"}),(0,Za.jsx)("option",{value:"Tripura",children:"Tripura"}),(0,Za.jsx)("option",{value:"Uttar Pradesh",children:"Uttar Pradesh"}),(0,Za.jsx)("option",{value:"Uttarakhand",children:"Uttarakhand"}),(0,Za.jsx)("option",{value:"West Bengal",children:"West Bengal"}),(0,Za.jsx)("option",{value:"Delhi",children:"Delhi"})]})),f.state&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f.state.message})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"PIN Code *"}),(0,Za.jsx)("input",c(c({},u("pinCode")),{},{type:"text",maxLength:"6",className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"6-digit PIN code"})),f.pinCode&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f.pinCode.message})]})]})]}),3===s&&(0,Za.jsxs)("div",{className:"space-y-6",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,Za.jsx)(jl,{className:"w-5 h-5 mr-2"}),"Document Upload"]}),(0,Za.jsx)("p",{className:"text-sm text-gray-600",children:"Please upload clear, readable copies of the following documents. Accepted formats: JPEG, PNG, PDF (Max 5MB each)"})]}),(0,Za.jsx)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[{key:"aadharCard",label:"Aadhar Card",required:!0},{key:"panCard",label:"PAN Card",required:!0},{key:"photo",label:"Passport Size Photo",required:!0},{key:"bankPassbook",label:"Bank Passbook/Statement",required:!1},{key:"businessProof",label:"Business Proof (if applicable)",required:!1}].map(e=>(0,Za.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 transition-colors",children:(0,Za.jsxs)("div",{className:"text-center",children:[(0,Za.jsx)(jl,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,Za.jsx)("div",{className:"mt-4",children:(0,Za.jsxs)("label",{className:"cursor-pointer",children:[(0,Za.jsxs)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:[e.label," ",e.required&&"*"]}),(0,Za.jsx)("input",{type:"file",className:"hidden",accept:".jpg,.jpeg,.png,.pdf",onChange:t=>((e,t)=>{if(!t)return;["image/jpeg","image/png","application/pdf"].includes(t.type)?t.size>5242880?en.error("File size must be less than 5MB"):(l(n=>c(c({},n),{},{[e]:t})),en.success("".concat(e," uploaded successfully"))):en.error("Only JPEG, PNG, and PDF files are allowed")})(e.key,t.target.files[0])}),(0,Za.jsx)("span",{className:"mt-2 block text-xs text-gray-500",children:"Click to upload or drag and drop"})]})}),i[e.key]&&(0,Za.jsxs)("div",{className:"mt-2 text-sm text-green-600 flex items-center justify-center",children:[(0,Za.jsx)(hs,{className:"w-4 h-4 mr-1"}),i[e.key].name]})]})},e.key))})]}),4===s&&(0,Za.jsxs)("div",{className:"space-y-6",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,Za.jsx)(hs,{className:"w-5 h-5 mr-2"}),"Review & Submit"]}),(0,Za.jsx)("p",{className:"text-sm text-gray-600",children:"Please review all the information before submitting your application."})]}),(0,Za.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 space-y-4",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Personal Information"}),(0,Za.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("span",{className:"text-gray-500",children:"Name:"}),(0,Za.jsxs)("span",{className:"ml-2 text-gray-900",children:[p("firstName")," ",p("lastName")]})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("span",{className:"text-gray-500",children:"Email:"}),(0,Za.jsx)("span",{className:"ml-2 text-gray-900",children:p("email")})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("span",{className:"text-gray-500",children:"Phone:"}),(0,Za.jsx)("span",{className:"ml-2 text-gray-900",children:p("phoneNumber")})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("span",{className:"text-gray-500",children:"Date of Birth:"}),(0,Za.jsx)("span",{className:"ml-2 text-gray-900",children:p("dateOfBirth")})]})]})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Identity & Address"}),(0,Za.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("span",{className:"text-gray-500",children:"Aadhar:"}),(0,Za.jsx)("span",{className:"ml-2 text-gray-900",children:p("aadharNumber")})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("span",{className:"text-gray-500",children:"PAN:"}),(0,Za.jsx)("span",{className:"ml-2 text-gray-900",children:p("panNumber")})]}),(0,Za.jsxs)("div",{className:"col-span-2",children:[(0,Za.jsx)("span",{className:"text-gray-500",children:"Address:"}),(0,Za.jsxs)("span",{className:"ml-2 text-gray-900",children:[p("address"),", ",p("city"),", ",p("state")," - ",p("pinCode")]})]})]})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Uploaded Documents"}),(0,Za.jsx)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:Object.entries(i).map(e=>{let[t,n]=e;return n&&(0,Za.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,Za.jsx)(hs,{className:"w-4 h-4 mr-1"}),t.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())]},t)})})]})]}),(0,Za.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,Za.jsxs)("div",{className:"flex",children:[(0,Za.jsx)("div",{className:"flex-shrink-0",children:(0,Za.jsx)(hs,{className:"h-5 w-5 text-blue-400"})}),(0,Za.jsxs)("div",{className:"ml-3",children:[(0,Za.jsx)("h3",{className:"text-sm font-medium text-blue-800",children:"Important Information"}),(0,Za.jsx)("div",{className:"mt-2 text-sm text-blue-700",children:(0,Za.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,Za.jsx)("li",{children:"Your application will be reviewed within 3-5 business days"}),(0,Za.jsx)("li",{children:"You will receive email notifications about status updates"}),(0,Za.jsx)("li",{children:"Ensure all uploaded documents are clear and readable"}),(0,Za.jsx)("li",{children:"Any false information may lead to application rejection"})]})})]})]})})]}),(0,Za.jsxs)("div",{className:"flex justify-between pt-6 border-t border-gray-200",children:[(0,Za.jsx)("button",{type:"button",onClick:()=>{o(e=>Math.max(e-1,1))},disabled:1===s,className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),s<4?(0,Za.jsx)("button",{type:"button",onClick:async()=>{let e=[];switch(s){case 1:e=["firstName","lastName","email","phoneNumber","dateOfBirth"];break;case 2:e=["aadharNumber","panNumber","address","city","state","pinCode"];break;case 3:const t=["aadharCard","panCard","photo"].filter(e=>!i[e]);if(t.length>0)return void en.error("Please upload: ".concat(t.join(", ")))}if(e.length>0){if(!await m(e))return}o(e=>Math.min(e+1,4))},className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Next"}):(0,Za.jsx)("button",{type:"submit",disabled:r,className:"inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed",children:r?(0,Za.jsxs)(Za.Fragment,{children:[(0,Za.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):"Submit Application"})]})]})]})},Pl=ds("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),Ol=ds("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),Rl=ds("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),Fl=ds("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),Ll=ds("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),Dl=ds("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),zl=()=>{const{isAdmin:e,isReviewer:n}=ts(),[r,a]=(0,t.useState)([]),[s,o]=(0,t.useState)(!0),[i,l]=(0,t.useState)(""),[c,u]=(0,t.useState)(""),[d,f]=(0,t.useState)(1),[m,p]=(0,t.useState)(1),[h]=(0,t.useState)(10),[g,y]=(0,t.useState)("createdAt"),[v,b]=(0,t.useState)("desc");(0,t.useEffect)(()=>{x()},[d,c,g,v]);const x=async()=>{o(!0);try{const e=await Va(d,h,c);let t=e.data||[];i&&(t=t.filter(e=>{var t,n,r,a,s;return(null===(t=e.firstName)||void 0===t?void 0:t.toLowerCase().includes(i.toLowerCase()))||(null===(n=e.lastName)||void 0===n?void 0:n.toLowerCase().includes(i.toLowerCase()))||(null===(r=e.email)||void 0===r?void 0:r.toLowerCase().includes(i.toLowerCase()))||(null===(a=e.phoneNumber)||void 0===a?void 0:a.includes(i))||(null===(s=e.agentId)||void 0===s?void 0:s.toString().includes(i))})),a(t),p(Math.ceil((e.total||t.length)/h))}catch(e){console.error("Error fetching agents:",e),en.error("Failed to fetch agents")}finally{o(!1)}},w=()=>{f(1),x()},k=async function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";try{await qa(e,t,n),en.success("Agent status updated to ".concat(t)),x()}catch(r){console.error("Error updating status:",r),en.error("Failed to update agent status")}},N=e=>{switch(e){case"Approved":return(0,Za.jsx)(yl,{className:"h-5 w-5 text-green-500"});case"Pending":return(0,Za.jsx)(vl,{className:"h-5 w-5 text-yellow-500"});case"Rejected":return(0,Za.jsx)(bl,{className:"h-5 w-5 text-red-500"});case"UnderReview":return(0,Za.jsx)(xl,{className:"h-5 w-5 text-blue-500"});default:return(0,Za.jsx)(xl,{className:"h-5 w-5 text-gray-500"})}},j=e=>{switch(e){case"Approved":return"bg-green-100 text-green-800";case"Pending":return"bg-yellow-100 text-yellow-800";case"Rejected":return"bg-red-100 text-red-800";case"UnderReview":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},S=e=>{g===e?b("asc"===v?"desc":"asc"):(y(e),b("asc"))};return s?(0,Za.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,Za.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):(0,Za.jsxs)("div",{className:"space-y-6",children:[(0,Za.jsxs)("div",{className:"flex justify-between items-center",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Agent Management"}),(0,Za.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"View and manage all agent applications and their status."})]}),(0,Za.jsxs)("div",{className:"flex space-x-3",children:[(0,Za.jsxs)("button",{onClick:x,className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,Za.jsx)(Pl,{className:"h-4 w-4 mr-2"}),"Refresh"]}),(0,Za.jsxs)("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,Za.jsx)(Ol,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,Za.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,Za.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-4",children:[(0,Za.jsxs)("div",{className:"sm:col-span-2",children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Agents"}),(0,Za.jsxs)("div",{className:"relative",children:[(0,Za.jsx)("input",{type:"text",value:i,onChange:e=>l(e.target.value),onKeyPress:e=>"Enter"===e.key&&w(),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500",placeholder:"Search by name, email, phone, or ID..."}),(0,Za.jsx)(Rl,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"})]})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status Filter"}),(0,Za.jsxs)("select",{value:c,onChange:e=>u(e.target.value),className:"block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,Za.jsx)("option",{value:"",children:"All Status"}),(0,Za.jsx)("option",{value:"Pending",children:"Pending"}),(0,Za.jsx)("option",{value:"UnderReview",children:"Under Review"}),(0,Za.jsx)("option",{value:"Approved",children:"Approved"}),(0,Za.jsx)("option",{value:"Rejected",children:"Rejected"}),(0,Za.jsx)("option",{value:"Active",children:"Active"}),(0,Za.jsx)("option",{value:"Inactive",children:"Inactive"}),(0,Za.jsx)("option",{value:"Suspended",children:"Suspended"})]})]}),(0,Za.jsx)("div",{className:"flex items-end",children:(0,Za.jsxs)("button",{onClick:w,className:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,Za.jsx)(Fl,{className:"h-4 w-4 mr-2"}),"Apply Filters"]})})]})}),(0,Za.jsxs)("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:[(0,Za.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Agents (",r.length," of ",m*h,")"]})}),(0,Za.jsx)("div",{className:"overflow-x-auto",children:(0,Za.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,Za.jsx)("thead",{className:"bg-gray-50",children:(0,Za.jsxs)("tr",{children:[(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>S("agentId"),children:"Agent ID"}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>S("firstName"),children:"Name"}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>S("email"),children:"Contact"}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>S("status"),children:"Status"}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>S("createdAt"),children:"Applied Date"}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Za.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:r.map(t=>(0,Za.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,Za.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:["#",t.agentId]}),(0,Za.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Za.jsxs)("div",{children:[(0,Za.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[t.firstName," ",t.lastName]}),(0,Za.jsxs)("div",{className:"text-sm text-gray-500",children:["PAN: ",t.panNumber]})]})}),(0,Za.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Za.jsxs)("div",{children:[(0,Za.jsx)("div",{className:"text-sm text-gray-900",children:t.email}),(0,Za.jsx)("div",{className:"text-sm text-gray-500",children:t.phoneNumber})]})}),(0,Za.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Za.jsxs)("div",{className:"flex items-center",children:[N(t.status),(0,Za.jsx)("span",{className:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(j(t.status)),children:t.status})]})}),(0,Za.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(t.createdAt).toLocaleDateString()}),(0,Za.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,Za.jsxs)("div",{className:"flex space-x-2",children:[(0,Za.jsxs)(yt,{to:"/agents/".concat(t.agentId),className:"text-blue-600 hover:text-blue-900 flex items-center",children:[(0,Za.jsx)(pl,{className:"h-4 w-4 mr-1"}),"View"]}),(e()||n())&&"Pending"===t.status&&(0,Za.jsxs)(Za.Fragment,{children:[(0,Za.jsxs)("button",{onClick:()=>k(t.agentId,"Approved"),className:"text-green-600 hover:text-green-900 flex items-center",children:[(0,Za.jsx)(yl,{className:"h-4 w-4 mr-1"}),"Approve"]}),(0,Za.jsxs)("button",{onClick:()=>k(t.agentId,"Rejected"),className:"text-red-600 hover:text-red-900 flex items-center",children:[(0,Za.jsx)(bl,{className:"h-4 w-4 mr-1"}),"Reject"]})]})]})})]},t.agentId))})]})}),m>1&&(0,Za.jsxs)("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,Za.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,Za.jsx)("button",{onClick:()=>f(Math.max(d-1,1)),disabled:1===d,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,Za.jsx)("button",{onClick:()=>f(Math.min(d+1,m)),disabled:d===m,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,Za.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,Za.jsx)("div",{children:(0,Za.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing"," ",(0,Za.jsx)("span",{className:"font-medium",children:(d-1)*h+1})," ","to"," ",(0,Za.jsx)("span",{className:"font-medium",children:Math.min(d*h,m*h)})," ","of"," ",(0,Za.jsx)("span",{className:"font-medium",children:m*h})," ","results"]})}),(0,Za.jsx)("div",{children:(0,Za.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,Za.jsx)("button",{onClick:()=>f(Math.max(d-1,1)),disabled:1===d,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,Za.jsx)(Ll,{className:"h-5 w-5"})}),[...Array(m)].map((e,t)=>{const n=t+1;return n===d||1===n||n===m||n>=d-1&&n<=d+1?(0,Za.jsx)("button",{onClick:()=>f(n),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(n===d?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"),children:n},n):n===d-2||n===d+2?(0,Za.jsx)("span",{className:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700",children:"..."},n):null}),(0,Za.jsx)("button",{onClick:()=>f(Math.min(d+1,m)),disabled:d===m,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,Za.jsx)(Dl,{className:"h-5 w-5"})})]})})]})]})]}),0===r.length&&!s&&(0,Za.jsx)("div",{className:"bg-white shadow rounded-lg p-6",children:(0,Za.jsxs)("div",{className:"text-center py-12",children:[(0,Za.jsx)(xl,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,Za.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No agents found"}),(0,Za.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:i||c?"Try adjusting your search criteria or filters.":"No agent applications have been submitted yet."})]})})]})},Ml=ds("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),Il=ds("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),Ul=ds("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),Vl=e=>{let{agentId:n,showActions:r=!1}=e;const[a,s]=(0,t.useState)(null),[o,i]=(0,t.useState)(!0),[l,c]=(0,t.useState)(!1);(0,t.useEffect)(()=>{u()},[n]);const u=async()=>{try{i(!0);const e=await $a(n);s(e)}catch(e){console.error("Error fetching workflow summary:",e),en.error("Failed to load workflow information")}finally{i(!1)}},d=e=>e.isCompleted?(0,Za.jsx)(yl,{className:"w-6 h-6 text-green-500"}):e.isCurrent?(0,Za.jsx)(vl,{className:"w-6 h-6 text-blue-500"}):(0,Za.jsx)("div",{className:"w-6 h-6 rounded-full border-2 border-gray-300 bg-white"}),f=e=>{if(!e)return"N/A";const t=Math.floor(e.totalHours||0),n=Math.floor(t/24),r=t%24;return n>0?"".concat(n,"d ").concat(r,"h"):r>0?"".concat(r,"h"):"< 1h"};return o?(0,Za.jsxs)("div",{className:"animate-pulse",children:[(0,Za.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4 mb-4"}),(0,Za.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,Za.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,Za.jsx)("div",{className:"w-6 h-6 bg-gray-200 rounded-full"}),(0,Za.jsx)("div",{className:"flex-1 h-4 bg-gray-200 rounded"})]},e))})]}):a?(0,Za.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,Za.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,Za.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Onboarding Progress"}),r&&(0,Za.jsx)("button",{onClick:async()=>{try{c(!0),await Ka(n),en.success("Workflow auto-progressed successfully"),await u()}catch(e){console.error("Error auto-progressing workflow:",e),en.error("Failed to auto-progress workflow")}finally{c(!1)}},disabled:l,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm",children:l?"Processing...":"Auto Progress"})]}),(0,Za.jsxs)("div",{className:"mb-6",children:[(0,Za.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,Za.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:["Progress: ",a.completedSteps," of ",a.totalSteps," steps"]}),(0,Za.jsxs)("span",{className:"text-sm text-gray-500",children:[Math.round(a.progressPercentage),"%"]})]}),(0,Za.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,Za.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(a.progressPercentage,"%")}})})]}),(0,Za.jsx)("div",{className:"mb-6 p-4 bg-blue-50 rounded-lg",children:(0,Za.jsxs)("div",{className:"flex items-center space-x-3",children:[(e=>{const t={0:gs,1:hs,2:Ml,4:Il,3:xl,5:vl,6:xl}[e]||gs;return(0,Za.jsx)(t,{className:"w-4 h-4"})})(a.currentStatus),(0,Za.jsxs)("div",{children:[(0,Za.jsxs)("h4",{className:"font-medium text-blue-900",children:["Current Status: ",a.currentStepName]}),(0,Za.jsxs)("p",{className:"text-sm text-blue-700",children:["Time in current step: ",f(a.totalTimeSpent)]}),a.estimatedRemainingTime&&(0,Za.jsxs)("p",{className:"text-sm text-blue-700",children:["Estimated remaining: ",f(a.estimatedRemainingTime)]})]})]})}),a.isBlocked&&(0,Za.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,Za.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,Za.jsx)(xl,{className:"w-5 h-5 text-red-500"}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h4",{className:"font-medium text-red-900",children:"Workflow Blocked"}),(0,Za.jsx)("p",{className:"text-sm text-red-700",children:a.blockedReason})]})]})}),(0,Za.jsx)("div",{className:"space-y-6",children:a.steps.map((e,t)=>(0,Za.jsxs)("div",{className:"relative",children:[t<a.steps.length-1&&(0,Za.jsx)("div",{className:"absolute left-3 top-8 w-0.5 h-12 bg-gray-200"}),(0,Za.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,Za.jsx)("div",{className:"flex-shrink-0",children:d(e)}),(0,Za.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,Za.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Za.jsx)("h4",{className:"font-medium ".concat(e.isCompleted?"text-green-700":e.isCurrent?"text-blue-700":"text-gray-500"),children:e.name}),e.completedAt&&(0,Za.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,Za.jsx)(El,{className:"w-4 h-4"}),(0,Za.jsx)("span",{children:new Date(e.completedAt).toLocaleDateString()})]})]}),e.requirements.length>0&&(0,Za.jsxs)("div",{className:"mt-2",children:[(0,Za.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Requirements:"}),(0,Za.jsx)("ul",{className:"text-sm text-gray-500 space-y-1",children:e.requirements.map((e,t)=>(0,Za.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,Za.jsx)("div",{className:"w-1.5 h-1.5 bg-gray-400 rounded-full"}),(0,Za.jsx)("span",{children:e})]},t))})]}),e.completedBy&&(0,Za.jsxs)("div",{className:"mt-2 flex items-center space-x-2 text-sm text-gray-500",children:[(0,Za.jsx)(gs,{className:"w-4 h-4"}),(0,Za.jsxs)("span",{children:["Completed by: ",e.completedBy]})]}),e.requiresManualReview&&(0,Za.jsx)("div",{className:"mt-2",children:(0,Za.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,Za.jsx)(Ul,{className:"w-3 h-3 mr-1"}),"Manual Review Required"]})})]})]})]},e.status))}),(0,Za.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,Za.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("span",{className:"text-gray-500",children:"Total Time:"}),(0,Za.jsx)("span",{className:"ml-2 font-medium",children:f(a.totalTimeSpent)})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("span",{className:"text-gray-500",children:"Completion:"}),(0,Za.jsxs)("span",{className:"ml-2 font-medium",children:[Math.round(a.progressPercentage),"%"]})]})]})})]}):(0,Za.jsxs)("div",{className:"text-center py-8",children:[(0,Za.jsx)(xl,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,Za.jsx)("p",{className:"text-gray-500",children:"Unable to load workflow information"})]})},Bl=ds("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),ql=e=>{let{agentId:n,currentStatus:r,onStatusUpdated:a,onClose:s}=e;const[o,i]=(0,t.useState)([]),[l,c]=(0,t.useState)(""),[u,d]=(0,t.useState)(""),[f,m]=(0,t.useState)(null),[p,h]=(0,t.useState)(!1),[g,y]=(0,t.useState)(!1);(0,t.useEffect)(()=>{v()},[n]),(0,t.useEffect)(()=>{l&&b()},[l]);const v=async()=>{try{const e=await Ha(n);i(e)}catch(e){console.error("Error fetching valid statuses:",e),en.error("Failed to load valid status options")}},b=async()=>{if(l)try{y(!0);const e=await Wa(n,parseInt(l));m(e)}catch(e){console.error("Error validating transition:",e),en.error("Failed to validate status transition")}finally{y(!1)}},x=e=>{const t={0:{name:"Pending",icon:vl,color:"text-yellow-600",bgColor:"bg-yellow-50",borderColor:"border-yellow-200"},1:{name:"Under Review",icon:hs,color:"text-blue-600",bgColor:"bg-blue-50",borderColor:"border-blue-200"},2:{name:"Approved",icon:yl,color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"},3:{name:"Rejected",icon:bl,color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"},4:{name:"Active",icon:Il,color:"text-green-600",bgColor:"bg-green-50",borderColor:"border-green-200"},5:{name:"Inactive",icon:vl,color:"text-gray-600",bgColor:"bg-gray-50",borderColor:"border-gray-200"},6:{name:"Suspended",icon:Bl,color:"text-red-600",bgColor:"bg-red-50",borderColor:"border-red-200"}};return t[e]||t[0]};return(0,Za.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,Za.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white",children:(0,Za.jsxs)("div",{className:"mt-3",children:[(0,Za.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,Za.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Update Agent Status"}),(0,Za.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-gray-600",children:(0,Za.jsx)(bl,{className:"w-6 h-6"})})]}),(0,Za.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:[(0,Za.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Current Status"}),(0,Za.jsxs)("div",{className:"flex items-center space-x-3",children:[t.createElement(x(r).icon,{className:"w-5 h-5 ".concat(x(r).color)}),(0,Za.jsx)("span",{className:"font-medium",children:x(r).name})]})]}),(0,Za.jsxs)("div",{className:"mb-6",children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Select New Status"}),(0,Za.jsx)("div",{className:"grid grid-cols-1 gap-3",children:o.map(e=>{const n=x(e),r=l===e.toString();return(0,Za.jsxs)("label",{className:"relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ".concat(r?"".concat(n.borderColor," ").concat(n.bgColor):"border-gray-200"),children:[(0,Za.jsx)("input",{type:"radio",name:"status",value:e,checked:r,onChange:e=>c(e.target.value),className:"sr-only"}),(0,Za.jsxs)("div",{className:"flex items-center space-x-3",children:[t.createElement(n.icon,{className:"w-5 h-5 ".concat(n.color)}),(0,Za.jsx)("span",{className:"font-medium ".concat(r?n.color:"text-gray-900"),children:n.name})]}),r&&(0,Za.jsx)(yl,{className:"w-5 h-5 ml-auto ".concat(n.color)})]},e)})})]}),g&&(0,Za.jsx)("div",{className:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,Za.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,Za.jsx)(vl,{className:"w-5 h-5 text-blue-500 animate-spin"}),(0,Za.jsx)("span",{className:"text-blue-700",children:"Validating status transition..."})]})}),f&&!g&&(0,Za.jsx)("div",{className:"mb-6 p-4 border rounded-lg ".concat(f.isValid?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:(0,Za.jsxs)("div",{className:"flex items-start space-x-3",children:[f.isValid?(0,Za.jsx)(yl,{className:"w-5 h-5 text-green-500 mt-0.5"}):(0,Za.jsx)(bl,{className:"w-5 h-5 text-red-500 mt-0.5"}),(0,Za.jsxs)("div",{className:"flex-1",children:[(0,Za.jsx)("h4",{className:"font-medium ".concat(f.isValid?"text-green-800":"text-red-800"),children:f.isValid?"Transition Valid":"Transition Invalid"}),f.errorMessage&&(0,Za.jsx)("p",{className:"text-sm text-red-700 mt-1",children:f.errorMessage}),f.requiredActions.length>0&&(0,Za.jsxs)("div",{className:"mt-2",children:[(0,Za.jsx)("p",{className:"text-sm font-medium text-red-800",children:"Required Actions:"}),(0,Za.jsx)("ul",{className:"text-sm text-red-700 mt-1 space-y-1",children:f.requiredActions.map((e,t)=>(0,Za.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,Za.jsx)("span",{className:"text-red-500 mt-1",children:"\u2022"}),(0,Za.jsx)("span",{children:e})]},t))})]}),f.missingDocuments.length>0&&(0,Za.jsxs)("div",{className:"mt-2",children:[(0,Za.jsx)("p",{className:"text-sm font-medium text-red-800",children:"Missing Documents:"}),(0,Za.jsx)("ul",{className:"text-sm text-red-700 mt-1 space-y-1",children:f.missingDocuments.map((e,t)=>(0,Za.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,Za.jsx)("span",{className:"text-red-500 mt-1",children:"\u2022"}),(0,Za.jsx)("span",{children:e})]},t))})]}),f.requiresManualReview&&(0,Za.jsxs)("div",{className:"mt-2 flex items-center space-x-2",children:[(0,Za.jsx)(Ul,{className:"w-4 h-4 text-yellow-500"}),(0,Za.jsx)("span",{className:"text-sm text-yellow-700",children:"Manual review required for this transition"})]})]})]})}),(0,Za.jsxs)("div",{className:"mb-6",children:[(0,Za.jsxs)("label",{htmlFor:"comments",className:"block text-sm font-medium text-gray-700 mb-2",children:["Comments ","3"===l&&(0,Za.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,Za.jsx)("textarea",{id:"comments",rows:4,value:u,onChange:e=>d(e.target.value),placeholder:"Add comments about this status change...",className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"}),"3"===l&&(0,Za.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Comments are required when rejecting an application"})]}),(0,Za.jsxs)("div",{className:"flex items-center justify-end space-x-4",children:[(0,Za.jsx)("button",{onClick:s,className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,Za.jsx)("button",{onClick:async()=>{if(l)if(!f||f.isValid)try{h(!0),await Qa(n,parseInt(l),u),en.success("Status updated successfully"),a(),s()}catch(e){console.error("Error updating status:",e),en.error("Failed to update status")}finally{h(!1)}else en.error("Cannot proceed with invalid status transition");else en.error("Please select a status")},disabled:p||!l||f&&!f.isValid||"3"===l&&!u.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:p?"Updating...":"Update Status"})]})]})})})},$l=ds("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),Hl=ds("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]),Wl=()=>{const{id:e}=he(),n=me(),{isAdmin:r,isReviewer:a}=ts(),[s,o]=(0,t.useState)(null),[i,l]=(0,t.useState)(!0),[c,u]=(0,t.useState)([]),[d,f]=(0,t.useState)([]),[m,p]=(0,t.useState)(!1),[h,g]=(0,t.useState)("details");(0,t.useEffect)(()=>{y()},[e]);const y=async()=>{l(!0);try{const t=await Ba(e);o(t),u([{id:1,status:"Pending",comments:"Application submitted",createdAt:t.createdAt,updatedBy:"System"}]),f([{type:"aadharCard",name:"Aadhar Card",status:"Uploaded",url:"#"},{type:"panCard",name:"PAN Card",status:"Uploaded",url:"#"},{type:"photo",name:"Photo",status:"Uploaded",url:"#"}])}catch(t){console.error("Error fetching agent details:",t),en.error("Failed to fetch agent details")}finally{l(!1)}},v=e=>{switch(e){case"Approved":return(0,Za.jsx)(yl,{className:"h-6 w-6 text-green-500"});case"Pending":return(0,Za.jsx)(vl,{className:"h-6 w-6 text-yellow-500"});case"Rejected":return(0,Za.jsx)(bl,{className:"h-6 w-6 text-red-500"});case"UnderReview":return(0,Za.jsx)(xl,{className:"h-6 w-6 text-blue-500"});default:return(0,Za.jsx)(xl,{className:"h-6 w-6 text-gray-500"})}};return i?(0,Za.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,Za.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):s?(0,Za.jsxs)("div",{className:"space-y-6",children:[(0,Za.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Za.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,Za.jsxs)("button",{onClick:()=>n("/agents"),className:"inline-flex items-center text-gray-500 hover:text-gray-700",children:[(0,Za.jsx)($l,{className:"h-5 w-5 mr-1"}),"Back to Agents"]}),(0,Za.jsxs)("div",{children:[(0,Za.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[s.firstName," ",s.lastName]}),(0,Za.jsxs)("p",{className:"mt-1 text-sm text-gray-600",children:["Agent ID: #",s.agentId," \u2022 Applied on ",new Date(s.createdAt).toLocaleDateString()]})]})]}),(0,Za.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,Za.jsxs)("div",{className:"flex items-center px-3 py-2 rounded-lg border ".concat((e=>{switch(e){case"Approved":return"bg-green-100 text-green-800 border-green-200";case"Pending":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"Rejected":return"bg-red-100 text-red-800 border-red-200";case"UnderReview":return"bg-blue-100 text-blue-800 border-blue-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(s.status)),children:[v(s.status),(0,Za.jsx)("span",{className:"ml-2 font-medium",children:s.status})]}),(r()||a())&&(0,Za.jsx)("button",{onClick:()=>p(!0),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Update Status"})]})]}),(0,Za.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,Za.jsx)("div",{className:"border-b border-gray-200",children:(0,Za.jsx)("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:[{id:"details",name:"Personal Details",icon:gs},{id:"workflow",name:"Workflow Progress",icon:vl},{id:"documents",name:"Documents",icon:hs},{id:"history",name:"Status History",icon:Hl}].map(e=>{const t=e.icon;return(0,Za.jsxs)("button",{onClick:()=>g(e.id),className:"".concat(h===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"," whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center"),children:[(0,Za.jsx)(t,{className:"h-4 w-4 mr-2"}),e.name]},e.id)})})}),(0,Za.jsxs)("div",{className:"p-6",children:["details"===h&&(0,Za.jsxs)("div",{className:"space-y-6",children:[(0,Za.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[(0,Za.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,Za.jsx)(gs,{className:"h-5 w-5 mr-2"}),"Personal Information"]}),(0,Za.jsxs)("dl",{className:"space-y-3",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Full Name"}),(0,Za.jsxs)("dd",{className:"text-sm text-gray-900",children:[s.firstName," ",s.lastName]})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Date of Birth"}),(0,Za.jsx)("dd",{className:"text-sm text-gray-900",children:new Date(s.dateOfBirth).toLocaleDateString()})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,Za.jsxs)("dd",{className:"text-sm text-gray-900 flex items-center",children:[(0,Za.jsx)(Sl,{className:"h-4 w-4 mr-2 text-gray-400"}),s.email]})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Phone Number"}),(0,Za.jsxs)("dd",{className:"text-sm text-gray-900 flex items-center",children:[(0,Za.jsx)(_l,{className:"h-4 w-4 mr-2 text-gray-400"}),s.phoneNumber]})]})]})]}),(0,Za.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,Za.jsx)(Tl,{className:"h-5 w-5 mr-2"}),"Identity Information"]}),(0,Za.jsxs)("dl",{className:"space-y-3",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Aadhar Number"}),(0,Za.jsx)("dd",{className:"text-sm text-gray-900",children:s.aadharNumber})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"PAN Number"}),(0,Za.jsx)("dd",{className:"text-sm text-gray-900",children:s.panNumber})]})]})]})]}),(0,Za.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4 flex items-center",children:[(0,Za.jsx)(Nl,{className:"h-5 w-5 mr-2"}),"Address Information"]}),(0,Za.jsxs)("dl",{className:"grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-4",children:[(0,Za.jsxs)("div",{className:"sm:col-span-2",children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Address"}),(0,Za.jsx)("dd",{className:"text-sm text-gray-900",children:s.address})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"City"}),(0,Za.jsx)("dd",{className:"text-sm text-gray-900",children:s.city})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"State"}),(0,Za.jsx)("dd",{className:"text-sm text-gray-900",children:s.state})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"PIN Code"}),(0,Za.jsx)("dd",{className:"text-sm text-gray-900",children:s.pinCode})]})]})]})]}),"workflow"===h&&(0,Za.jsx)(Vl,{agentId:s.agentId,showActions:r()||a()}),"documents"===h&&(0,Za.jsx)("div",{className:"space-y-6",children:(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Uploaded Documents"}),(0,Za.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:d.map((e,t)=>(0,Za.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,Za.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,Za.jsxs)("div",{className:"flex items-center",children:[(0,Za.jsx)(hs,{className:"h-8 w-8 text-blue-500"}),(0,Za.jsxs)("div",{className:"ml-3",children:[(0,Za.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,Za.jsx)("p",{className:"text-xs text-gray-500",children:e.type})]})]}),(0,Za.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:e.status})]}),(0,Za.jsxs)("div",{className:"flex space-x-2",children:[(0,Za.jsxs)("button",{className:"flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50",children:[(0,Za.jsx)(pl,{className:"h-3 w-3 mr-1"}),"View"]}),(0,Za.jsxs)("button",{className:"flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50",children:[(0,Za.jsx)(Ol,{className:"h-3 w-3 mr-1"}),"Download"]})]})]},t))})]})}),"history"===h&&(0,Za.jsx)("div",{className:"space-y-6",children:(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Status History"}),(0,Za.jsx)("div",{className:"flow-root",children:(0,Za.jsx)("ul",{className:"-mb-8",children:c.map((e,t)=>(0,Za.jsx)("li",{children:(0,Za.jsxs)("div",{className:"relative pb-8",children:[t!==c.length-1&&(0,Za.jsx)("span",{className:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200","aria-hidden":"true"}),(0,Za.jsxs)("div",{className:"relative flex space-x-3",children:[(0,Za.jsx)("div",{children:v(e.status)}),(0,Za.jsxs)("div",{className:"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsxs)("p",{className:"text-sm text-gray-500",children:["Status changed to ",(0,Za.jsx)("span",{className:"font-medium text-gray-900",children:e.status})]}),e.comments&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:e.comments})]}),(0,Za.jsxs)("div",{className:"text-right text-sm whitespace-nowrap text-gray-500",children:[(0,Za.jsx)("time",{dateTime:e.createdAt,children:new Date(e.createdAt).toLocaleDateString()}),(0,Za.jsxs)("p",{className:"text-xs text-gray-400",children:["by ",e.updatedBy]})]})]})]})]})},e.id))})})]})})]})]}),m&&(0,Za.jsx)(ql,{agentId:s.agentId,currentStatus:(b=s.status,{Pending:0,UnderReview:1,Approved:2,Rejected:3,Active:4,Inactive:5,Suspended:6}[b]||0),onStatusUpdated:y,onClose:()=>p(!1)})]}):(0,Za.jsxs)("div",{className:"text-center py-12",children:[(0,Za.jsx)(xl,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,Za.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Agent not found"}),(0,Za.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:["The agent with ID ",e," could not be found."]}),(0,Za.jsx)("button",{onClick:()=>n("/agents"),className:"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Back to Agents"})]});var b},Ql=ds("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),Kl=ds("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),Yl=ds("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),Xl=ll({currentPassword:Yi().required("Current password is required"),newPassword:Yi().min(8,"Password must be at least 8 characters").matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,"Password must contain uppercase, lowercase, number and special character").required("New password is required"),confirmPassword:Yi().oneOf([(Jl="newPassword",new Ai(Jl,Gl)),null],"Passwords must match").required("Please confirm your password")});var Jl,Gl;const Zl=()=>{const{user:e,updateUser:n}=ts(),[r,a]=(0,t.useState)(!1),[s,o]=(0,t.useState)(!1),[i,l]=(0,t.useState)({current:!1,new:!1,confirm:!1}),[u,d]=(0,t.useState)({username:(null===e||void 0===e?void 0:e.username)||"",email:(null===e||void 0===e?void 0:e.email)||""}),{register:f,handleSubmit:m,formState:{errors:p},reset:h}=Qo({resolver:Zo(Xl)});(0,t.useEffect)(()=>{e&&d({username:e.username||"",email:e.email||""})},[e]);const g=e=>{l(t=>c(c({},t),{},{[e]:!t[e]}))};return(0,Za.jsxs)("div",{className:"space-y-6",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Profile"}),(0,Za.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Manage your account settings and information."})]}),(0,Za.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,Za.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,Za.jsxs)("div",{className:"flex items-center justify-between",children:[(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[(0,Za.jsx)(gs,{className:"h-5 w-5 mr-2"}),"User Information"]}),r?(0,Za.jsxs)("div",{className:"flex space-x-2",children:[(0,Za.jsxs)("button",{onClick:async()=>{try{await n(u),en.success("Profile updated successfully"),a(!1)}catch(e){console.error("Error updating profile:",e),en.error("Failed to update profile")}},className:"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,Za.jsx)(Kl,{className:"h-4 w-4 mr-2"}),"Save"]}),(0,Za.jsxs)("button",{onClick:()=>{a(!1),d({username:(null===e||void 0===e?void 0:e.username)||"",email:(null===e||void 0===e?void 0:e.email)||""})},className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,Za.jsx)(ys,{className:"h-4 w-4 mr-2"}),"Cancel"]})]}):(0,Za.jsxs)("button",{onClick:()=>a(!0),className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50",children:[(0,Za.jsx)(Ql,{className:"h-4 w-4 mr-2"}),"Edit"]})]})}),(0,Za.jsx)("div",{className:"p-6",children:(0,Za.jsxs)("dl",{className:"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Username"}),r?(0,Za.jsx)("input",{type:"text",value:u.username,onChange:e=>d(t=>c(c({},t),{},{username:e.target.value})),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"}):(0,Za.jsx)("dd",{className:"mt-1 text-sm text-gray-900",children:null===e||void 0===e?void 0:e.username})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Email"}),r?(0,Za.jsx)("input",{type:"email",value:u.email,onChange:e=>d(t=>c(c({},t),{},{email:e.target.value})),className:"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"}):(0,Za.jsxs)("dd",{className:"mt-1 text-sm text-gray-900 flex items-center",children:[(0,Za.jsx)(Sl,{className:"h-4 w-4 mr-2 text-gray-400"}),null===e||void 0===e?void 0:e.email]})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Role"}),(0,Za.jsxs)("dd",{className:"mt-1 text-sm text-gray-900 flex items-center",children:[(0,Za.jsx)(Ml,{className:"h-4 w-4 mr-2 text-gray-400"}),null===e||void 0===e?void 0:e.role]})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Last Login"}),(0,Za.jsxs)("dd",{className:"mt-1 text-sm text-gray-900 flex items-center",children:[(0,Za.jsx)(Il,{className:"h-4 w-4 mr-2 text-gray-400"}),null!==e&&void 0!==e&&e.lastLoginAt?new Date(e.lastLoginAt).toLocaleString():"Never"]})]}),(null===e||void 0===e?void 0:e.agentId)&&(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Agent ID"}),(0,Za.jsxs)("dd",{className:"mt-1 text-sm text-gray-900",children:["#",e.agentId]})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Account Created"}),(0,Za.jsxs)("dd",{className:"mt-1 text-sm text-gray-900 flex items-center",children:[(0,Za.jsx)(El,{className:"h-4 w-4 mr-2 text-gray-400"}),null!==e&&void 0!==e&&e.createdAt?new Date(e.createdAt).toLocaleDateString():"Unknown"]})]})]})})]}),(0,Za.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,Za.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[(0,Za.jsx)(Yl,{className:"h-5 w-5 mr-2"}),"Security Settings"]})}),(0,Za.jsx)("div",{className:"p-6",children:s?(0,Za.jsxs)("form",{onSubmit:m(async e=>{try{await Ia({currentPassword:e.currentPassword,newPassword:e.newPassword}),en.success("Password changed successfully"),o(!1),h()}catch(r){var t,n;console.error("Error changing password:",r);const e=(null===(t=r.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.message)||"Failed to change password";en.error(e)}}),className:"space-y-6",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-4",children:"Change Password"}),(0,Za.jsxs)("div",{className:"space-y-4",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Current Password"}),(0,Za.jsxs)("div",{className:"mt-1 relative",children:[(0,Za.jsx)("input",c(c({},f("currentPassword")),{},{type:i.current?"text":"password",className:"block w-full pr-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your current password"})),(0,Za.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>g("current"),children:i.current?(0,Za.jsx)(ml,{className:"h-5 w-5 text-gray-400"}):(0,Za.jsx)(pl,{className:"h-5 w-5 text-gray-400"})})]}),p.currentPassword&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.currentPassword.message})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"New Password"}),(0,Za.jsxs)("div",{className:"mt-1 relative",children:[(0,Za.jsx)("input",c(c({},f("newPassword")),{},{type:i.new?"text":"password",className:"block w-full pr-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your new password"})),(0,Za.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>g("new"),children:i.new?(0,Za.jsx)(ml,{className:"h-5 w-5 text-gray-400"}):(0,Za.jsx)(pl,{className:"h-5 w-5 text-gray-400"})})]}),p.newPassword&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.newPassword.message}),(0,Za.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Password must contain at least 8 characters with uppercase, lowercase, number and special character."})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"Confirm New Password"}),(0,Za.jsxs)("div",{className:"mt-1 relative",children:[(0,Za.jsx)("input",c(c({},f("confirmPassword")),{},{type:i.confirm?"text":"password",className:"block w-full pr-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Confirm your new password"})),(0,Za.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>g("confirm"),children:i.confirm?(0,Za.jsx)(ml,{className:"h-5 w-5 text-gray-400"}):(0,Za.jsx)(pl,{className:"h-5 w-5 text-gray-400"})})]}),p.confirmPassword&&(0,Za.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.confirmPassword.message})]})]})]}),(0,Za.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,Za.jsx)("button",{type:"button",onClick:()=>{o(!1),h()},className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,Za.jsx)("button",{type:"submit",className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700",children:"Update Password"})]})]}):(0,Za.jsxs)("div",{className:"space-y-4",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Password"}),(0,Za.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Last changed: Unknown"})]}),(0,Za.jsxs)("button",{onClick:()=>o(!0),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:[(0,Za.jsx)(Yl,{className:"h-4 w-4 mr-2"}),"Change Password"]})]})})]}),(0,Za.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,Za.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900 flex items-center",children:[(0,Za.jsx)(Il,{className:"h-5 w-5 mr-2"}),"Recent Activity"]})}),(0,Za.jsx)("div",{className:"p-6",children:(0,Za.jsxs)("div",{className:"text-center py-8",children:[(0,Za.jsx)(Il,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,Za.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"No recent activity"}),(0,Za.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Your account activity will appear here."})]})})]})]})},ec=()=>{const[e,n]=(0,t.useState)([]),[r,a]=(0,t.useState)(!0),[s,o]=(0,t.useState)("Pending"),[i,l]=(0,t.useState)([]),[u,d]=(0,t.useState)(!1),[f,m]=(0,t.useState)({status:"",comments:""}),p=[{value:"Pending",label:"Pending Review",color:"bg-yellow-100 text-yellow-800"},{value:"UnderReview",label:"Under Review",color:"bg-blue-100 text-blue-800"},{value:"Approved",label:"Approved",color:"bg-green-100 text-green-800"},{value:"Rejected",label:"Rejected",color:"bg-red-100 text-red-800"},{value:"ResubmissionRequired",label:"Resubmission Required",color:"bg-orange-100 text-orange-800"}],h={0:"Aadhar Card",1:"PAN Card",2:"Photo",3:"Bank Passbook",4:"Address Proof",5:"Business Registration",6:"GST Certificate",7:"Other"};(0,t.useEffect)(()=>{g()},[s]);const g=async()=>{try{a(!0);const e=await Ya(s);n(e)}catch(e){console.error("Error fetching documents:",e),en.error("Failed to fetch documents")}finally{a(!1)}},y=async(e,t)=>{try{const n=await Xa(e),r=window.URL.createObjectURL(n),a=document.createElement("a");a.href=r,a.download=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(r),en.success("Document downloaded successfully")}catch(n){console.error("Error downloading document:",n),en.error("Failed to download document")}},v=async function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";try{await Ja(e,t,n),en.success("Document status updated successfully"),g()}catch(r){console.error("Error updating document status:",r),en.error("Failed to update document status")}},b=e=>{const t=p.find(t=>t.value===e);return t?t.color:"bg-gray-100 text-gray-800"};return r?(0,Za.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,Za.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,Za.jsxs)("div",{className:"space-y-6",children:[(0,Za.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,Za.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,Za.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,Za.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Document Management"}),(0,Za.jsxs)("div",{className:"flex space-x-4",children:[(0,Za.jsx)("select",{value:s,onChange:e=>o(e.target.value),className:"rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",children:p.map(e=>(0,Za.jsx)("option",{value:e.value,children:e.label},e.value))}),i.length>0&&(0,Za.jsxs)("button",{onClick:()=>d(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:["Update Selected (",i.length,")"]})]})]}),(0,Za.jsx)("div",{className:"overflow-x-auto",children:(0,Za.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,Za.jsx)("thead",{className:"bg-gray-50",children:(0,Za.jsxs)("tr",{children:[(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:(0,Za.jsx)("input",{type:"checkbox",checked:i.length===e.length&&e.length>0,onChange:()=>{i.length===e.length?l([]):l(e.map(e=>e.documentId))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Agent"}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Document Type"}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"File Name"}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Size"}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Uploaded"}),(0,Za.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,Za.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:0===e.length?(0,Za.jsx)("tr",{children:(0,Za.jsxs)("td",{colSpan:"8",className:"px-6 py-4 text-center text-gray-500",children:["No documents found for status: ",s]})}):e.map(e=>{var t,n;return(0,Za.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,Za.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Za.jsx)("input",{type:"checkbox",checked:i.includes(e.documentId),onChange:()=>{return t=e.documentId,void l(e=>e.includes(t)?e.filter(e=>e!==t):[...e,t]);var t},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),(0,Za.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,Za.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.agentName}),(0,Za.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.agentId]})]}),(0,Za.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Za.jsx)("span",{className:"text-sm text-gray-900",children:h[e.documentType]})}),(0,Za.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Za.jsx)("span",{className:"text-sm text-gray-900",children:e.fileName})}),(0,Za.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Za.jsx)("span",{className:"text-sm text-gray-500",children:e.fileSize})}),(0,Za.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,Za.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(b(e.status)),children:(null===(t=p.find(t=>t.value===e.status))||void 0===t?void 0:t.label)||e.status})}),(0,Za.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(n=e.uploadedAt,new Date(n).toLocaleDateString("en-IN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}))}),(0,Za.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,Za.jsx)("button",{onClick:()=>y(e.documentId,e.fileName),className:"text-blue-600 hover:text-blue-900",children:"Download"}),"Pending"===e.status&&(0,Za.jsxs)(Za.Fragment,{children:[(0,Za.jsx)("button",{onClick:()=>v(e.documentId,"Approved"),className:"text-green-600 hover:text-green-900",children:"Approve"}),(0,Za.jsx)("button",{onClick:()=>v(e.documentId,"Rejected","Document rejected by reviewer"),className:"text-red-600 hover:text-red-900",children:"Reject"})]})]})]},e.documentId)})})]})})]})}),u&&(0,Za.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,Za.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,Za.jsxs)("div",{className:"mt-3",children:[(0,Za.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:["Update Status for ",i.length," Documents"]}),(0,Za.jsxs)("div",{className:"space-y-4",children:[(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Status"}),(0,Za.jsxs)("select",{value:f.status,onChange:e=>m(t=>c(c({},t),{},{status:e.target.value})),className:"w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",children:[(0,Za.jsx)("option",{value:"",children:"Select Status"}),p.map(e=>(0,Za.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,Za.jsxs)("div",{children:[(0,Za.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Comments (Optional)"}),(0,Za.jsx)("textarea",{value:f.comments,onChange:e=>m(t=>c(c({},t),{},{comments:e.target.value})),rows:3,className:"w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500",placeholder:"Add comments for the status update..."})]})]}),(0,Za.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,Za.jsx)("button",{onClick:()=>{d(!1),m({status:"",comments:""})},className:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200",children:"Cancel"}),(0,Za.jsx)("button",{onClick:async()=>{if(0!==i.length)try{await Ga(i,f.status,f.comments),en.success("".concat(i.length," documents updated successfully")),l([]),d(!1),m({status:"",comments:""}),g()}catch(e){console.error("Error updating documents:",e),en.error("Failed to update documents")}else en.warning("Please select documents to update")},disabled:!f.status,className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Update Status"})]})]})})})]})};const tc=function(){return(0,Za.jsx)(ns,{children:(0,Za.jsx)(ht,{children:(0,Za.jsxs)("div",{className:"App",children:[(0,Za.jsx)(dn,{position:"top-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0}),(0,Za.jsxs)(Fe,{children:[(0,Za.jsx)(Oe,{path:"/login",element:(0,Za.jsx)(gl,{})}),(0,Za.jsx)(Oe,{path:"/register",element:(0,Za.jsx)(Al,{})}),(0,Za.jsxs)(Oe,{path:"/",element:(0,Za.jsx)(rs,{children:(0,Za.jsx)(xs,{})}),children:[(0,Za.jsx)(Oe,{index:!0,element:(0,Za.jsx)(Ae,{to:"/dashboard",replace:!0})}),(0,Za.jsx)(Oe,{path:"dashboard",element:(0,Za.jsx)(kl,{})}),(0,Za.jsx)(Oe,{path:"admin/register",element:(0,Za.jsx)(Al,{})}),(0,Za.jsx)(Oe,{path:"agents",element:(0,Za.jsx)(zl,{})}),(0,Za.jsx)(Oe,{path:"agents/:id",element:(0,Za.jsx)(Wl,{})}),(0,Za.jsx)(Oe,{path:"documents",element:(0,Za.jsx)(ec,{})}),(0,Za.jsx)(Oe,{path:"profile",element:(0,Za.jsx)(Zl,{})})]}),(0,Za.jsx)(Oe,{path:"*",element:(0,Za.jsx)(Ae,{to:"/dashboard",replace:!0})})]})]})})})},nc=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,453)).then(t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:s,getTTFB:o}=t;n(e),r(e),a(e),s(e),o(e)})};r.createRoot(document.getElementById("root")).render((0,Za.jsx)(t.StrictMode,{children:(0,Za.jsx)(tc,{})})),nc()})()})();
//# sourceMappingURL=main.b6945cb7.js.map