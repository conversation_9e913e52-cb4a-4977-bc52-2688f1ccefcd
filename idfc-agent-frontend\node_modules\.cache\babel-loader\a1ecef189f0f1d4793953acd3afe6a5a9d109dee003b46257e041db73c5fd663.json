{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\IDFCAgentOnboardingAndManagementSolution\\\\idfc-agent-frontend\\\\src\\\\components\\\\WorkflowTimeline.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { CheckCircle, Clock, AlertCircle, User, FileText, Shield, Activity, Calendar, MessageSquare } from 'lucide-react';\nimport { workflowAPI } from '../services/api';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkflowTimeline = ({\n  agentId,\n  showActions = false\n}) => {\n  _s();\n  const [workflowSummary, setWorkflowSummary] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [processing, setProcessing] = useState(false);\n  useEffect(() => {\n    fetchWorkflowSummary();\n  }, [agentId]);\n  const fetchWorkflowSummary = async () => {\n    try {\n      setLoading(true);\n      const summary = await workflowAPI.getWorkflowSummary(agentId);\n      setWorkflowSummary(summary);\n    } catch (error) {\n      console.error('Error fetching workflow summary:', error);\n      toast.error('Failed to load workflow information');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAutoProgress = async () => {\n    try {\n      setProcessing(true);\n      await workflowAPI.autoProgressWorkflow(agentId);\n      toast.success('Workflow auto-progressed successfully');\n      await fetchWorkflowSummary();\n    } catch (error) {\n      console.error('Error auto-progressing workflow:', error);\n      toast.error('Failed to auto-progress workflow');\n    } finally {\n      setProcessing(false);\n    }\n  };\n  const getStepIcon = step => {\n    if (step.isCompleted) {\n      return /*#__PURE__*/_jsxDEV(CheckCircle, {\n        className: \"w-6 h-6 text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 14\n      }, this);\n    } else if (step.isCurrent) {\n      return /*#__PURE__*/_jsxDEV(Clock, {\n        className: \"w-6 h-6 text-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 14\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-6 h-6 rounded-full border-2 border-gray-300 bg-white\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 14\n      }, this);\n    }\n  };\n  const getStatusIcon = status => {\n    const iconMap = {\n      0: User,\n      // Pending\n      1: FileText,\n      // UnderReview\n      2: Shield,\n      // Approved\n      4: Activity,\n      // Active\n      3: AlertCircle,\n      // Rejected\n      5: Clock,\n      // Inactive\n      6: AlertCircle // Suspended\n    };\n    const IconComponent = iconMap[status] || User;\n    return /*#__PURE__*/_jsxDEV(IconComponent, {\n      className: \"w-4 h-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 12\n    }, this);\n  };\n  const formatDuration = timeSpan => {\n    if (!timeSpan) return 'N/A';\n    const totalHours = Math.floor(timeSpan.totalHours || 0);\n    const days = Math.floor(totalHours / 24);\n    const hours = totalHours % 24;\n    if (days > 0) {\n      return `${days}d ${hours}h`;\n    } else if (hours > 0) {\n      return `${hours}h`;\n    } else {\n      return '< 1h';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"animate-pulse\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-4 bg-gray-200 rounded w-1/4 mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [1, 2, 3, 4].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 bg-gray-200 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 h-4 bg-gray-200 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this)]\n        }, i, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this);\n  }\n  if (!workflowSummary) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Unable to load workflow information\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-sm border p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900\",\n        children: \"Onboarding Progress\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), showActions && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleAutoProgress,\n        disabled: processing,\n        className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n        children: processing ? 'Processing...' : 'Auto Progress'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium text-gray-700\",\n          children: [\"Progress: \", workflowSummary.completedSteps, \" of \", workflowSummary.totalSteps, \" steps\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm text-gray-500\",\n          children: [Math.round(workflowSummary.progressPercentage), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full bg-gray-200 rounded-full h-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n          style: {\n            width: `${workflowSummary.progressPercentage}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 p-4 bg-blue-50 rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [getStatusIcon(workflowSummary.currentStatus), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-blue-900\",\n            children: [\"Current Status: \", workflowSummary.currentStepName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-blue-700\",\n            children: [\"Time in current step: \", formatDuration(workflowSummary.totalTimeSpent)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), workflowSummary.estimatedRemainingTime && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-blue-700\",\n            children: [\"Estimated remaining: \", formatDuration(workflowSummary.estimatedRemainingTime)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), workflowSummary.isBlocked && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"font-medium text-red-900\",\n            children: \"Workflow Blocked\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-red-700\",\n            children: workflowSummary.blockedReason\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: workflowSummary.steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [index < workflowSummary.steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute left-3 top-8 w-0.5 h-12 bg-gray-200\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: getStepIcon(step)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: `font-medium ${step.isCompleted ? 'text-green-700' : step.isCurrent ? 'text-blue-700' : 'text-gray-500'}`,\n                children: step.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), step.completedAt && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: new Date(step.completedAt).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), step.requirements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mb-1\",\n                children: \"Requirements:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"text-sm text-gray-500 space-y-1\",\n                children: step.requirements.map((req, reqIndex) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-1.5 h-1.5 bg-gray-400 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: req\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 27\n                  }, this)]\n                }, reqIndex, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 19\n            }, this), step.completedBy && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2 flex items-center space-x-2 text-sm text-gray-500\",\n              children: [/*#__PURE__*/_jsxDEV(User, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Completed by: \", step.completedBy]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this), step.requiresManualReview && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\",\n                children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                  className: \"w-3 h-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 23\n                }, this), \"Manual Review Required\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)]\n      }, step.status, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 pt-6 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-4 text-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-500\",\n            children: \"Total Time:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 font-medium\",\n            children: formatDuration(workflowSummary.totalTimeSpent)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-500\",\n            children: \"Completion:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 font-medium\",\n            children: [Math.round(workflowSummary.progressPercentage), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkflowTimeline, \"OGeaZIi47yaefTKkAg6L6vZJOyU=\");\n_c = WorkflowTimeline;\nexport default WorkflowTimeline;\nvar _c;\n$RefreshReg$(_c, \"WorkflowTimeline\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "CheckCircle", "Clock", "AlertCircle", "User", "FileText", "Shield", "Activity", "Calendar", "MessageSquare", "workflowAPI", "toast", "jsxDEV", "_jsxDEV", "WorkflowTimeline", "agentId", "showActions", "_s", "workflowSummary", "setWorkflowSummary", "loading", "setLoading", "processing", "setProcessing", "fetchWorkflowSummary", "summary", "getWorkflowSummary", "error", "console", "handleAutoProgress", "autoProgressWorkflow", "success", "getStepIcon", "step", "isCompleted", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isCurrent", "getStatusIcon", "status", "iconMap", "IconComponent", "formatDuration", "timeSpan", "totalHours", "Math", "floor", "days", "hours", "children", "map", "i", "onClick", "disabled", "completedSteps", "totalSteps", "round", "progressPercentage", "style", "width", "currentStatus", "currentStepName", "totalTimeSpent", "estimatedRemainingTime", "isBlocked", "blockedReason", "steps", "index", "length", "name", "completedAt", "Date", "toLocaleDateString", "requirements", "req", "reqIndex", "completedBy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/IDFCAgentOnboardingAndManagementSolution/idfc-agent-frontend/src/components/WorkflowTimeline.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  CheckCircle, \n  Clock, \n  AlertCircle, \n  User, \n  FileText, \n  Shield, \n  Activity,\n  Calendar,\n  MessageSquare\n} from 'lucide-react';\nimport { workflowAPI } from '../services/api';\nimport { toast } from 'react-toastify';\n\nconst WorkflowTimeline = ({ agentId, showActions = false }) => {\n  const [workflowSummary, setWorkflowSummary] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [processing, setProcessing] = useState(false);\n\n  useEffect(() => {\n    fetchWorkflowSummary();\n  }, [agentId]);\n\n  const fetchWorkflowSummary = async () => {\n    try {\n      setLoading(true);\n      const summary = await workflowAPI.getWorkflowSummary(agentId);\n      setWorkflowSummary(summary);\n    } catch (error) {\n      console.error('Error fetching workflow summary:', error);\n      toast.error('Failed to load workflow information');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAutoProgress = async () => {\n    try {\n      setProcessing(true);\n      await workflowAPI.autoProgressWorkflow(agentId);\n      toast.success('Workflow auto-progressed successfully');\n      await fetchWorkflowSummary();\n    } catch (error) {\n      console.error('Error auto-progressing workflow:', error);\n      toast.error('Failed to auto-progress workflow');\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  const getStepIcon = (step) => {\n    if (step.isCompleted) {\n      return <CheckCircle className=\"w-6 h-6 text-green-500\" />;\n    } else if (step.isCurrent) {\n      return <Clock className=\"w-6 h-6 text-blue-500\" />;\n    } else {\n      return <div className=\"w-6 h-6 rounded-full border-2 border-gray-300 bg-white\" />;\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    const iconMap = {\n      0: User,        // Pending\n      1: FileText,    // UnderReview\n      2: Shield,      // Approved\n      4: Activity,    // Active\n      3: AlertCircle, // Rejected\n      5: Clock,       // Inactive\n      6: AlertCircle  // Suspended\n    };\n    \n    const IconComponent = iconMap[status] || User;\n    return <IconComponent className=\"w-4 h-4\" />;\n  };\n\n  const formatDuration = (timeSpan) => {\n    if (!timeSpan) return 'N/A';\n    \n    const totalHours = Math.floor(timeSpan.totalHours || 0);\n    const days = Math.floor(totalHours / 24);\n    const hours = totalHours % 24;\n    \n    if (days > 0) {\n      return `${days}d ${hours}h`;\n    } else if (hours > 0) {\n      return `${hours}h`;\n    } else {\n      return '< 1h';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"animate-pulse\">\n        <div className=\"h-4 bg-gray-200 rounded w-1/4 mb-4\"></div>\n        <div className=\"space-y-4\">\n          {[1, 2, 3, 4].map((i) => (\n            <div key={i} className=\"flex items-center space-x-4\">\n              <div className=\"w-6 h-6 bg-gray-200 rounded-full\"></div>\n              <div className=\"flex-1 h-4 bg-gray-200 rounded\"></div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (!workflowSummary) {\n    return (\n      <div className=\"text-center py-8\">\n        <AlertCircle className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n        <p className=\"text-gray-500\">Unable to load workflow information</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Onboarding Progress</h3>\n        {showActions && (\n          <button\n            onClick={handleAutoProgress}\n            disabled={processing}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm\"\n          >\n            {processing ? 'Processing...' : 'Auto Progress'}\n          </button>\n        )}\n      </div>\n\n      {/* Progress Bar */}\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <span className=\"text-sm font-medium text-gray-700\">\n            Progress: {workflowSummary.completedSteps} of {workflowSummary.totalSteps} steps\n          </span>\n          <span className=\"text-sm text-gray-500\">\n            {Math.round(workflowSummary.progressPercentage)}%\n          </span>\n        </div>\n        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n          <div\n            className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n            style={{ width: `${workflowSummary.progressPercentage}%` }}\n          ></div>\n        </div>\n      </div>\n\n      {/* Current Status */}\n      <div className=\"mb-6 p-4 bg-blue-50 rounded-lg\">\n        <div className=\"flex items-center space-x-3\">\n          {getStatusIcon(workflowSummary.currentStatus)}\n          <div>\n            <h4 className=\"font-medium text-blue-900\">\n              Current Status: {workflowSummary.currentStepName}\n            </h4>\n            <p className=\"text-sm text-blue-700\">\n              Time in current step: {formatDuration(workflowSummary.totalTimeSpent)}\n            </p>\n            {workflowSummary.estimatedRemainingTime && (\n              <p className=\"text-sm text-blue-700\">\n                Estimated remaining: {formatDuration(workflowSummary.estimatedRemainingTime)}\n              </p>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Blocked Status */}\n      {workflowSummary.isBlocked && (\n        <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\">\n          <div className=\"flex items-center space-x-3\">\n            <AlertCircle className=\"w-5 h-5 text-red-500\" />\n            <div>\n              <h4 className=\"font-medium text-red-900\">Workflow Blocked</h4>\n              <p className=\"text-sm text-red-700\">{workflowSummary.blockedReason}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Timeline Steps */}\n      <div className=\"space-y-6\">\n        {workflowSummary.steps.map((step, index) => (\n          <div key={step.status} className=\"relative\">\n            {/* Connector Line */}\n            {index < workflowSummary.steps.length - 1 && (\n              <div className=\"absolute left-3 top-8 w-0.5 h-12 bg-gray-200\"></div>\n            )}\n            \n            <div className=\"flex items-start space-x-4\">\n              {/* Step Icon */}\n              <div className=\"flex-shrink-0\">\n                {getStepIcon(step)}\n              </div>\n              \n              {/* Step Content */}\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center justify-between\">\n                  <h4 className={`font-medium ${\n                    step.isCompleted ? 'text-green-700' : \n                    step.isCurrent ? 'text-blue-700' : \n                    'text-gray-500'\n                  }`}>\n                    {step.name}\n                  </h4>\n                  \n                  {step.completedAt && (\n                    <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n                      <Calendar className=\"w-4 h-4\" />\n                      <span>{new Date(step.completedAt).toLocaleDateString()}</span>\n                    </div>\n                  )}\n                </div>\n                \n                {/* Step Requirements */}\n                {step.requirements.length > 0 && (\n                  <div className=\"mt-2\">\n                    <p className=\"text-sm text-gray-600 mb-1\">Requirements:</p>\n                    <ul className=\"text-sm text-gray-500 space-y-1\">\n                      {step.requirements.map((req, reqIndex) => (\n                        <li key={reqIndex} className=\"flex items-center space-x-2\">\n                          <div className=\"w-1.5 h-1.5 bg-gray-400 rounded-full\"></div>\n                          <span>{req}</span>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n                \n                {/* Completed By */}\n                {step.completedBy && (\n                  <div className=\"mt-2 flex items-center space-x-2 text-sm text-gray-500\">\n                    <User className=\"w-4 h-4\" />\n                    <span>Completed by: {step.completedBy}</span>\n                  </div>\n                )}\n                \n                {/* Manual Review Badge */}\n                {step.requiresManualReview && (\n                  <div className=\"mt-2\">\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                      <MessageSquare className=\"w-3 h-3 mr-1\" />\n                      Manual Review Required\n                    </span>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Summary Stats */}\n      <div className=\"mt-6 pt-6 border-t border-gray-200\">\n        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n          <div>\n            <span className=\"text-gray-500\">Total Time:</span>\n            <span className=\"ml-2 font-medium\">{formatDuration(workflowSummary.totalTimeSpent)}</span>\n          </div>\n          <div>\n            <span className=\"text-gray-500\">Completion:</span>\n            <span className=\"ml-2 font-medium\">{Math.round(workflowSummary.progressPercentage)}%</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WorkflowTimeline;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,WAAW,EACXC,KAAK,EACLC,WAAW,EACXC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,aAAa,QACR,cAAc;AACrB,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,WAAW,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdwB,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACT,OAAO,CAAC,CAAC;EAEb,MAAMS,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,OAAO,GAAG,MAAMf,WAAW,CAACgB,kBAAkB,CAACX,OAAO,CAAC;MAC7DI,kBAAkB,CAACM,OAAO,CAAC;IAC7B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDhB,KAAK,CAACgB,KAAK,CAAC,qCAAqC,CAAC;IACpD,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFN,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMb,WAAW,CAACoB,oBAAoB,CAACf,OAAO,CAAC;MAC/CJ,KAAK,CAACoB,OAAO,CAAC,uCAAuC,CAAC;MACtD,MAAMP,oBAAoB,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDhB,KAAK,CAACgB,KAAK,CAAC,kCAAkC,CAAC;IACjD,CAAC,SAAS;MACRJ,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMS,WAAW,GAAIC,IAAI,IAAK;IAC5B,IAAIA,IAAI,CAACC,WAAW,EAAE;MACpB,oBAAOrB,OAAA,CAACZ,WAAW;QAACkC,SAAS,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC3D,CAAC,MAAM,IAAIN,IAAI,CAACO,SAAS,EAAE;MACzB,oBAAO3B,OAAA,CAACX,KAAK;QAACiC,SAAS,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACpD,CAAC,MAAM;MACL,oBAAO1B,OAAA;QAAKsB,SAAS,EAAC;MAAwD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACnF;EACF,CAAC;EAED,MAAME,aAAa,GAAIC,MAAM,IAAK;IAChC,MAAMC,OAAO,GAAG;MACd,CAAC,EAAEvC,IAAI;MAAS;MAChB,CAAC,EAAEC,QAAQ;MAAK;MAChB,CAAC,EAAEC,MAAM;MAAO;MAChB,CAAC,EAAEC,QAAQ;MAAK;MAChB,CAAC,EAAEJ,WAAW;MAAE;MAChB,CAAC,EAAED,KAAK;MAAQ;MAChB,CAAC,EAAEC,WAAW,CAAE;IAClB,CAAC;IAED,MAAMyC,aAAa,GAAGD,OAAO,CAACD,MAAM,CAAC,IAAItC,IAAI;IAC7C,oBAAOS,OAAA,CAAC+B,aAAa;MAACT,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC9C,CAAC;EAED,MAAMM,cAAc,GAAIC,QAAQ,IAAK;IACnC,IAAI,CAACA,QAAQ,EAAE,OAAO,KAAK;IAE3B,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAACC,UAAU,IAAI,CAAC,CAAC;IACvD,MAAMG,IAAI,GAAGF,IAAI,CAACC,KAAK,CAACF,UAAU,GAAG,EAAE,CAAC;IACxC,MAAMI,KAAK,GAAGJ,UAAU,GAAG,EAAE;IAE7B,IAAIG,IAAI,GAAG,CAAC,EAAE;MACZ,OAAO,GAAGA,IAAI,KAAKC,KAAK,GAAG;IAC7B,CAAC,MAAM,IAAIA,KAAK,GAAG,CAAC,EAAE;MACpB,OAAO,GAAGA,KAAK,GAAG;IACpB,CAAC,MAAM;MACL,OAAO,MAAM;IACf;EACF,CAAC;EAED,IAAI/B,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKsB,SAAS,EAAC,eAAe;MAAAiB,QAAA,gBAC5BvC,OAAA;QAAKsB,SAAS,EAAC;MAAoC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1D1B,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAAiB,QAAA,EACvB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,CAAC,iBAClBzC,OAAA;UAAasB,SAAS,EAAC,6BAA6B;UAAAiB,QAAA,gBAClDvC,OAAA;YAAKsB,SAAS,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxD1B,OAAA;YAAKsB,SAAS,EAAC;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAF9Ce,CAAC;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGN,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACrB,eAAe,EAAE;IACpB,oBACEL,OAAA;MAAKsB,SAAS,EAAC,kBAAkB;MAAAiB,QAAA,gBAC/BvC,OAAA,CAACV,WAAW;QAACgC,SAAS,EAAC;MAAsC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChE1B,OAAA;QAAGsB,SAAS,EAAC,eAAe;QAAAiB,QAAA,EAAC;MAAmC;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC;EAEV;EAEA,oBACE1B,OAAA;IAAKsB,SAAS,EAAC,0CAA0C;IAAAiB,QAAA,gBACvDvC,OAAA;MAAKsB,SAAS,EAAC,wCAAwC;MAAAiB,QAAA,gBACrDvC,OAAA;QAAIsB,SAAS,EAAC,qCAAqC;QAAAiB,QAAA,EAAC;MAAmB;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC3EvB,WAAW,iBACVH,OAAA;QACE0C,OAAO,EAAE1B,kBAAmB;QAC5B2B,QAAQ,EAAElC,UAAW;QACrBa,SAAS,EAAC,uHAAuH;QAAAiB,QAAA,EAEhI9B,UAAU,GAAG,eAAe,GAAG;MAAe;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN1B,OAAA;MAAKsB,SAAS,EAAC,MAAM;MAAAiB,QAAA,gBACnBvC,OAAA;QAAKsB,SAAS,EAAC,wCAAwC;QAAAiB,QAAA,gBACrDvC,OAAA;UAAMsB,SAAS,EAAC,mCAAmC;UAAAiB,QAAA,GAAC,YACxC,EAAClC,eAAe,CAACuC,cAAc,EAAC,MAAI,EAACvC,eAAe,CAACwC,UAAU,EAAC,QAC5E;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP1B,OAAA;UAAMsB,SAAS,EAAC,uBAAuB;UAAAiB,QAAA,GACpCJ,IAAI,CAACW,KAAK,CAACzC,eAAe,CAAC0C,kBAAkB,CAAC,EAAC,GAClD;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1B,OAAA;QAAKsB,SAAS,EAAC,qCAAqC;QAAAiB,QAAA,eAClDvC,OAAA;UACEsB,SAAS,EAAC,0DAA0D;UACpE0B,KAAK,EAAE;YAAEC,KAAK,EAAE,GAAG5C,eAAe,CAAC0C,kBAAkB;UAAI;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1B,OAAA;MAAKsB,SAAS,EAAC,gCAAgC;MAAAiB,QAAA,eAC7CvC,OAAA;QAAKsB,SAAS,EAAC,6BAA6B;QAAAiB,QAAA,GACzCX,aAAa,CAACvB,eAAe,CAAC6C,aAAa,CAAC,eAC7ClD,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAIsB,SAAS,EAAC,2BAA2B;YAAAiB,QAAA,GAAC,kBACxB,EAAClC,eAAe,CAAC8C,eAAe;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACL1B,OAAA;YAAGsB,SAAS,EAAC,uBAAuB;YAAAiB,QAAA,GAAC,wBACb,EAACP,cAAc,CAAC3B,eAAe,CAAC+C,cAAc,CAAC;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,EACHrB,eAAe,CAACgD,sBAAsB,iBACrCrD,OAAA;YAAGsB,SAAS,EAAC,uBAAuB;YAAAiB,QAAA,GAAC,uBACd,EAACP,cAAc,CAAC3B,eAAe,CAACgD,sBAAsB,CAAC;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLrB,eAAe,CAACiD,SAAS,iBACxBtD,OAAA;MAAKsB,SAAS,EAAC,qDAAqD;MAAAiB,QAAA,eAClEvC,OAAA;QAAKsB,SAAS,EAAC,6BAA6B;QAAAiB,QAAA,gBAC1CvC,OAAA,CAACV,WAAW;UAACgC,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChD1B,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAIsB,SAAS,EAAC,0BAA0B;YAAAiB,QAAA,EAAC;UAAgB;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9D1B,OAAA;YAAGsB,SAAS,EAAC,sBAAsB;YAAAiB,QAAA,EAAElC,eAAe,CAACkD;UAAa;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD1B,OAAA;MAAKsB,SAAS,EAAC,WAAW;MAAAiB,QAAA,EACvBlC,eAAe,CAACmD,KAAK,CAAChB,GAAG,CAAC,CAACpB,IAAI,EAAEqC,KAAK,kBACrCzD,OAAA;QAAuBsB,SAAS,EAAC,UAAU;QAAAiB,QAAA,GAExCkB,KAAK,GAAGpD,eAAe,CAACmD,KAAK,CAACE,MAAM,GAAG,CAAC,iBACvC1D,OAAA;UAAKsB,SAAS,EAAC;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACpE,eAED1B,OAAA;UAAKsB,SAAS,EAAC,4BAA4B;UAAAiB,QAAA,gBAEzCvC,OAAA;YAAKsB,SAAS,EAAC,eAAe;YAAAiB,QAAA,EAC3BpB,WAAW,CAACC,IAAI;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAGN1B,OAAA;YAAKsB,SAAS,EAAC,gBAAgB;YAAAiB,QAAA,gBAC7BvC,OAAA;cAAKsB,SAAS,EAAC,mCAAmC;cAAAiB,QAAA,gBAChDvC,OAAA;gBAAIsB,SAAS,EAAE,eACbF,IAAI,CAACC,WAAW,GAAG,gBAAgB,GACnCD,IAAI,CAACO,SAAS,GAAG,eAAe,GAChC,eAAe,EACd;gBAAAY,QAAA,EACAnB,IAAI,CAACuC;cAAI;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,EAEJN,IAAI,CAACwC,WAAW,iBACf5D,OAAA;gBAAKsB,SAAS,EAAC,mDAAmD;gBAAAiB,QAAA,gBAChEvC,OAAA,CAACL,QAAQ;kBAAC2B,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChC1B,OAAA;kBAAAuC,QAAA,EAAO,IAAIsB,IAAI,CAACzC,IAAI,CAACwC,WAAW,CAAC,CAACE,kBAAkB,CAAC;gBAAC;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGLN,IAAI,CAAC2C,YAAY,CAACL,MAAM,GAAG,CAAC,iBAC3B1D,OAAA;cAAKsB,SAAS,EAAC,MAAM;cAAAiB,QAAA,gBACnBvC,OAAA;gBAAGsB,SAAS,EAAC,4BAA4B;gBAAAiB,QAAA,EAAC;cAAa;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3D1B,OAAA;gBAAIsB,SAAS,EAAC,iCAAiC;gBAAAiB,QAAA,EAC5CnB,IAAI,CAAC2C,YAAY,CAACvB,GAAG,CAAC,CAACwB,GAAG,EAAEC,QAAQ,kBACnCjE,OAAA;kBAAmBsB,SAAS,EAAC,6BAA6B;kBAAAiB,QAAA,gBACxDvC,OAAA;oBAAKsB,SAAS,EAAC;kBAAsC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5D1B,OAAA;oBAAAuC,QAAA,EAAOyB;kBAAG;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFXuC,QAAQ;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGb,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACN,EAGAN,IAAI,CAAC8C,WAAW,iBACflE,OAAA;cAAKsB,SAAS,EAAC,wDAAwD;cAAAiB,QAAA,gBACrEvC,OAAA,CAACT,IAAI;gBAAC+B,SAAS,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5B1B,OAAA;gBAAAuC,QAAA,GAAM,gBAAc,EAACnB,IAAI,CAAC8C,WAAW;cAAA;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACN,EAGAN,IAAI,CAAC+C,oBAAoB,iBACxBnE,OAAA;cAAKsB,SAAS,EAAC,MAAM;cAAAiB,QAAA,eACnBvC,OAAA;gBAAMsB,SAAS,EAAC,uGAAuG;gBAAAiB,QAAA,gBACrHvC,OAAA,CAACJ,aAAa;kBAAC0B,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,0BAE5C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GAhEEN,IAAI,CAACS,MAAM;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiEhB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN1B,OAAA;MAAKsB,SAAS,EAAC,oCAAoC;MAAAiB,QAAA,eACjDvC,OAAA;QAAKsB,SAAS,EAAC,gCAAgC;QAAAiB,QAAA,gBAC7CvC,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAMsB,SAAS,EAAC,eAAe;YAAAiB,QAAA,EAAC;UAAW;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClD1B,OAAA;YAAMsB,SAAS,EAAC,kBAAkB;YAAAiB,QAAA,EAAEP,cAAc,CAAC3B,eAAe,CAAC+C,cAAc;UAAC;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC,eACN1B,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAMsB,SAAS,EAAC,eAAe;YAAAiB,QAAA,EAAC;UAAW;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClD1B,OAAA;YAAMsB,SAAS,EAAC,kBAAkB;YAAAiB,QAAA,GAAEJ,IAAI,CAACW,KAAK,CAACzC,eAAe,CAAC0C,kBAAkB,CAAC,EAAC,GAAC;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CA/PIH,gBAAgB;AAAAmE,EAAA,GAAhBnE,gBAAgB;AAiQtB,eAAeA,gBAAgB;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}