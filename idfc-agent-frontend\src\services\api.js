import axios from 'axios';
import { toast } from 'react-toastify';

const API_BASE_URL = 'https://localhost:7093/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error intercepted:', error);

    if (error.code === 'ERR_NETWORK') {
      console.error('Network error - API might be down or CORS issue');
      toast.error('Unable to connect to server. Please check if the API is running.');
    } else if (error.code === 'ERR_CERT_AUTHORITY_INVALID') {
      console.error('SSL Certificate error');
      toast.error('SSL Certificate error. Please check API configuration.');
    } else if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    } else {
      const message = error.response?.data?.message || 'An error occurred';
      toast.error(message);
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (credentials) => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },
  
  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },
  
  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data;
  },
  
  changePassword: async (passwordData) => {
    const response = await api.post('/auth/change-password', passwordData);
    return response.data;
  }
};

// Agent API
export const agentAPI = {
  register: async (agentData) => {
    console.log('Making API call to register agent:', agentData);
    console.log('Full URL:', `${API_BASE_URL}/agents/register`);
    const response = await api.post('/agents/register', agentData);
    console.log('API response received:', response);
    return response.data;
  },
  
  getAll: async (page = 1, pageSize = 10, status = null) => {
    const params = { page, pageSize };
    if (status) params.status = status;
    
    const response = await api.get('/agents', { params });
    return response.data;
  },
  
  getById: async (id) => {
    const response = await api.get(`/agents/${id}`);
    return response.data;
  },
  
  update: async (id, agentData) => {
    const response = await api.put(`/agents/${id}`, agentData);
    return response.data;
  },
  
  updateStatus: async (id, status, comments = '') => {
    const response = await api.patch(`/agents/${id}/status`, { status, comments });
    return response.data;
  },
  
  delete: async (id) => {
    const response = await api.delete(`/agents/${id}`);
    return response.data;
  },
  
  getStatusHistory: async (id) => {
    const response = await api.get(`/agents/${id}/status-history`);
    return response.data;
  }
};

// Workflow API
export const workflowAPI = {
  getWorkflowSummary: async (agentId) => {
    const response = await api.get(`/workflow/agents/${agentId}/summary`);
    return response.data;
  },

  getNextWorkflowStep: async (agentId) => {
    const response = await api.get(`/workflow/agents/${agentId}/next-step`);
    return response.data;
  },

  getValidNextStatuses: async (agentId) => {
    const response = await api.get(`/workflow/agents/${agentId}/valid-statuses`);
    return response.data;
  },

  validateStatusTransition: async (agentId, newStatus) => {
    const response = await api.post(`/workflow/agents/${agentId}/validate-transition`, {
      newStatus
    });
    return response.data;
  },

  processStatusTransition: async (agentId, newStatus, comments) => {
    const response = await api.post(`/workflow/agents/${agentId}/transition`, {
      newStatus,
      comments
    });
    return response.data;
  },

  autoProgressWorkflow: async (agentId) => {
    const response = await api.post(`/workflow/agents/${agentId}/auto-progress`);
    return response.data;
  },

  isWorkflowComplete: async (agentId) => {
    const response = await api.get(`/workflow/agents/${agentId}/is-complete`);
    return response.data;
  },

  requiresManualReview: async (agentId) => {
    const response = await api.get(`/workflow/agents/${agentId}/requires-review`);
    return response.data;
  },

  getEstimatedCompletionTime: async (agentId) => {
    const response = await api.get(`/workflow/agents/${agentId}/estimated-completion`);
    return response.data;
  }
};

// Document API
export const documentAPI = {
  upload: async (agentId, file, documentType) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('documentType', documentType);

    const response = await api.post(`/documents/agents/${agentId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  getByAgent: async (agentId) => {
    const response = await api.get(`/documents/agents/${agentId}`);
    return response.data;
  },

  getByStatus: async (status) => {
    const response = await api.get(`/documents/status/${status}`);
    return response.data;
  },

  getById: async (documentId) => {
    const response = await api.get(`/documents/${documentId}`);
    return response.data;
  },

  download: async (documentId) => {
    const response = await api.get(`/documents/${documentId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },

  updateStatus: async (documentId, status, comments = '') => {
    const response = await api.patch(`/documents/${documentId}/status`, {
      status,
      comments
    });
    return response.data;
  },

  delete: async (documentId) => {
    const response = await api.delete(`/documents/${documentId}`);
    return response.data;
  },

  bulkUpdateStatus: async (documentIds, status, comments = '') => {
    const response = await api.patch('/documents/bulk-status', {
      documentIds,
      status,
      comments
    });
    return response.data;
  }
};

export default api;
