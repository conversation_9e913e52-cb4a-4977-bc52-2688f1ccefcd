# IDFC Agent Onboarding Database Setup

This directory contains the complete database setup scripts and utilities for the IDFC Agent Onboarding and Management System.

## 📁 Files Overview

- **`IDFCAgentDB_Setup.sql`** - Complete SQL script to create the database schema
- **`Setup-Database.ps1`** - PowerShell script to execute the SQL setup and verify installation
- **`VerifyDatabase.cs`** - C# utility class for database verification within the API
- **`README.md`** - This documentation file

## 🚀 Quick Setup

### Option 1: Using PowerShell Script (Recommended)

1. Open PowerShell as Administrator
2. Navigate to the Database directory:
   ```powershell
   cd "path\to\IDFCAgentOnboardingAndManagementSolution\Database"
   ```
3. Execute the setup script:
   ```powershell
   .\Setup-Database.ps1
   ```

### Option 2: Manual SQL Execution

1. Open SQL Server Management Studio (SSMS) or Azure Data Studio
2. Connect to your LocalDB instance: `(localdb)\mssqllocaldb`
3. Open and execute the `IDFCAgentDB_Setup.sql` script

## 📊 Database Schema

### Core Tables

| Table | Purpose | Key Features |
|-------|---------|--------------|
| **Agents** | Store agent personal and business information | Unique constraints on Email, Aadhar, PAN, Phone |
| **Users** | Authentication and authorization | Role-based access control |
| **AgentDocuments** | Document storage metadata | File tracking with approval workflow |
| **AgentStatusHistories** | Audit trail for status changes | Complete workflow tracking |

### Reference Tables

| Table | Purpose |
|-------|---------|
| **UserRoles** | User role definitions (Agent, Reviewer, Admin, SuperAdmin) |
| **AgentStatuses** | Agent status definitions (Pending, Under Review, Approved, etc.) |
| **DocumentTypes** | Document type definitions (Aadhar, PAN, Photo, etc.) |
| **WorkflowSteps** | Workflow configuration and step definitions |

### Performance Features

- **15 Indexes** for optimized queries on frequently accessed columns
- **Foreign Key Constraints** ensuring data integrity
- **Check Constraints** for data validation (Aadhar format, PAN format, etc.)
- **Unique Constraints** preventing duplicate registrations

## 🔐 Default Users

The setup script creates default users for immediate system access:

| Username | Password | Role | Purpose |
|----------|----------|------|---------|
| `admin` | `Admin@123` | Admin | System administration |
| `reviewer` | `Reviewer@123` | Reviewer | Application review and approval |

> **Security Note**: Change these default passwords in production environments.

## 🛠 Stored Procedures

### `sp_GetAgentDashboardStats`
Returns dashboard statistics including counts of applications by status.

### `sp_GetAgentsWithPagination`
Retrieves agents with pagination, filtering, and sorting capabilities.
```sql
EXEC sp_GetAgentsWithPagination 
    @PageNumber = 1, 
    @PageSize = 10, 
    @StatusFilter = NULL, 
    @SearchTerm = 'john',
    @SortBy = 'CreatedAt',
    @SortOrder = 'DESC'
```

### `sp_UpdateAgentStatus`
Updates agent status with automatic history tracking.
```sql
EXEC sp_UpdateAgentStatus 
    @AgentId = 1, 
    @NewStatus = 2, 
    @ChangedBy = 'admin',
    @Comments = 'Application approved after document verification'
```

### `sp_GetAgentWorkflowSummary`
Provides complete workflow summary for an agent including progress and history.

## 📈 Functions

### `fn_GetAgentProgress(@AgentId)`
Calculates the onboarding progress percentage for an agent based on their current status.

## 📋 Views

### `vw_AgentSummary`
Consolidated view of agent information with status and progress details.

### `vw_DocumentSummary`
Document overview with agent information and approval status.

## 🔧 Configuration

### Connection String
The API uses this connection string (already configured in `appsettings.json`):
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=IDFCAgentDB;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

### Entity Framework Compatibility
The database schema is fully compatible with the existing Entity Framework models and migrations in the API project.

## ✅ Verification

### Using PowerShell Script
The setup script automatically verifies the installation and displays:
- Database creation status
- Table and object counts
- Sample data verification
- Connection testing

### Using C# Verification Class
```csharp
var verifier = new DatabaseVerifier(connectionString);
var result = await verifier.VerifyDatabaseAsync();
result.PrintStatus();
```

### Manual Verification Queries
```sql
-- Check all tables exist
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';

-- Verify seed data
SELECT Username, Role FROM Users;
SELECT StatusName FROM AgentStatuses;

-- Check constraints and indexes
SELECT name FROM sys.indexes WHERE object_id = OBJECT_ID('Agents');
```

## 🚨 Troubleshooting

### LocalDB Not Found
```powershell
# Install SQL Server LocalDB
# Download from: https://docs.microsoft.com/en-us/sql/database-engine/configure-windows/sql-server-express-localdb

# Start LocalDB
sqllocaldb start mssqllocaldb
```

### Permission Issues
- Run PowerShell as Administrator
- Ensure LocalDB service is running
- Check Windows Authentication is enabled

### Connection Issues
- Verify LocalDB instance name: `sqllocaldb info`
- Test connection: `sqlcmd -S (localdb)\mssqllocaldb -Q "SELECT @@VERSION"`

## 🔄 Migration from Entity Framework

If you have existing EF migrations, you can:

1. **Clean Approach**: Drop existing database and use this script
2. **Merge Approach**: Compare schema differences and apply manually
3. **Backup Approach**: Export data, recreate with script, import data

## 📞 Support

For issues with database setup:
1. Check the PowerShell script output for specific error messages
2. Verify LocalDB installation and service status
3. Ensure proper permissions for database creation
4. Review connection string configuration in `appsettings.json`
