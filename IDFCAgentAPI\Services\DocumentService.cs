using IDFCAgentAPI.Data;
using IDFCAgentAPI.Models;
using Microsoft.EntityFrameworkCore;
using System.IO;

namespace IDFCAgentAPI.Services
{
    public class DocumentService : IDocumentService
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<DocumentService> _logger;
        private readonly string _uploadsPath;

        public DocumentService(
            ApplicationDbContext context, 
            IWebHostEnvironment environment,
            ILogger<DocumentService> logger)
        {
            _context = context;
            _environment = environment;
            _logger = logger;
            _uploadsPath = Path.Combine(_environment.ContentRootPath, "uploads", "documents");
            
            // Ensure uploads directory exists
            if (!Directory.Exists(_uploadsPath))
            {
                Directory.CreateDirectory(_uploadsPath);
            }
        }

        public async Task<AgentDocument> UploadDocumentAsync(int agentId, DocumentType documentType, IFormFile file)
        {
            try
            {
                // Validate file
                if (file == null || file.Length == 0)
                    throw new ArgumentException("File is required");

                // Validate file size (max 10MB)
                if (file.Length > 10 * 1024 * 1024)
                    throw new ArgumentException("File size cannot exceed 10MB");

                // Validate file type
                var allowedTypes = new[] { "image/jpeg", "image/png", "image/jpg", "application/pdf" };
                if (!allowedTypes.Contains(file.ContentType.ToLower()))
                    throw new ArgumentException("Only JPEG, PNG, and PDF files are allowed");

                // Check if agent exists
                var agent = await _context.Agents.FindAsync(agentId);
                if (agent == null)
                    throw new ArgumentException("Agent not found");

                // Check if document of this type already exists for the agent
                var existingDocument = await _context.AgentDocuments
                    .FirstOrDefaultAsync(d => d.AgentId == agentId && d.DocumentType == documentType);

                // Create agent-specific directory
                var agentDirectory = Path.Combine(_uploadsPath, agentId.ToString());
                if (!Directory.Exists(agentDirectory))
                {
                    Directory.CreateDirectory(agentDirectory);
                }

                // Generate unique filename
                var fileExtension = Path.GetExtension(file.FileName);
                var fileName = $"{documentType}_{DateTime.UtcNow:yyyyMMddHHmmss}{fileExtension}";
                var filePath = Path.Combine(agentDirectory, fileName);

                // Save file to disk
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // If document exists, update it; otherwise create new
                AgentDocument document;
                if (existingDocument != null)
                {
                    // Delete old file if it exists
                    if (File.Exists(existingDocument.FilePath))
                    {
                        File.Delete(existingDocument.FilePath);
                    }

                    // Update existing document
                    existingDocument.FileName = file.FileName;
                    existingDocument.FilePath = filePath;
                    existingDocument.FileSize = FormatFileSize(file.Length);
                    existingDocument.MimeType = file.ContentType;
                    existingDocument.Status = DocumentStatus.Pending;
                    existingDocument.UploadedAt = DateTime.UtcNow;
                    existingDocument.ReviewComments = null;
                    existingDocument.ReviewedAt = null;
                    existingDocument.ReviewedBy = null;

                    document = existingDocument;
                }
                else
                {
                    // Create new document record
                    document = new AgentDocument
                    {
                        AgentId = agentId,
                        DocumentType = documentType,
                        FileName = file.FileName,
                        FilePath = filePath,
                        FileSize = FormatFileSize(file.Length),
                        MimeType = file.ContentType,
                        Status = DocumentStatus.Pending,
                        UploadedAt = DateTime.UtcNow
                    };

                    _context.AgentDocuments.Add(document);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation($"Document uploaded successfully for Agent {agentId}, Type: {documentType}");
                return document;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error uploading document for Agent {agentId}, Type: {documentType}");
                throw;
            }
        }

        public async Task<AgentDocument?> GetDocumentByIdAsync(int documentId)
        {
            return await _context.AgentDocuments
                .Include(d => d.Agent)
                .FirstOrDefaultAsync(d => d.DocumentId == documentId);
        }

        public async Task<IEnumerable<AgentDocument>> GetDocumentsByAgentIdAsync(int agentId)
        {
            return await _context.AgentDocuments
                .Where(d => d.AgentId == agentId)
                .OrderBy(d => d.DocumentType)
                .ToListAsync();
        }

        public async Task<IEnumerable<AgentDocument>> GetDocumentsByStatusAsync(DocumentStatus status)
        {
            return await _context.AgentDocuments
                .Include(d => d.Agent)
                .Where(d => d.Status == status)
                .OrderByDescending(d => d.UploadedAt)
                .ToListAsync();
        }

        public async Task<bool> UpdateDocumentStatusAsync(int documentId, DocumentStatus status, string reviewedBy, string? comments = null)
        {
            try
            {
                var document = await _context.AgentDocuments.FindAsync(documentId);
                if (document == null)
                    return false;

                document.Status = status;
                document.ReviewComments = comments;
                document.ReviewedAt = DateTime.UtcNow;
                document.ReviewedBy = reviewedBy;

                await _context.SaveChangesAsync();

                _logger.LogInformation($"Document {documentId} status updated to {status} by {reviewedBy}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating document status for Document {documentId}");
                return false;
            }
        }

        public async Task<bool> DeleteDocumentAsync(int documentId)
        {
            try
            {
                var document = await _context.AgentDocuments.FindAsync(documentId);
                if (document == null)
                    return false;

                // Delete file from disk
                if (File.Exists(document.FilePath))
                {
                    File.Delete(document.FilePath);
                }

                _context.AgentDocuments.Remove(document);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Document {documentId} deleted successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting document {documentId}");
                return false;
            }
        }

        public async Task<byte[]?> GetDocumentContentAsync(int documentId)
        {
            try
            {
                var document = await _context.AgentDocuments.FindAsync(documentId);
                if (document == null || !File.Exists(document.FilePath))
                    return null;

                return await File.ReadAllBytesAsync(document.FilePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error reading document content for Document {documentId}");
                return null;
            }
        }

        public async Task<bool> DocumentExistsAsync(int documentId)
        {
            return await _context.AgentDocuments.AnyAsync(d => d.DocumentId == documentId);
        }

        public async Task<string> SaveFileAsync(IFormFile file, string directory)
        {
            var fileName = Guid.NewGuid().ToString() + Path.GetExtension(file.FileName);
            var filePath = Path.Combine(directory, fileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            return filePath;
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting file {filePath}");
                return false;
            }
        }

        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
