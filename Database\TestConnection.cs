using Microsoft.Data.SqlClient;
using System;
using System.Threading.Tasks;

namespace DatabaseTest;

class Program
{
        static async Task Main(string[] args)
        {
            string connectionString = "Server=(localdb)\\mssqllocaldb;Database=IDFCAgentDB;Trusted_Connection=true;MultipleActiveResultSets=true";
            
            Console.WriteLine("Testing IDFC Agent Database Connection...");
            Console.WriteLine("==========================================");
            
            try
            {
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();
                
                Console.WriteLine("✓ Database connection successful!");
                Console.WriteLine($"Database: {connection.Database}");
                Console.WriteLine($"Server: {connection.DataSource}");
                
                // Test basic queries
                Console.WriteLine("\nTesting basic queries...");
                
                // Count tables
                var tableCountQuery = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'";
                using var tableCmd = new SqlCommand(tableCountQuery, connection);
                var tableCount = (int)await tableCmd.ExecuteScalarAsync();
                Console.WriteLine($"✓ Tables found: {tableCount}");
                
                // Count users
                var userCountQuery = "SELECT COUNT(*) FROM Users";
                using var userCmd = new SqlCommand(userCountQuery, connection);
                var userCount = (int)await userCmd.ExecuteScalarAsync();
                Console.WriteLine($"✓ Users found: {userCount}");
                
                // Test admin user
                var adminQuery = "SELECT Username, Role FROM Users WHERE Username = 'admin'";
                using var adminCmd = new SqlCommand(adminQuery, connection);
                using var reader = await adminCmd.ExecuteReaderAsync();
                
                if (await reader.ReadAsync())
                {
                    var username = reader.GetString("Username");
                    var role = reader.GetInt32("Role");
                    Console.WriteLine($"✓ Admin user found: {username} (Role: {role})");
                }
                else
                {
                    Console.WriteLine("✗ Admin user not found");
                }
                
                Console.WriteLine("\n==========================================");
                Console.WriteLine("Database connection test completed successfully!");
                Console.WriteLine("The API should be able to connect to this database.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Database connection failed: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
