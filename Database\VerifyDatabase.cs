using Microsoft.Data.SqlClient;
using System;
using System.Threading.Tasks;

namespace IDFCAgentAPI.Database
{
    /// <summary>
    /// Database verification utility to ensure the database is properly set up
    /// </summary>
    public class DatabaseVerifier
    {
        private readonly string _connectionString;

        public DatabaseVerifier(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// Verifies that the database exists and all required tables are present
        /// </summary>
        public async Task<DatabaseVerificationResult> VerifyDatabaseAsync()
        {
            var result = new DatabaseVerificationResult();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                result.CanConnect = true;
                result.DatabaseName = connection.Database;

                // Check if all required tables exist
                var requiredTables = new[]
                {
                    "Agents", "Users", "AgentDocuments", "AgentStatusHistories",
                    "UserRoles", "AgentStatuses", "DocumentTypes", "WorkflowSteps"
                };

                foreach (var tableName in requiredTables)
                {
                    var tableExists = await CheckTableExistsAsync(connection, tableName);
                    result.TableStatus[tableName] = tableExists;
                    
                    if (!tableExists)
                    {
                        result.MissingTables.Add(tableName);
                    }
                }

                // Check if seed data exists
                result.AdminUserExists = await CheckAdminUserExistsAsync(connection);
                result.WorkflowStepsConfigured = await CheckWorkflowStepsExistAsync(connection);

                // Get record counts
                result.RecordCounts = await GetRecordCountsAsync(connection);

                result.IsValid = result.MissingTables.Count == 0 && result.AdminUserExists;
            }
            catch (Exception ex)
            {
                result.CanConnect = false;
                result.ErrorMessage = ex.Message;
                result.IsValid = false;
            }

            return result;
        }

        private async Task<bool> CheckTableExistsAsync(SqlConnection connection, string tableName)
        {
            var query = @"
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = @TableName AND TABLE_TYPE = 'BASE TABLE'";

            using var command = new SqlCommand(query, connection);
            command.Parameters.AddWithValue("@TableName", tableName);

            var count = (int)await command.ExecuteScalarAsync();
            return count > 0;
        }

        private async Task<bool> CheckAdminUserExistsAsync(SqlConnection connection)
        {
            var query = "SELECT COUNT(*) FROM Users WHERE Username = 'admin' AND Role = 2";
            using var command = new SqlCommand(query, connection);
            var count = (int)await command.ExecuteScalarAsync();
            return count > 0;
        }

        private async Task<bool> CheckWorkflowStepsExistAsync(SqlConnection connection)
        {
            var query = "SELECT COUNT(*) FROM WorkflowSteps";
            using var command = new SqlCommand(query, connection);
            var count = (int)await command.ExecuteScalarAsync();
            return count > 0;
        }

        private async Task<Dictionary<string, int>> GetRecordCountsAsync(SqlConnection connection)
        {
            var counts = new Dictionary<string, int>();
            var tables = new[] { "Users", "Agents", "AgentDocuments", "AgentStatusHistories" };

            foreach (var table in tables)
            {
                var query = $"SELECT COUNT(*) FROM {table}";
                using var command = new SqlCommand(query, connection);
                counts[table] = (int)await command.ExecuteScalarAsync();
            }

            return counts;
        }
    }

    public class DatabaseVerificationResult
    {
        public bool IsValid { get; set; }
        public bool CanConnect { get; set; }
        public string DatabaseName { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
        public Dictionary<string, bool> TableStatus { get; set; } = new();
        public List<string> MissingTables { get; set; } = new();
        public bool AdminUserExists { get; set; }
        public bool WorkflowStepsConfigured { get; set; }
        public Dictionary<string, int> RecordCounts { get; set; } = new();

        public void PrintStatus()
        {
            Console.WriteLine("=== Database Verification Results ===");
            Console.WriteLine($"Can Connect: {CanConnect}");
            Console.WriteLine($"Database: {DatabaseName}");
            Console.WriteLine($"Overall Status: {(IsValid ? "✓ VALID" : "✗ INVALID")}");

            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                Console.WriteLine($"Error: {ErrorMessage}");
                return;
            }

            Console.WriteLine("\nTable Status:");
            foreach (var table in TableStatus)
            {
                Console.WriteLine($"  {table.Key}: {(table.Value ? "✓" : "✗")}");
            }

            if (MissingTables.Any())
            {
                Console.WriteLine($"\nMissing Tables: {string.Join(", ", MissingTables)}");
            }

            Console.WriteLine($"\nSeed Data:");
            Console.WriteLine($"  Admin User: {(AdminUserExists ? "✓" : "✗")}");
            Console.WriteLine($"  Workflow Steps: {(WorkflowStepsConfigured ? "✓" : "✗")}");

            Console.WriteLine($"\nRecord Counts:");
            foreach (var count in RecordCounts)
            {
                Console.WriteLine($"  {count.Key}: {count.Value}");
            }
        }
    }
}
