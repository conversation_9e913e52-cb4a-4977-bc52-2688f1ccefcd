{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\IDFCAgentOnboardingAndManagementSolution\\\\idfc-agent-frontend\\\\src\\\\pages\\\\DocumentManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { documentAPI } from '../services/api';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DocumentManagement = () => {\n  _s();\n  const [documents, setDocuments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedStatus, setSelectedStatus] = useState('Pending');\n  const [selectedDocuments, setSelectedDocuments] = useState([]);\n  const [showStatusModal, setShowStatusModal] = useState(false);\n  const [statusUpdateData, setStatusUpdateData] = useState({\n    status: '',\n    comments: ''\n  });\n  const documentStatuses = [{\n    value: 'Pending',\n    label: 'Pending Review',\n    color: 'bg-yellow-100 text-yellow-800'\n  }, {\n    value: 'UnderReview',\n    label: 'Under Review',\n    color: 'bg-blue-100 text-blue-800'\n  }, {\n    value: 'Approved',\n    label: 'Approved',\n    color: 'bg-green-100 text-green-800'\n  }, {\n    value: 'Rejected',\n    label: 'Rejected',\n    color: 'bg-red-100 text-red-800'\n  }, {\n    value: 'ResubmissionRequired',\n    label: 'Resubmission Required',\n    color: 'bg-orange-100 text-orange-800'\n  }];\n  const documentTypes = {\n    0: 'Aadhar Card',\n    1: 'PAN Card',\n    2: 'Photo',\n    3: 'Bank Passbook',\n    4: 'Address Proof',\n    5: 'Business Registration',\n    6: 'GST Certificate',\n    7: 'Other'\n  };\n  useEffect(() => {\n    fetchDocuments();\n  }, [selectedStatus]);\n  const fetchDocuments = async () => {\n    try {\n      setLoading(true);\n      const data = await documentAPI.getByStatus(selectedStatus);\n      setDocuments(data);\n    } catch (error) {\n      console.error('Error fetching documents:', error);\n      toast.error('Failed to fetch documents');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDownload = async (documentId, fileName) => {\n    try {\n      const blob = await documentAPI.download(documentId);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n      toast.success('Document downloaded successfully');\n    } catch (error) {\n      console.error('Error downloading document:', error);\n      toast.error('Failed to download document');\n    }\n  };\n  const handleStatusUpdate = async (documentId, status, comments = '') => {\n    try {\n      await documentAPI.updateStatus(documentId, status, comments);\n      toast.success('Document status updated successfully');\n      fetchDocuments();\n    } catch (error) {\n      console.error('Error updating document status:', error);\n      toast.error('Failed to update document status');\n    }\n  };\n  const handleBulkStatusUpdate = async () => {\n    if (selectedDocuments.length === 0) {\n      toast.warning('Please select documents to update');\n      return;\n    }\n    try {\n      await documentAPI.bulkUpdateStatus(selectedDocuments, statusUpdateData.status, statusUpdateData.comments);\n      toast.success(`${selectedDocuments.length} documents updated successfully`);\n      setSelectedDocuments([]);\n      setShowStatusModal(false);\n      setStatusUpdateData({\n        status: '',\n        comments: ''\n      });\n      fetchDocuments();\n    } catch (error) {\n      console.error('Error updating documents:', error);\n      toast.error('Failed to update documents');\n    }\n  };\n  const handleSelectDocument = documentId => {\n    setSelectedDocuments(prev => prev.includes(documentId) ? prev.filter(id => id !== documentId) : [...prev, documentId]);\n  };\n  const handleSelectAll = () => {\n    if (selectedDocuments.length === documents.length) {\n      setSelectedDocuments([]);\n    } else {\n      setSelectedDocuments(documents.map(doc => doc.documentId));\n    }\n  };\n  const getStatusColor = status => {\n    const statusObj = documentStatuses.find(s => s.value === status);\n    return statusObj ? statusObj.color : 'bg-gray-100 text-gray-800';\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-IN', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-5 sm:p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Document Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedStatus,\n              onChange: e => setSelectedStatus(e.target.value),\n              className: \"rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\",\n              children: documentStatuses.map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: status.value,\n                children: status.label\n              }, status.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), selectedDocuments.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowStatusModal(true),\n              className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: [\"Update Selected (\", selectedDocuments.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: selectedDocuments.length === documents.length && documents.length > 0,\n                    onChange: handleSelectAll,\n                    className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Agent\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Document Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"File Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Size\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Uploaded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: documents.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: \"8\",\n                  className: \"px-6 py-4 text-center text-gray-500\",\n                  children: [\"No documents found for status: \", selectedStatus]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this) : documents.map(document => {\n                var _documentStatuses$fin;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"hover:bg-gray-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: selectedDocuments.includes(document.documentId),\n                      onChange: () => handleSelectDocument(document.documentId),\n                      className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 223,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: document.agentName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"ID: \", document.agentId]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-900\",\n                      children: documentTypes[document.documentType]\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-900\",\n                      children: document.fileName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-500\",\n                      children: document.fileSize\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(document.status)}`,\n                      children: ((_documentStatuses$fin = documentStatuses.find(s => s.value === document.status)) === null || _documentStatuses$fin === void 0 ? void 0 : _documentStatuses$fin.label) || document.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                    children: formatDate(document.uploadedAt)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleDownload(document.documentId, document.fileName),\n                      className: \"text-blue-600 hover:text-blue-900\",\n                      children: \"Download\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 25\n                    }, this), document.status === 'Pending' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleStatusUpdate(document.documentId, 'Approved'),\n                        className: \"text-green-600 hover:text-green-900\",\n                        children: \"Approve\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 270,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleStatusUpdate(document.documentId, 'Rejected', 'Document rejected by reviewer'),\n                        className: \"text-red-600 hover:text-red-900\",\n                        children: \"Reject\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 23\n                  }, this)]\n                }, document.documentId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), showStatusModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: [\"Update Status for \", selectedDocuments.length, \" Documents\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"New Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: statusUpdateData.status,\n                onChange: e => setStatusUpdateData(prev => ({\n                  ...prev,\n                  status: e.target.value\n                })),\n                className: \"w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this), documentStatuses.map(status => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: status.value,\n                  children: status.label\n                }, status.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Comments (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: statusUpdateData.comments,\n                onChange: e => setStatusUpdateData(prev => ({\n                  ...prev,\n                  comments: e.target.value\n                })),\n                rows: 3,\n                className: \"w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\",\n                placeholder: \"Add comments for the status update...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowStatusModal(false);\n                setStatusUpdateData({\n                  status: '',\n                  comments: ''\n                });\n              },\n              className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleBulkStatusUpdate,\n              disabled: !statusUpdateData.status,\n              className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: \"Update Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(DocumentManagement, \"NYEyWapO8ikUc0mw0KtdJUUHUb8=\");\n_c = DocumentManagement;\nexport default DocumentManagement;\nvar _c;\n$RefreshReg$(_c, \"DocumentManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "documentAPI", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DocumentManagement", "_s", "documents", "setDocuments", "loading", "setLoading", "selectedStatus", "setSelectedStatus", "selectedDocuments", "setSelectedDocuments", "showStatusModal", "setShowStatusModal", "statusUpdateData", "setStatusUpdateData", "status", "comments", "documentStatuses", "value", "label", "color", "documentTypes", "fetchDocuments", "data", "getByStatus", "error", "console", "handleDownload", "documentId", "fileName", "blob", "download", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "success", "handleStatusUpdate", "updateStatus", "handleBulkStatusUpdate", "length", "warning", "bulkUpdateStatus", "handleSelectDocument", "prev", "includes", "filter", "id", "handleSelectAll", "map", "doc", "getStatusColor", "statusObj", "find", "s", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "className", "children", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "e", "target", "onClick", "type", "checked", "colSpan", "_documentStatuses$fin", "<PERSON><PERSON><PERSON>", "agentId", "documentType", "fileSize", "uploadedAt", "rows", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/IDFCAgentOnboardingAndManagementSolution/idfc-agent-frontend/src/pages/DocumentManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { documentAPI } from '../services/api';\nimport { toast } from 'react-toastify';\n\nconst DocumentManagement = () => {\n  const [documents, setDocuments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedStatus, setSelectedStatus] = useState('Pending');\n  const [selectedDocuments, setSelectedDocuments] = useState([]);\n  const [showStatusModal, setShowStatusModal] = useState(false);\n  const [statusUpdateData, setStatusUpdateData] = useState({\n    status: '',\n    comments: ''\n  });\n\n  const documentStatuses = [\n    { value: 'Pending', label: 'Pending Review', color: 'bg-yellow-100 text-yellow-800' },\n    { value: 'UnderReview', label: 'Under Review', color: 'bg-blue-100 text-blue-800' },\n    { value: 'Approved', label: 'Approved', color: 'bg-green-100 text-green-800' },\n    { value: 'Rejected', label: 'Rejected', color: 'bg-red-100 text-red-800' },\n    { value: 'ResubmissionRequired', label: 'Resubmission Required', color: 'bg-orange-100 text-orange-800' }\n  ];\n\n  const documentTypes = {\n    0: 'Aadhar Card',\n    1: 'PAN Card',\n    2: 'Photo',\n    3: 'Bank Passbook',\n    4: 'Address Proof',\n    5: 'Business Registration',\n    6: 'GST Certificate',\n    7: 'Other'\n  };\n\n  useEffect(() => {\n    fetchDocuments();\n  }, [selectedStatus]);\n\n  const fetchDocuments = async () => {\n    try {\n      setLoading(true);\n      const data = await documentAPI.getByStatus(selectedStatus);\n      setDocuments(data);\n    } catch (error) {\n      console.error('Error fetching documents:', error);\n      toast.error('Failed to fetch documents');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDownload = async (documentId, fileName) => {\n    try {\n      const blob = await documentAPI.download(documentId);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n      toast.success('Document downloaded successfully');\n    } catch (error) {\n      console.error('Error downloading document:', error);\n      toast.error('Failed to download document');\n    }\n  };\n\n  const handleStatusUpdate = async (documentId, status, comments = '') => {\n    try {\n      await documentAPI.updateStatus(documentId, status, comments);\n      toast.success('Document status updated successfully');\n      fetchDocuments();\n    } catch (error) {\n      console.error('Error updating document status:', error);\n      toast.error('Failed to update document status');\n    }\n  };\n\n  const handleBulkStatusUpdate = async () => {\n    if (selectedDocuments.length === 0) {\n      toast.warning('Please select documents to update');\n      return;\n    }\n\n    try {\n      await documentAPI.bulkUpdateStatus(\n        selectedDocuments,\n        statusUpdateData.status,\n        statusUpdateData.comments\n      );\n      toast.success(`${selectedDocuments.length} documents updated successfully`);\n      setSelectedDocuments([]);\n      setShowStatusModal(false);\n      setStatusUpdateData({ status: '', comments: '' });\n      fetchDocuments();\n    } catch (error) {\n      console.error('Error updating documents:', error);\n      toast.error('Failed to update documents');\n    }\n  };\n\n  const handleSelectDocument = (documentId) => {\n    setSelectedDocuments(prev => \n      prev.includes(documentId)\n        ? prev.filter(id => id !== documentId)\n        : [...prev, documentId]\n    );\n  };\n\n  const handleSelectAll = () => {\n    if (selectedDocuments.length === documents.length) {\n      setSelectedDocuments([]);\n    } else {\n      setSelectedDocuments(documents.map(doc => doc.documentId));\n    }\n  };\n\n  const getStatusColor = (status) => {\n    const statusObj = documentStatuses.find(s => s.value === status);\n    return statusObj ? statusObj.color : 'bg-gray-100 text-gray-800';\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-IN', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"px-4 py-5 sm:p-6\">\n          <div className=\"flex justify-between items-center mb-6\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">Document Management</h1>\n            \n            <div className=\"flex space-x-4\">\n              {/* Status Filter */}\n              <select\n                value={selectedStatus}\n                onChange={(e) => setSelectedStatus(e.target.value)}\n                className=\"rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"\n              >\n                {documentStatuses.map(status => (\n                  <option key={status.value} value={status.value}>\n                    {status.label}\n                  </option>\n                ))}\n              </select>\n\n              {/* Bulk Actions */}\n              {selectedDocuments.length > 0 && (\n                <button\n                  onClick={() => setShowStatusModal(true)}\n                  className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  Update Selected ({selectedDocuments.length})\n                </button>\n              )}\n            </div>\n          </div>\n\n          {/* Documents Table */}\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    <input\n                      type=\"checkbox\"\n                      checked={selectedDocuments.length === documents.length && documents.length > 0}\n                      onChange={handleSelectAll}\n                      className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                    />\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Agent\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Document Type\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    File Name\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Size\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Uploaded\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {documents.length === 0 ? (\n                  <tr>\n                    <td colSpan=\"8\" className=\"px-6 py-4 text-center text-gray-500\">\n                      No documents found for status: {selectedStatus}\n                    </td>\n                  </tr>\n                ) : (\n                  documents.map((document) => (\n                    <tr key={document.documentId} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedDocuments.includes(document.documentId)}\n                          onChange={() => handleSelectDocument(document.documentId)}\n                          className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                        />\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {document.agentName}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          ID: {document.agentId}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className=\"text-sm text-gray-900\">\n                          {documentTypes[document.documentType]}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className=\"text-sm text-gray-900\">\n                          {document.fileName}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className=\"text-sm text-gray-500\">\n                          {document.fileSize}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(document.status)}`}>\n                          {documentStatuses.find(s => s.value === document.status)?.label || document.status}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {formatDate(document.uploadedAt)}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\">\n                        <button\n                          onClick={() => handleDownload(document.documentId, document.fileName)}\n                          className=\"text-blue-600 hover:text-blue-900\"\n                        >\n                          Download\n                        </button>\n                        {document.status === 'Pending' && (\n                          <>\n                            <button\n                              onClick={() => handleStatusUpdate(document.documentId, 'Approved')}\n                              className=\"text-green-600 hover:text-green-900\"\n                            >\n                              Approve\n                            </button>\n                            <button\n                              onClick={() => handleStatusUpdate(document.documentId, 'Rejected', 'Document rejected by reviewer')}\n                              className=\"text-red-600 hover:text-red-900\"\n                            >\n                              Reject\n                            </button>\n                          </>\n                        )}\n                      </td>\n                    </tr>\n                  ))\n                )}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n\n      {/* Bulk Status Update Modal */}\n      {showStatusModal && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                Update Status for {selectedDocuments.length} Documents\n              </h3>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    New Status\n                  </label>\n                  <select\n                    value={statusUpdateData.status}\n                    onChange={(e) => setStatusUpdateData(prev => ({ ...prev, status: e.target.value }))}\n                    className=\"w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">Select Status</option>\n                    {documentStatuses.map(status => (\n                      <option key={status.value} value={status.value}>\n                        {status.label}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Comments (Optional)\n                  </label>\n                  <textarea\n                    value={statusUpdateData.comments}\n                    onChange={(e) => setStatusUpdateData(prev => ({ ...prev, comments: e.target.value }))}\n                    rows={3}\n                    className=\"w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"\n                    placeholder=\"Add comments for the status update...\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <button\n                  onClick={() => {\n                    setShowStatusModal(false);\n                    setStatusUpdateData({ status: '', comments: '' });\n                  }}\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleBulkStatusUpdate}\n                  disabled={!statusUpdateData.status}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  Update Status\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DocumentManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACgB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC;IACvDsB,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,gBAAgB,GAAG,CACvB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,gBAAgB;IAAEC,KAAK,EAAE;EAAgC,CAAC,EACrF;IAAEF,KAAK,EAAE,aAAa;IAAEC,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAA4B,CAAC,EACnF;IAAEF,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAA8B,CAAC,EAC9E;IAAEF,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAA0B,CAAC,EAC1E;IAAEF,KAAK,EAAE,sBAAsB;IAAEC,KAAK,EAAE,uBAAuB;IAAEC,KAAK,EAAE;EAAgC,CAAC,CAC1G;EAED,MAAMC,aAAa,GAAG;IACpB,CAAC,EAAE,aAAa;IAChB,CAAC,EAAE,UAAU;IACb,CAAC,EAAE,OAAO;IACV,CAAC,EAAE,eAAe;IAClB,CAAC,EAAE,eAAe;IAClB,CAAC,EAAE,uBAAuB;IAC1B,CAAC,EAAE,iBAAiB;IACpB,CAAC,EAAE;EACL,CAAC;EAED3B,SAAS,CAAC,MAAM;IACd4B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACf,cAAc,CAAC,CAAC;EAEpB,MAAMe,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiB,IAAI,GAAG,MAAM5B,WAAW,CAAC6B,WAAW,CAACjB,cAAc,CAAC;MAC1DH,YAAY,CAACmB,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7B,KAAK,CAAC6B,KAAK,CAAC,2BAA2B,CAAC;IAC1C,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,cAAc,GAAG,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,KAAK;IACrD,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMnC,WAAW,CAACoC,QAAQ,CAACH,UAAU,CAAC;MACnD,MAAMI,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACL,QAAQ,GAAGF,QAAQ;MACxBQ,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,IAAI,CAAC;MAC/BA,IAAI,CAACM,KAAK,CAAC,CAAC;MACZL,QAAQ,CAACG,IAAI,CAACG,WAAW,CAACP,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACU,eAAe,CAACZ,GAAG,CAAC;MAC/BpC,KAAK,CAACiD,OAAO,CAAC,kCAAkC,CAAC;IACnD,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD7B,KAAK,CAAC6B,KAAK,CAAC,6BAA6B,CAAC;IAC5C;EACF,CAAC;EAED,MAAMqB,kBAAkB,GAAG,MAAAA,CAAOlB,UAAU,EAAEb,MAAM,EAAEC,QAAQ,GAAG,EAAE,KAAK;IACtE,IAAI;MACF,MAAMrB,WAAW,CAACoD,YAAY,CAACnB,UAAU,EAAEb,MAAM,EAAEC,QAAQ,CAAC;MAC5DpB,KAAK,CAACiD,OAAO,CAAC,sCAAsC,CAAC;MACrDvB,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD7B,KAAK,CAAC6B,KAAK,CAAC,kCAAkC,CAAC;IACjD;EACF,CAAC;EAED,MAAMuB,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAIvC,iBAAiB,CAACwC,MAAM,KAAK,CAAC,EAAE;MAClCrD,KAAK,CAACsD,OAAO,CAAC,mCAAmC,CAAC;MAClD;IACF;IAEA,IAAI;MACF,MAAMvD,WAAW,CAACwD,gBAAgB,CAChC1C,iBAAiB,EACjBI,gBAAgB,CAACE,MAAM,EACvBF,gBAAgB,CAACG,QACnB,CAAC;MACDpB,KAAK,CAACiD,OAAO,CAAC,GAAGpC,iBAAiB,CAACwC,MAAM,iCAAiC,CAAC;MAC3EvC,oBAAoB,CAAC,EAAE,CAAC;MACxBE,kBAAkB,CAAC,KAAK,CAAC;MACzBE,mBAAmB,CAAC;QAAEC,MAAM,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC,CAAC;MACjDM,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7B,KAAK,CAAC6B,KAAK,CAAC,4BAA4B,CAAC;IAC3C;EACF,CAAC;EAED,MAAM2B,oBAAoB,GAAIxB,UAAU,IAAK;IAC3ClB,oBAAoB,CAAC2C,IAAI,IACvBA,IAAI,CAACC,QAAQ,CAAC1B,UAAU,CAAC,GACrByB,IAAI,CAACE,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAK5B,UAAU,CAAC,GACpC,CAAC,GAAGyB,IAAI,EAAEzB,UAAU,CAC1B,CAAC;EACH,CAAC;EAED,MAAM6B,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIhD,iBAAiB,CAACwC,MAAM,KAAK9C,SAAS,CAAC8C,MAAM,EAAE;MACjDvC,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,MAAM;MACLA,oBAAoB,CAACP,SAAS,CAACuD,GAAG,CAACC,GAAG,IAAIA,GAAG,CAAC/B,UAAU,CAAC,CAAC;IAC5D;EACF,CAAC;EAED,MAAMgC,cAAc,GAAI7C,MAAM,IAAK;IACjC,MAAM8C,SAAS,GAAG5C,gBAAgB,CAAC6C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,KAAKH,MAAM,CAAC;IAChE,OAAO8C,SAAS,GAAGA,SAAS,CAACzC,KAAK,GAAG,2BAA2B;EAClE,CAAC;EAED,MAAM4C,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,IAAInE,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK2E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD5E,OAAA;QAAK2E,SAAS,EAAC;MAAgE;QAAA5C,QAAA,EAAA8C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAhD,QAAA,EAAA8C,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACE/E,OAAA;IAAK2E,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5E,OAAA;MAAK2E,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzC5E,OAAA;QAAK2E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B5E,OAAA;UAAK2E,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD5E,OAAA;YAAI2E,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAmB;YAAA7C,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzE/E,OAAA;YAAK2E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAE7B5E,OAAA;cACEoB,KAAK,EAAEX,cAAe;cACtBuE,QAAQ,EAAGC,CAAC,IAAKvE,iBAAiB,CAACuE,CAAC,CAACC,MAAM,CAAC9D,KAAK,CAAE;cACnDuD,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAEzFzD,gBAAgB,CAACyC,GAAG,CAAC3C,MAAM,iBAC1BjB,OAAA;gBAA2BoB,KAAK,EAAEH,MAAM,CAACG,KAAM;gBAAAwD,QAAA,EAC5C3D,MAAM,CAACI;cAAK,GADFJ,MAAM,CAACG,KAAK;gBAAAW,QAAA,EAAA8C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACT;YAAC;cAAAhD,QAAA,EAAA8C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAGRpE,iBAAiB,CAACwC,MAAM,GAAG,CAAC,iBAC3BnD,OAAA;cACEmF,OAAO,EAAEA,CAAA,KAAMrE,kBAAkB,CAAC,IAAI,CAAE;cACxC6D,SAAS,EAAC,mHAAmH;cAAAC,QAAA,GAC9H,mBACkB,EAACjE,iBAAiB,CAACwC,MAAM,EAAC,GAC7C;YAAA;cAAApB,QAAA,EAAA8C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAhD,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAhD,QAAA,EAAA8C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/E,OAAA;UAAK2E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9B5E,OAAA;YAAO2E,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBACpD5E,OAAA;cAAO2E,SAAS,EAAC,YAAY;cAAAC,QAAA,eAC3B5E,OAAA;gBAAA4E,QAAA,gBACE5E,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,eAC5F5E,OAAA;oBACEoF,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAE1E,iBAAiB,CAACwC,MAAM,KAAK9C,SAAS,CAAC8C,MAAM,IAAI9C,SAAS,CAAC8C,MAAM,GAAG,CAAE;oBAC/E6B,QAAQ,EAAErB,eAAgB;oBAC1BgB,SAAS,EAAC;kBAA2D;oBAAA5C,QAAA,EAAA8C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE;gBAAC;kBAAAhD,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACL/E,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAA7C,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/E,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAA7C,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/E,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAA7C,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/E,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAA7C,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/E,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAA7C,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/E,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAA7C,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/E,OAAA;kBAAI2E,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAAC;gBAE/F;kBAAA7C,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAhD,QAAA,EAAA8C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAhD,QAAA,EAAA8C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR/E,OAAA;cAAO2E,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EACjDvE,SAAS,CAAC8C,MAAM,KAAK,CAAC,gBACrBnD,OAAA;gBAAA4E,QAAA,eACE5E,OAAA;kBAAIsF,OAAO,EAAC,GAAG;kBAACX,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,GAAC,iCAC/B,EAACnE,cAAc;gBAAA;kBAAAsB,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C;cAAC;gBAAAhD,QAAA,EAAA8C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,GAEL1E,SAAS,CAACuD,GAAG,CAAErB,QAAQ;gBAAA,IAAAgD,qBAAA;gBAAA,oBACrBvF,OAAA;kBAA8B2E,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBACxD5E,OAAA;oBAAI2E,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,eACzC5E,OAAA;sBACEoF,IAAI,EAAC,UAAU;sBACfC,OAAO,EAAE1E,iBAAiB,CAAC6C,QAAQ,CAACjB,QAAQ,CAACT,UAAU,CAAE;sBACzDkD,QAAQ,EAAEA,CAAA,KAAM1B,oBAAoB,CAACf,QAAQ,CAACT,UAAU,CAAE;sBAC1D6C,SAAS,EAAC;oBAA2D;sBAAA5C,QAAA,EAAA8C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE;kBAAC;oBAAAhD,QAAA,EAAA8C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACL/E,OAAA;oBAAI2E,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBACzC5E,OAAA;sBAAK2E,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC/CrC,QAAQ,CAACiD;oBAAS;sBAAAzD,QAAA,EAAA8C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC,eACN/E,OAAA;sBAAK2E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,MACjC,EAACrC,QAAQ,CAACkD,OAAO;oBAAA;sBAAA1D,QAAA,EAAA8C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC;kBAAA;oBAAAhD,QAAA,EAAA8C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACL/E,OAAA;oBAAI2E,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,eACzC5E,OAAA;sBAAM2E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpCrD,aAAa,CAACgB,QAAQ,CAACmD,YAAY;oBAAC;sBAAA3D,QAAA,EAAA8C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC;kBAAC;oBAAAhD,QAAA,EAAA8C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL/E,OAAA;oBAAI2E,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,eACzC5E,OAAA;sBAAM2E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpCrC,QAAQ,CAACR;oBAAQ;sBAAAA,QAAA,EAAA8C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd;kBAAC;oBAAAhD,QAAA,EAAA8C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL/E,OAAA;oBAAI2E,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,eACzC5E,OAAA;sBAAM2E,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpCrC,QAAQ,CAACoD;oBAAQ;sBAAA5D,QAAA,EAAA8C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd;kBAAC;oBAAAhD,QAAA,EAAA8C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL/E,OAAA;oBAAI2E,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,eACzC5E,OAAA;sBAAM2E,SAAS,EAAE,4DAA4Db,cAAc,CAACvB,QAAQ,CAACtB,MAAM,CAAC,EAAG;sBAAA2D,QAAA,EAC5G,EAAAW,qBAAA,GAAApE,gBAAgB,CAAC6C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,KAAK,KAAKmB,QAAQ,CAACtB,MAAM,CAAC,cAAAsE,qBAAA,uBAAvDA,qBAAA,CAAyDlE,KAAK,KAAIkB,QAAQ,CAACtB;oBAAM;sBAAAc,QAAA,EAAA8C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9E;kBAAC;oBAAAhD,QAAA,EAAA8C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL/E,OAAA;oBAAI2E,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,EAC9DV,UAAU,CAAC3B,QAAQ,CAACqD,UAAU;kBAAC;oBAAA7D,QAAA,EAAA8C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACL/E,OAAA;oBAAI2E,SAAS,EAAC,2DAA2D;oBAAAC,QAAA,gBACvE5E,OAAA;sBACEmF,OAAO,EAAEA,CAAA,KAAMtD,cAAc,CAACU,QAAQ,CAACT,UAAU,EAAES,QAAQ,CAACR,QAAQ,CAAE;sBACtE4C,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC9C;oBAED;sBAAA7C,QAAA,EAAA8C,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRxC,QAAQ,CAACtB,MAAM,KAAK,SAAS,iBAC5BjB,OAAA,CAAAE,SAAA;sBAAA0E,QAAA,gBACE5E,OAAA;wBACEmF,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAACT,QAAQ,CAACT,UAAU,EAAE,UAAU,CAAE;wBACnE6C,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAChD;sBAED;wBAAA7C,QAAA,EAAA8C,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACT/E,OAAA;wBACEmF,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAACT,QAAQ,CAACT,UAAU,EAAE,UAAU,EAAE,+BAA+B,CAAE;wBACpG6C,SAAS,EAAC,iCAAiC;wBAAAC,QAAA,EAC5C;sBAED;wBAAA7C,QAAA,EAAA8C,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CACH;kBAAA;oBAAAhD,QAAA,EAAA8C,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GA/DExC,QAAQ,CAACT,UAAU;kBAAAC,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgExB,CAAC;cAAA,CACN;YACF;cAAAhD,QAAA,EAAA8C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAhD,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAhD,QAAA,EAAA8C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAhD,QAAA,EAAA8C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAhD,QAAA,EAAA8C,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlE,eAAe,iBACdb,OAAA;MAAK2E,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzF5E,OAAA;QAAK2E,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpF5E,OAAA;UAAK2E,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB5E,OAAA;YAAI2E,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GAAC,oBACnC,EAACjE,iBAAiB,CAACwC,MAAM,EAAC,YAC9C;UAAA;YAAApB,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL/E,OAAA;YAAK2E,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB5E,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAO2E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAA7C,QAAA,EAAA8C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACEoB,KAAK,EAAEL,gBAAgB,CAACE,MAAO;gBAC/B+D,QAAQ,EAAGC,CAAC,IAAKjE,mBAAmB,CAACuC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEtC,MAAM,EAAEgE,CAAC,CAACC,MAAM,CAAC9D;gBAAM,CAAC,CAAC,CAAE;gBACpFuD,SAAS,EAAC,uFAAuF;gBAAAC,QAAA,gBAEjG5E,OAAA;kBAAQoB,KAAK,EAAC,EAAE;kBAAAwD,QAAA,EAAC;gBAAa;kBAAA7C,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACtC5D,gBAAgB,CAACyC,GAAG,CAAC3C,MAAM,iBAC1BjB,OAAA;kBAA2BoB,KAAK,EAAEH,MAAM,CAACG,KAAM;kBAAAwD,QAAA,EAC5C3D,MAAM,CAACI;gBAAK,GADFJ,MAAM,CAACG,KAAK;kBAAAW,QAAA,EAAA8C,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACT,CAAC;cAAA;gBAAAhD,QAAA,EAAA8C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAhD,QAAA,EAAA8C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN/E,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAO2E,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAA7C,QAAA,EAAA8C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR/E,OAAA;gBACEoB,KAAK,EAAEL,gBAAgB,CAACG,QAAS;gBACjC8D,QAAQ,EAAGC,CAAC,IAAKjE,mBAAmB,CAACuC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAErC,QAAQ,EAAE+D,CAAC,CAACC,MAAM,CAAC9D;gBAAM,CAAC,CAAC,CAAE;gBACtFyE,IAAI,EAAE,CAAE;gBACRlB,SAAS,EAAC,uFAAuF;gBACjGmB,WAAW,EAAC;cAAuC;gBAAA/D,QAAA,EAAA8C,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAhD,QAAA,EAAA8C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAhD,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/E,OAAA;YAAK2E,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9C5E,OAAA;cACEmF,OAAO,EAAEA,CAAA,KAAM;gBACbrE,kBAAkB,CAAC,KAAK,CAAC;gBACzBE,mBAAmB,CAAC;kBAAEC,MAAM,EAAE,EAAE;kBAAEC,QAAQ,EAAE;gBAAG,CAAC,CAAC;cACnD,CAAE;cACFyD,SAAS,EAAC,sFAAsF;cAAAC,QAAA,EACjG;YAED;cAAA7C,QAAA,EAAA8C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/E,OAAA;cACEmF,OAAO,EAAEjC,sBAAuB;cAChC6C,QAAQ,EAAE,CAAChF,gBAAgB,CAACE,MAAO;cACnC0D,SAAS,EAAC,mIAAmI;cAAAC,QAAA,EAC9I;YAED;cAAA7C,QAAA,EAAA8C,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAhD,QAAA,EAAA8C,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAhD,QAAA,EAAA8C,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAhD,QAAA,EAAA8C,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAhD,QAAA,EAAA8C,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAhD,QAAA,EAAA8C,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAnWID,kBAAkB;AAAA6F,EAAA,GAAlB7F,kBAAkB;AAqWxB,eAAeA,kBAAkB;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}