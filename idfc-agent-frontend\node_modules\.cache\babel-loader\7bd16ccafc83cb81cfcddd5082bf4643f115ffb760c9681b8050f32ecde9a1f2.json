{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\IDFCAgentOnboardingAndManagementSolution\\\\idfc-agent-frontend\\\\src\\\\pages\\\\AgentRegistration.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { agentAPI } from '../services/api';\nimport { toast } from 'react-toastify';\nimport { User, Mail, Phone, MapPin, Calendar, CreditCard, Upload, FileText, Building, DollarSign, Lock } from 'lucide-react';\n\n// Validation schema\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst schema = yup.object({\n  // Personal Information\n  firstName: yup.string().required('First name is required').min(2, 'Minimum 2 characters'),\n  lastName: yup.string().required('Last name is required').min(2, 'Minimum 2 characters'),\n  email: yup.string().email('Invalid email format').required('Email is required'),\n  phoneNumber: yup.string().matches(/^[0-9]{10}$/, 'Phone number must be 10 digits').required('Phone number is required'),\n  aadharNumber: yup.string().matches(/^[0-9]{12}$/, 'Aadhar number must be 12 digits').required('Aadhar number is required'),\n  panNumber: yup.string().matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Invalid PAN format').required('PAN number is required'),\n  dateOfBirth: yup.date().max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Must be at least 18 years old').required('Date of birth is required'),\n  // Address Information\n  address: yup.string().required('Address is required').min(10, 'Address too short'),\n  city: yup.string().required('City is required'),\n  state: yup.string().required('State is required'),\n  pinCode: yup.string().matches(/^[0-9]{6}$/, 'PIN code must be 6 digits').required('PIN code is required'),\n  // Account Information\n  username: yup.string().min(3, 'Username must be at least 3 characters').max(50, 'Username must be less than 50 characters').matches(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores').required('Username is required'),\n  password: yup.string().min(6, 'Password must be at least 6 characters').matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/, 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character').required('Password is required'),\n  confirmPassword: yup.string().oneOf([yup.ref('password')], 'Passwords must match').required('Please confirm your password')\n});\nconst AgentRegistration = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [currentStep, setCurrentStep] = useState(1);\n  const [documents, setDocuments] = useState({\n    aadharCard: null,\n    panCard: null,\n    photo: null,\n    bankPassbook: null,\n    businessProof: null\n  });\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    },\n    trigger,\n    getValues\n  } = useForm({\n    resolver: yupResolver(schema),\n    mode: 'onChange'\n  });\n  const steps = [{\n    id: 1,\n    name: 'Personal Information',\n    icon: User\n  }, {\n    id: 2,\n    name: 'Address Details',\n    icon: MapPin\n  }, {\n    id: 3,\n    name: 'Document Upload',\n    icon: Upload\n  }, {\n    id: 4,\n    name: 'Review & Submit',\n    icon: FileText\n  }];\n  const handleNext = async () => {\n    let fieldsToValidate = [];\n    switch (currentStep) {\n      case 1:\n        fieldsToValidate = ['firstName', 'lastName', 'email', 'phoneNumber', 'dateOfBirth', 'username', 'password', 'confirmPassword'];\n        break;\n      case 2:\n        fieldsToValidate = ['aadharNumber', 'panNumber', 'address', 'city', 'state', 'pinCode'];\n        break;\n      case 3:\n        // Validate documents\n        const requiredDocs = ['aadharCard', 'panCard', 'photo'];\n        const missingDocs = requiredDocs.filter(doc => !documents[doc]);\n        if (missingDocs.length > 0) {\n          toast.error(`Please upload: ${missingDocs.join(', ')}`);\n          return;\n        }\n        break;\n    }\n    if (fieldsToValidate.length > 0) {\n      const isValid = await trigger(fieldsToValidate);\n      if (!isValid) return;\n    }\n    setCurrentStep(prev => Math.min(prev + 1, 4));\n  };\n  const handlePrevious = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n  const handleFileUpload = (documentType, file) => {\n    if (!file) return;\n\n    // Validate file type\n    const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];\n    if (!allowedTypes.includes(file.type)) {\n      toast.error('Only JPEG, PNG, and PDF files are allowed');\n      return;\n    }\n\n    // Validate file size (5MB max)\n    if (file.size > 5 * 1024 * 1024) {\n      toast.error('File size must be less than 5MB');\n      return;\n    }\n    setDocuments(prev => ({\n      ...prev,\n      [documentType]: file\n    }));\n    toast.success(`${documentType} uploaded successfully`);\n  };\n  const onSubmit = async data => {\n    setIsSubmitting(true);\n    try {\n      // Format the data according to the API requirements\n      const {\n        confirmPassword,\n        ...formData\n      } = data;\n      const formattedData = {\n        ...formData,\n        dateOfBirth: new Date(formData.dateOfBirth).toISOString()\n      };\n      const response = await agentAPI.register(formattedData);\n      if (response.success) {\n        toast.success('Agent registration submitted successfully!');\n        // Navigate based on whether this is public registration or admin registration\n        if (location.pathname === '/register') {\n          // Public registration - redirect to login\n          navigate('/login', {\n            state: {\n              message: 'Registration successful! Please login with your credentials.'\n            }\n          });\n        } else {\n          // Admin registration - stay in admin area\n          navigate('/agents');\n        }\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Registration error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        response: error.response,\n        request: error.request,\n        config: error.config\n      });\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Registration failed. Please try again.';\n      toast.error(message);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: \"Agent Registration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-600\",\n        children: \"Complete your agent registration to become an IDFC FASTag agent.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        \"aria-label\": \"Progress\",\n        children: /*#__PURE__*/_jsxDEV(\"ol\", {\n          className: \"flex items-center\",\n          children: steps.map((step, stepIdx) => {\n            const Icon = step.icon;\n            const isCompleted = currentStep > step.id;\n            const isCurrent = currentStep === step.id;\n            return /*#__PURE__*/_jsxDEV(\"li\", {\n              className: `${stepIdx !== steps.length - 1 ? 'flex-1' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `flex items-center ${stepIdx !== steps.length - 1 ? 'w-full' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `\n                        flex items-center justify-center w-10 h-10 rounded-full border-2\n                        ${isCompleted ? 'bg-blue-600 border-blue-600 text-white' : isCurrent ? 'border-blue-600 text-blue-600' : 'border-gray-300 text-gray-500'}\n                      `,\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `ml-3 text-sm font-medium ${isCurrent ? 'text-blue-600' : isCompleted ? 'text-gray-900' : 'text-gray-500'}`,\n                    children: step.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this), stepIdx !== steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex-1 ml-4 h-0.5 ${isCompleted ? 'bg-blue-600' : 'bg-gray-300'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this)\n            }, step.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit(onSubmit),\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: [currentStep === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), \"Personal Information\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"First Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('firstName'),\n              type: \"text\",\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              placeholder: \"Enter your first name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this), errors.firstName && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.firstName.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Last Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('lastName'),\n              type: \"text\",\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              placeholder: \"Enter your last name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this), errors.lastName && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.lastName.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Email Address *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('email'),\n                type: \"email\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"Enter your email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Mail, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.email.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Phone Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('phoneNumber'),\n                type: \"tel\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"10-digit mobile number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Phone, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), errors.phoneNumber && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.phoneNumber.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Date of Birth *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('dateOfBirth'),\n                type: \"date\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 17\n            }, this), errors.dateOfBirth && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.dateOfBirth.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-md font-medium text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this), \"Account Information\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Username *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('username'),\n                type: \"text\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"Choose a unique username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(User, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this), errors.username && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.username.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Password *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('password'),\n                type: \"password\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"Create a strong password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Lock, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), errors.password && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.password.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sm:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Confirm Password *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('confirmPassword'),\n                type: \"password\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"Confirm your password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Lock, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this), errors.confirmPassword && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.confirmPassword.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this), currentStep === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(MapPin, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), \"Address & Identity Details\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Aadhar Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('aadharNumber'),\n                type: \"text\",\n                maxLength: \"12\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"12-digit Aadhar number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(CreditCard, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this), errors.aadharNumber && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.aadharNumber.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"PAN Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('panNumber'),\n                type: \"text\",\n                maxLength: \"10\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"**********\",\n                style: {\n                  textTransform: 'uppercase'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(CreditCard, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this), errors.panNumber && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.panNumber.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sm:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Address *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              ...register('address'),\n              rows: 3,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              placeholder: \"Enter your complete address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this), errors.address && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.address.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"City *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('city'),\n              type: \"text\",\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              placeholder: \"Enter your city\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this), errors.city && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.city.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"State *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              ...register('state'),\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select State\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Andhra Pradesh\",\n                children: \"Andhra Pradesh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Arunachal Pradesh\",\n                children: \"Arunachal Pradesh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Assam\",\n                children: \"Assam\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Bihar\",\n                children: \"Bihar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Chhattisgarh\",\n                children: \"Chhattisgarh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Goa\",\n                children: \"Goa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Gujarat\",\n                children: \"Gujarat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Haryana\",\n                children: \"Haryana\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Himachal Pradesh\",\n                children: \"Himachal Pradesh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Jharkhand\",\n                children: \"Jharkhand\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Karnataka\",\n                children: \"Karnataka\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Kerala\",\n                children: \"Kerala\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Madhya Pradesh\",\n                children: \"Madhya Pradesh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Maharashtra\",\n                children: \"Maharashtra\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Manipur\",\n                children: \"Manipur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Meghalaya\",\n                children: \"Meghalaya\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Mizoram\",\n                children: \"Mizoram\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Nagaland\",\n                children: \"Nagaland\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Odisha\",\n                children: \"Odisha\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Punjab\",\n                children: \"Punjab\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Rajasthan\",\n                children: \"Rajasthan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Sikkim\",\n                children: \"Sikkim\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Tamil Nadu\",\n                children: \"Tamil Nadu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Telangana\",\n                children: \"Telangana\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Tripura\",\n                children: \"Tripura\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Uttar Pradesh\",\n                children: \"Uttar Pradesh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Uttarakhand\",\n                children: \"Uttarakhand\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"West Bengal\",\n                children: \"West Bengal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Delhi\",\n                children: \"Delhi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this), errors.state && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.state.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"PIN Code *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('pinCode'),\n              type: \"text\",\n              maxLength: \"6\",\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              placeholder: \"6-digit PIN code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 17\n            }, this), errors.pinCode && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.pinCode.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 11\n      }, this), currentStep === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this), \"Document Upload\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Please upload clear, readable copies of the following documents. Accepted formats: JPEG, PNG, PDF (Max 5MB each)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [{\n            key: 'aadharCard',\n            label: 'Aadhar Card',\n            required: true\n          }, {\n            key: 'panCard',\n            label: 'PAN Card',\n            required: true\n          }, {\n            key: 'photo',\n            label: 'Passport Size Photo',\n            required: true\n          }, {\n            key: 'bankPassbook',\n            label: 'Bank Passbook/Statement',\n            required: false\n          }, {\n            key: 'businessProof',\n            label: 'Business Proof (if applicable)',\n            required: false\n          }].map(doc => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(Upload, {\n                className: \"mx-auto h-12 w-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mt-2 block text-sm font-medium text-gray-900\",\n                    children: [doc.label, \" \", doc.required && '*']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    className: \"hidden\",\n                    accept: \".jpg,.jpeg,.png,.pdf\",\n                    onChange: e => handleFileUpload(doc.key, e.target.files[0])\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mt-2 block text-xs text-gray-500\",\n                    children: \"Click to upload or drag and drop\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 21\n              }, this), documents[doc.key] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 text-sm text-green-600 flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(FileText, {\n                  className: \"w-4 h-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 25\n                }, this), documents[doc.key].name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 19\n            }, this)\n          }, doc.key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 11\n      }, this), currentStep === 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this), \"Review & Submit\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Please review all the information before submitting your application.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 rounded-lg p-6 space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Personal Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: [getValues('firstName'), \" \", getValues('lastName')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: getValues('email')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Phone:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: getValues('phoneNumber')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Date of Birth:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: getValues('dateOfBirth')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Identity & Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Aadhar:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: getValues('aadharNumber')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"PAN:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: getValues('panNumber')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Address:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: [getValues('address'), \", \", getValues('city'), \", \", getValues('state'), \" - \", getValues('pinCode')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Account Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Username:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: getValues('username')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Password:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Uploaded Documents\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-2 text-sm\",\n              children: Object.entries(documents).map(([key, file]) => file && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-green-600\",\n                children: [/*#__PURE__*/_jsxDEV(FileText, {\n                  className: \"w-4 h-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 25\n                }, this), key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(FileText, {\n                className: \"h-5 w-5 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-medium text-blue-800\",\n                children: \"Important Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 text-sm text-blue-700\",\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-disc list-inside space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Your application will be reviewed within 3-5 business days\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"You will receive email notifications about status updates\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Ensure all uploaded documents are clear and readable\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Any false information may lead to application rejection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between pt-6 border-t border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handlePrevious,\n          disabled: currentStep === 1,\n          className: \"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 11\n        }, this), currentStep < 4 ? /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleNext,\n          className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: isSubmitting,\n          className: \"inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 19\n            }, this), \"Submitting...\"]\n          }, void 0, true) : 'Submit Application'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 733,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentRegistration, \"tPXQc+9Fe3n9ivanwpzZLb8Eq9M=\", false, function () {\n  return [useNavigate, useLocation, useForm];\n});\n_c = AgentRegistration;\nexport default AgentRegistration;\nvar _c;\n$RefreshReg$(_c, \"AgentRegistration\");", "map": {"version": 3, "names": ["React", "useState", "useForm", "yupResolver", "yup", "useNavigate", "useLocation", "agentAPI", "toast", "User", "Mail", "Phone", "MapPin", "Calendar", "CreditCard", "Upload", "FileText", "Building", "DollarSign", "Lock", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "schema", "object", "firstName", "string", "required", "min", "lastName", "email", "phoneNumber", "matches", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "panNumber", "dateOfBirth", "date", "max", "Date", "now", "address", "city", "state", "pinCode", "username", "password", "confirmPassword", "oneOf", "ref", "AgentRegistration", "_s", "navigate", "location", "isSubmitting", "setIsSubmitting", "currentStep", "setCurrentStep", "documents", "setDocuments", "aadharCard", "panCard", "photo", "bankPassbook", "businessProof", "register", "handleSubmit", "formState", "errors", "trigger", "getV<PERSON>ues", "resolver", "mode", "steps", "id", "name", "icon", "handleNext", "fieldsToValidate", "requiredDocs", "missingDocs", "filter", "doc", "length", "error", "join", "<PERSON><PERSON><PERSON><PERSON>", "prev", "Math", "handlePrevious", "handleFileUpload", "documentType", "file", "allowedTypes", "includes", "type", "size", "success", "onSubmit", "data", "formData", "formattedData", "toISOString", "response", "pathname", "message", "_error$response", "_error$response$data", "console", "request", "config", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "step", "stepIdx", "Icon", "isCompleted", "isCurrent", "placeholder", "max<PERSON><PERSON><PERSON>", "style", "textTransform", "rows", "value", "key", "label", "accept", "onChange", "e", "target", "files", "Object", "entries", "replace", "str", "toUpperCase", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/IDFCAgentOnboardingAndManagementSolution/idfc-agent-frontend/src/pages/AgentRegistration.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { agentAPI } from '../services/api';\nimport { toast } from 'react-toastify';\nimport {\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  Calendar,\n  CreditCard,\n  Upload,\n  FileText,\n  Building,\n  DollarSign,\n  Lock\n} from 'lucide-react';\n\n// Validation schema\nconst schema = yup.object({\n  // Personal Information\n  firstName: yup.string().required('First name is required').min(2, 'Minimum 2 characters'),\n  lastName: yup.string().required('Last name is required').min(2, 'Minimum 2 characters'),\n  email: yup.string().email('Invalid email format').required('Email is required'),\n  phoneNumber: yup.string()\n    .matches(/^[0-9]{10}$/, 'Phone number must be 10 digits')\n    .required('Phone number is required'),\n  aadharNumber: yup.string()\n    .matches(/^[0-9]{12}$/, 'Aadhar number must be 12 digits')\n    .required('Aadhar number is required'),\n  panNumber: yup.string()\n    .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Invalid PAN format')\n    .required('PAN number is required'),\n  dateOfBirth: yup.date()\n    .max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Must be at least 18 years old')\n    .required('Date of birth is required'),\n\n  // Address Information\n  address: yup.string().required('Address is required').min(10, 'Address too short'),\n  city: yup.string().required('City is required'),\n  state: yup.string().required('State is required'),\n  pinCode: yup.string()\n    .matches(/^[0-9]{6}$/, 'PIN code must be 6 digits')\n    .required('PIN code is required'),\n\n  // Account Information\n  username: yup.string()\n    .min(3, 'Username must be at least 3 characters')\n    .max(50, 'Username must be less than 50 characters')\n    .matches(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores')\n    .required('Username is required'),\n  password: yup.string()\n    .min(6, 'Password must be at least 6 characters')\n    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/,\n      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')\n    .required('Password is required'),\n  confirmPassword: yup.string()\n    .oneOf([yup.ref('password')], 'Passwords must match')\n    .required('Please confirm your password'),\n});\n\nconst AgentRegistration = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [currentStep, setCurrentStep] = useState(1);\n  const [documents, setDocuments] = useState({\n    aadharCard: null,\n    panCard: null,\n    photo: null,\n    bankPassbook: null,\n    businessProof: null\n  });\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    trigger,\n    getValues\n  } = useForm({\n    resolver: yupResolver(schema),\n    mode: 'onChange'\n  });\n\n  const steps = [\n    { id: 1, name: 'Personal Information', icon: User },\n    { id: 2, name: 'Address Details', icon: MapPin },\n    { id: 3, name: 'Document Upload', icon: Upload },\n    { id: 4, name: 'Review & Submit', icon: FileText }\n  ];\n\n  const handleNext = async () => {\n    let fieldsToValidate = [];\n\n    switch (currentStep) {\n      case 1:\n        fieldsToValidate = ['firstName', 'lastName', 'email', 'phoneNumber', 'dateOfBirth', 'username', 'password', 'confirmPassword'];\n        break;\n      case 2:\n        fieldsToValidate = ['aadharNumber', 'panNumber', 'address', 'city', 'state', 'pinCode'];\n        break;\n      case 3:\n        // Validate documents\n        const requiredDocs = ['aadharCard', 'panCard', 'photo'];\n        const missingDocs = requiredDocs.filter(doc => !documents[doc]);\n        if (missingDocs.length > 0) {\n          toast.error(`Please upload: ${missingDocs.join(', ')}`);\n          return;\n        }\n        break;\n    }\n\n    if (fieldsToValidate.length > 0) {\n      const isValid = await trigger(fieldsToValidate);\n      if (!isValid) return;\n    }\n\n    setCurrentStep(prev => Math.min(prev + 1, 4));\n  };\n\n  const handlePrevious = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n\n  const handleFileUpload = (documentType, file) => {\n    if (!file) return;\n\n    // Validate file type\n    const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];\n    if (!allowedTypes.includes(file.type)) {\n      toast.error('Only JPEG, PNG, and PDF files are allowed');\n      return;\n    }\n\n    // Validate file size (5MB max)\n    if (file.size > 5 * 1024 * 1024) {\n      toast.error('File size must be less than 5MB');\n      return;\n    }\n\n    setDocuments(prev => ({\n      ...prev,\n      [documentType]: file\n    }));\n\n    toast.success(`${documentType} uploaded successfully`);\n  };\n\n  const onSubmit = async (data) => {\n    setIsSubmitting(true);\n\n    try {\n      // Format the data according to the API requirements\n      const { confirmPassword, ...formData } = data;\n      const formattedData = {\n        ...formData,\n        dateOfBirth: new Date(formData.dateOfBirth).toISOString(),\n      };\n\n      const response = await agentAPI.register(formattedData);\n\n      if (response.success) {\n        toast.success('Agent registration submitted successfully!');\n        // Navigate based on whether this is public registration or admin registration\n        if (location.pathname === '/register') {\n          // Public registration - redirect to login\n          navigate('/login', {\n            state: { message: 'Registration successful! Please login with your credentials.' }\n          });\n        } else {\n          // Admin registration - stay in admin area\n          navigate('/agents');\n        }\n      }\n    } catch (error) {\n      console.error('Registration error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        response: error.response,\n        request: error.request,\n        config: error.config\n      });\n      const message = error.response?.data?.message || 'Registration failed. Please try again.';\n      toast.error(message);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">Agent Registration</h1>\n        <p className=\"mt-1 text-sm text-gray-600\">\n          Complete your agent registration to become an IDFC FASTag agent.\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <nav aria-label=\"Progress\">\n          <ol className=\"flex items-center\">\n            {steps.map((step, stepIdx) => {\n              const Icon = step.icon;\n              const isCompleted = currentStep > step.id;\n              const isCurrent = currentStep === step.id;\n\n              return (\n                <li key={step.name} className={`${stepIdx !== steps.length - 1 ? 'flex-1' : ''}`}>\n                  <div className={`flex items-center ${stepIdx !== steps.length - 1 ? 'w-full' : ''}`}>\n                    <div className=\"flex items-center\">\n                      <div className={`\n                        flex items-center justify-center w-10 h-10 rounded-full border-2\n                        ${isCompleted\n                          ? 'bg-blue-600 border-blue-600 text-white'\n                          : isCurrent\n                            ? 'border-blue-600 text-blue-600'\n                            : 'border-gray-300 text-gray-500'\n                        }\n                      `}>\n                        <Icon className=\"w-5 h-5\" />\n                      </div>\n                      <span className={`ml-3 text-sm font-medium ${\n                        isCurrent ? 'text-blue-600' : isCompleted ? 'text-gray-900' : 'text-gray-500'\n                      }`}>\n                        {step.name}\n                      </span>\n                    </div>\n                    {stepIdx !== steps.length - 1 && (\n                      <div className={`flex-1 ml-4 h-0.5 ${\n                        isCompleted ? 'bg-blue-600' : 'bg-gray-300'\n                      }`} />\n                    )}\n                  </div>\n                </li>\n              );\n            })}\n          </ol>\n        </nav>\n      </div>\n\n      {/* Form Content */}\n      <form onSubmit={handleSubmit(onSubmit)} className=\"bg-white shadow rounded-lg p-6\">\n        {/* Step 1: Personal Information */}\n        {currentStep === 1 && (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                <User className=\"w-5 h-5 mr-2\" />\n                Personal Information\n              </h3>\n            </div>\n\n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  First Name *\n                </label>\n                <input\n                  {...register('firstName')}\n                  type=\"text\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Enter your first name\"\n                />\n                {errors.firstName && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.firstName.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Last Name *\n                </label>\n                <input\n                  {...register('lastName')}\n                  type=\"text\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Enter your last name\"\n                />\n                {errors.lastName && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.lastName.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Email Address *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('email')}\n                    type=\"email\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    placeholder=\"Enter your email\"\n                  />\n                  <Mail className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.email && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Phone Number *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('phoneNumber')}\n                    type=\"tel\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    placeholder=\"10-digit mobile number\"\n                  />\n                  <Phone className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.phoneNumber && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.phoneNumber.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Date of Birth *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('dateOfBirth')}\n                    type=\"date\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  />\n                  <Calendar className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.dateOfBirth && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.dateOfBirth.message}</p>\n                )}\n              </div>\n            </div>\n\n            {/* Account Information */}\n            <div>\n              <h4 className=\"text-md font-medium text-gray-900 mb-4 flex items-center\">\n                <User className=\"w-4 h-4 mr-2\" />\n                Account Information\n              </h4>\n            </div>\n\n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Username *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('username')}\n                    type=\"text\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    placeholder=\"Choose a unique username\"\n                  />\n                  <User className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.username && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.username.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Password *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('password')}\n                    type=\"password\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    placeholder=\"Create a strong password\"\n                  />\n                  <Lock className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.password && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.password.message}</p>\n                )}\n              </div>\n\n              <div className=\"sm:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Confirm Password *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('confirmPassword')}\n                    type=\"password\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    placeholder=\"Confirm your password\"\n                  />\n                  <Lock className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.confirmPassword && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.confirmPassword.message}</p>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Step 2: Address & Identity Details */}\n        {currentStep === 2 && (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                <MapPin className=\"w-5 h-5 mr-2\" />\n                Address & Identity Details\n              </h3>\n            </div>\n\n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Aadhar Number *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('aadharNumber')}\n                    type=\"text\"\n                    maxLength=\"12\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    placeholder=\"12-digit Aadhar number\"\n                  />\n                  <CreditCard className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.aadharNumber && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.aadharNumber.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  PAN Number *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('panNumber')}\n                    type=\"text\"\n                    maxLength=\"10\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    placeholder=\"**********\"\n                    style={{ textTransform: 'uppercase' }}\n                  />\n                  <CreditCard className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.panNumber && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.panNumber.message}</p>\n                )}\n              </div>\n\n              <div className=\"sm:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Address *\n                </label>\n                <textarea\n                  {...register('address')}\n                  rows={3}\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Enter your complete address\"\n                />\n                {errors.address && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.address.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  City *\n                </label>\n                <input\n                  {...register('city')}\n                  type=\"text\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Enter your city\"\n                />\n                {errors.city && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.city.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  State *\n                </label>\n                <select\n                  {...register('state')}\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                >\n                  <option value=\"\">Select State</option>\n                  <option value=\"Andhra Pradesh\">Andhra Pradesh</option>\n                  <option value=\"Arunachal Pradesh\">Arunachal Pradesh</option>\n                  <option value=\"Assam\">Assam</option>\n                  <option value=\"Bihar\">Bihar</option>\n                  <option value=\"Chhattisgarh\">Chhattisgarh</option>\n                  <option value=\"Goa\">Goa</option>\n                  <option value=\"Gujarat\">Gujarat</option>\n                  <option value=\"Haryana\">Haryana</option>\n                  <option value=\"Himachal Pradesh\">Himachal Pradesh</option>\n                  <option value=\"Jharkhand\">Jharkhand</option>\n                  <option value=\"Karnataka\">Karnataka</option>\n                  <option value=\"Kerala\">Kerala</option>\n                  <option value=\"Madhya Pradesh\">Madhya Pradesh</option>\n                  <option value=\"Maharashtra\">Maharashtra</option>\n                  <option value=\"Manipur\">Manipur</option>\n                  <option value=\"Meghalaya\">Meghalaya</option>\n                  <option value=\"Mizoram\">Mizoram</option>\n                  <option value=\"Nagaland\">Nagaland</option>\n                  <option value=\"Odisha\">Odisha</option>\n                  <option value=\"Punjab\">Punjab</option>\n                  <option value=\"Rajasthan\">Rajasthan</option>\n                  <option value=\"Sikkim\">Sikkim</option>\n                  <option value=\"Tamil Nadu\">Tamil Nadu</option>\n                  <option value=\"Telangana\">Telangana</option>\n                  <option value=\"Tripura\">Tripura</option>\n                  <option value=\"Uttar Pradesh\">Uttar Pradesh</option>\n                  <option value=\"Uttarakhand\">Uttarakhand</option>\n                  <option value=\"West Bengal\">West Bengal</option>\n                  <option value=\"Delhi\">Delhi</option>\n                </select>\n                {errors.state && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.state.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  PIN Code *\n                </label>\n                <input\n                  {...register('pinCode')}\n                  type=\"text\"\n                  maxLength=\"6\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"6-digit PIN code\"\n                />\n                {errors.pinCode && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.pinCode.message}</p>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Step 3: Document Upload */}\n        {currentStep === 3 && (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                <Upload className=\"w-5 h-5 mr-2\" />\n                Document Upload\n              </h3>\n              <p className=\"text-sm text-gray-600\">\n                Please upload clear, readable copies of the following documents. Accepted formats: JPEG, PNG, PDF (Max 5MB each)\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n              {[\n                { key: 'aadharCard', label: 'Aadhar Card', required: true },\n                { key: 'panCard', label: 'PAN Card', required: true },\n                { key: 'photo', label: 'Passport Size Photo', required: true },\n                { key: 'bankPassbook', label: 'Bank Passbook/Statement', required: false },\n                { key: 'businessProof', label: 'Business Proof (if applicable)', required: false }\n              ].map((doc) => (\n                <div key={doc.key} className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 transition-colors\">\n                  <div className=\"text-center\">\n                    <Upload className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <div className=\"mt-4\">\n                      <label className=\"cursor-pointer\">\n                        <span className=\"mt-2 block text-sm font-medium text-gray-900\">\n                          {doc.label} {doc.required && '*'}\n                        </span>\n                        <input\n                          type=\"file\"\n                          className=\"hidden\"\n                          accept=\".jpg,.jpeg,.png,.pdf\"\n                          onChange={(e) => handleFileUpload(doc.key, e.target.files[0])}\n                        />\n                        <span className=\"mt-2 block text-xs text-gray-500\">\n                          Click to upload or drag and drop\n                        </span>\n                      </label>\n                    </div>\n                    {documents[doc.key] && (\n                      <div className=\"mt-2 text-sm text-green-600 flex items-center justify-center\">\n                        <FileText className=\"w-4 h-4 mr-1\" />\n                        {documents[doc.key].name}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Step 4: Review & Submit */}\n        {currentStep === 4 && (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                <FileText className=\"w-5 h-5 mr-2\" />\n                Review & Submit\n              </h3>\n              <p className=\"text-sm text-gray-600\">\n                Please review all the information before submitting your application.\n              </p>\n            </div>\n\n            <div className=\"bg-gray-50 rounded-lg p-6 space-y-4\">\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">Personal Information</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-gray-500\">Name:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('firstName')} {getValues('lastName')}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-500\">Email:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('email')}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-500\">Phone:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('phoneNumber')}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-500\">Date of Birth:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('dateOfBirth')}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">Identity & Address</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-gray-500\">Aadhar:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('aadharNumber')}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-500\">PAN:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('panNumber')}</span>\n                  </div>\n                  <div className=\"col-span-2\">\n                    <span className=\"text-gray-500\">Address:</span>\n                    <span className=\"ml-2 text-gray-900\">\n                      {getValues('address')}, {getValues('city')}, {getValues('state')} - {getValues('pinCode')}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">Account Information</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-gray-500\">Username:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('username')}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-500\">Password:</span>\n                    <span className=\"ml-2 text-gray-900\">••••••••</span>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">Uploaded Documents</h4>\n                <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                  {Object.entries(documents).map(([key, file]) => (\n                    file && (\n                      <div key={key} className=\"flex items-center text-green-600\">\n                        <FileText className=\"w-4 h-4 mr-1\" />\n                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}\n                      </div>\n                    )\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0\">\n                  <FileText className=\"h-5 w-5 text-blue-400\" />\n                </div>\n                <div className=\"ml-3\">\n                  <h3 className=\"text-sm font-medium text-blue-800\">\n                    Important Information\n                  </h3>\n                  <div className=\"mt-2 text-sm text-blue-700\">\n                    <ul className=\"list-disc list-inside space-y-1\">\n                      <li>Your application will be reviewed within 3-5 business days</li>\n                      <li>You will receive email notifications about status updates</li>\n                      <li>Ensure all uploaded documents are clear and readable</li>\n                      <li>Any false information may lead to application rejection</li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Navigation Buttons */}\n        <div className=\"flex justify-between pt-6 border-t border-gray-200\">\n          <button\n            type=\"button\"\n            onClick={handlePrevious}\n            disabled={currentStep === 1}\n            className=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            Previous\n          </button>\n\n          {currentStep < 4 ? (\n            <button\n              type=\"button\"\n              onClick={handleNext}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Next\n            </button>\n          ) : (\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Submitting...\n                </>\n              ) : (\n                'Submit Application'\n              )}\n            </button>\n          )}\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default AgentRegistration;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,IAAI,QACC,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,MAAM,GAAGpB,GAAG,CAACqB,MAAM,CAAC;EACxB;EACAC,SAAS,EAAEtB,GAAG,CAACuB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,wBAAwB,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC;EACzFC,QAAQ,EAAE1B,GAAG,CAACuB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,uBAAuB,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC;EACvFE,KAAK,EAAE3B,GAAG,CAACuB,MAAM,CAAC,CAAC,CAACI,KAAK,CAAC,sBAAsB,CAAC,CAACH,QAAQ,CAAC,mBAAmB,CAAC;EAC/EI,WAAW,EAAE5B,GAAG,CAACuB,MAAM,CAAC,CAAC,CACtBM,OAAO,CAAC,aAAa,EAAE,gCAAgC,CAAC,CACxDL,QAAQ,CAAC,0BAA0B,CAAC;EACvCM,YAAY,EAAE9B,GAAG,CAACuB,MAAM,CAAC,CAAC,CACvBM,OAAO,CAAC,aAAa,EAAE,iCAAiC,CAAC,CACzDL,QAAQ,CAAC,2BAA2B,CAAC;EACxCO,SAAS,EAAE/B,GAAG,CAACuB,MAAM,CAAC,CAAC,CACpBM,OAAO,CAAC,4BAA4B,EAAE,oBAAoB,CAAC,CAC3DL,QAAQ,CAAC,wBAAwB,CAAC;EACrCQ,WAAW,EAAEhC,GAAG,CAACiC,IAAI,CAAC,CAAC,CACpBC,GAAG,CAAC,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,+BAA+B,CAAC,CAC3FZ,QAAQ,CAAC,2BAA2B,CAAC;EAExC;EACAa,OAAO,EAAErC,GAAG,CAACuB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,qBAAqB,CAAC,CAACC,GAAG,CAAC,EAAE,EAAE,mBAAmB,CAAC;EAClFa,IAAI,EAAEtC,GAAG,CAACuB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,kBAAkB,CAAC;EAC/Ce,KAAK,EAAEvC,GAAG,CAACuB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,mBAAmB,CAAC;EACjDgB,OAAO,EAAExC,GAAG,CAACuB,MAAM,CAAC,CAAC,CAClBM,OAAO,CAAC,YAAY,EAAE,2BAA2B,CAAC,CAClDL,QAAQ,CAAC,sBAAsB,CAAC;EAEnC;EACAiB,QAAQ,EAAEzC,GAAG,CAACuB,MAAM,CAAC,CAAC,CACnBE,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDS,GAAG,CAAC,EAAE,EAAE,0CAA0C,CAAC,CACnDL,OAAO,CAAC,iBAAiB,EAAE,6DAA6D,CAAC,CACzFL,QAAQ,CAAC,sBAAsB,CAAC;EACnCkB,QAAQ,EAAE1C,GAAG,CAACuB,MAAM,CAAC,CAAC,CACnBE,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC,CAChDI,OAAO,CAAC,iEAAiE,EACxE,kHAAkH,CAAC,CACpHL,QAAQ,CAAC,sBAAsB,CAAC;EACnCmB,eAAe,EAAE3C,GAAG,CAACuB,MAAM,CAAC,CAAC,CAC1BqB,KAAK,CAAC,CAAC5C,GAAG,CAAC6C,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,sBAAsB,CAAC,CACpDrB,QAAQ,CAAC,8BAA8B;AAC5C,CAAC,CAAC;AAEF,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAG/C,WAAW,CAAC,CAAC;EAC9B,MAAMgD,QAAQ,GAAG/C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyD,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC;IACzC2D,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAM;IACJC,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO,CAAC;IACrBC,OAAO;IACPC;EACF,CAAC,GAAGpE,OAAO,CAAC;IACVqE,QAAQ,EAAEpE,WAAW,CAACqB,MAAM,CAAC;IAC7BgD,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAMC,KAAK,GAAG,CACZ;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,sBAAsB;IAAEC,IAAI,EAAEnE;EAAK,CAAC,EACnD;IAAEiE,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAEhE;EAAO,CAAC,EAChD;IAAE8D,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE7D;EAAO,CAAC,EAChD;IAAE2D,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE5D;EAAS,CAAC,CACnD;EAED,MAAM6D,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAIC,gBAAgB,GAAG,EAAE;IAEzB,QAAQtB,WAAW;MACjB,KAAK,CAAC;QACJsB,gBAAgB,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,iBAAiB,CAAC;QAC9H;MACF,KAAK,CAAC;QACJA,gBAAgB,GAAG,CAAC,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;QACvF;MACF,KAAK,CAAC;QACJ;QACA,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,CAAC;QACvD,MAAMC,WAAW,GAAGD,YAAY,CAACE,MAAM,CAACC,GAAG,IAAI,CAACxB,SAAS,CAACwB,GAAG,CAAC,CAAC;QAC/D,IAAIF,WAAW,CAACG,MAAM,GAAG,CAAC,EAAE;UAC1B3E,KAAK,CAAC4E,KAAK,CAAC,kBAAkBJ,WAAW,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;UACvD;QACF;QACA;IACJ;IAEA,IAAIP,gBAAgB,CAACK,MAAM,GAAG,CAAC,EAAE;MAC/B,MAAMG,OAAO,GAAG,MAAMjB,OAAO,CAACS,gBAAgB,CAAC;MAC/C,IAAI,CAACQ,OAAO,EAAE;IAChB;IAEA7B,cAAc,CAAC8B,IAAI,IAAIC,IAAI,CAAC3D,GAAG,CAAC0D,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3BhC,cAAc,CAAC8B,IAAI,IAAIC,IAAI,CAAClD,GAAG,CAACiD,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAACC,YAAY,EAAEC,IAAI,KAAK;IAC/C,IAAI,CAACA,IAAI,EAAE;;IAEX;IACA,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;IACnE,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;MACrCvF,KAAK,CAAC4E,KAAK,CAAC,2CAA2C,CAAC;MACxD;IACF;;IAEA;IACA,IAAIQ,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/BxF,KAAK,CAAC4E,KAAK,CAAC,iCAAiC,CAAC;MAC9C;IACF;IAEAzB,YAAY,CAAC4B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACI,YAAY,GAAGC;IAClB,CAAC,CAAC,CAAC;IAEHpF,KAAK,CAACyF,OAAO,CAAC,GAAGN,YAAY,wBAAwB,CAAC;EACxD,CAAC;EAED,MAAMO,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B5C,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM;QAAER,eAAe;QAAE,GAAGqD;MAAS,CAAC,GAAGD,IAAI;MAC7C,MAAME,aAAa,GAAG;QACpB,GAAGD,QAAQ;QACXhE,WAAW,EAAE,IAAIG,IAAI,CAAC6D,QAAQ,CAAChE,WAAW,CAAC,CAACkE,WAAW,CAAC;MAC1D,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMhG,QAAQ,CAAC0D,QAAQ,CAACoC,aAAa,CAAC;MAEvD,IAAIE,QAAQ,CAACN,OAAO,EAAE;QACpBzF,KAAK,CAACyF,OAAO,CAAC,4CAA4C,CAAC;QAC3D;QACA,IAAI5C,QAAQ,CAACmD,QAAQ,KAAK,WAAW,EAAE;UACrC;UACApD,QAAQ,CAAC,QAAQ,EAAE;YACjBT,KAAK,EAAE;cAAE8D,OAAO,EAAE;YAA+D;UACnF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACArD,QAAQ,CAAC,SAAS,CAAC;QACrB;MACF;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MAAA,IAAAsB,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACxB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CwB,OAAO,CAACxB,KAAK,CAAC,gBAAgB,EAAE;QAC9BqB,OAAO,EAAErB,KAAK,CAACqB,OAAO;QACtBF,QAAQ,EAAEnB,KAAK,CAACmB,QAAQ;QACxBM,OAAO,EAAEzB,KAAK,CAACyB,OAAO;QACtBC,MAAM,EAAE1B,KAAK,CAAC0B;MAChB,CAAC,CAAC;MACF,MAAML,OAAO,GAAG,EAAAC,eAAA,GAAAtB,KAAK,CAACmB,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBP,IAAI,cAAAQ,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,wCAAwC;MACzFjG,KAAK,CAAC4E,KAAK,CAACqB,OAAO,CAAC;IACtB,CAAC,SAAS;MACRlD,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACElC,OAAA;IAAK0F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3F,OAAA;MAAA2F,QAAA,gBACE3F,OAAA;QAAI0F,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxE/F,OAAA;QAAG0F,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN/F,OAAA;MAAK0F,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7C3F,OAAA;QAAK,cAAW,UAAU;QAAA2F,QAAA,eACxB3F,OAAA;UAAI0F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC9BvC,KAAK,CAAC4C,GAAG,CAAC,CAACC,IAAI,EAAEC,OAAO,KAAK;YAC5B,MAAMC,IAAI,GAAGF,IAAI,CAAC1C,IAAI;YACtB,MAAM6C,WAAW,GAAGjE,WAAW,GAAG8D,IAAI,CAAC5C,EAAE;YACzC,MAAMgD,SAAS,GAAGlE,WAAW,KAAK8D,IAAI,CAAC5C,EAAE;YAEzC,oBACErD,OAAA;cAAoB0F,SAAS,EAAE,GAAGQ,OAAO,KAAK9C,KAAK,CAACU,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAA6B,QAAA,eAC/E3F,OAAA;gBAAK0F,SAAS,EAAE,qBAAqBQ,OAAO,KAAK9C,KAAK,CAACU,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAAA6B,QAAA,gBAClF3F,OAAA;kBAAK0F,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC3F,OAAA;oBAAK0F,SAAS,EAAE;AACtC;AACA,0BAA0BU,WAAW,GACT,wCAAwC,GACxCC,SAAS,GACP,+BAA+B,GAC/B,+BAA+B;AAC7D,uBACwB;oBAAAV,QAAA,eACA3F,OAAA,CAACmG,IAAI;sBAACT,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACN/F,OAAA;oBAAM0F,SAAS,EAAE,4BACfW,SAAS,GAAG,eAAe,GAAGD,WAAW,GAAG,eAAe,GAAG,eAAe,EAC5E;oBAAAT,QAAA,EACAM,IAAI,CAAC3C;kBAAI;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACLG,OAAO,KAAK9C,KAAK,CAACU,MAAM,GAAG,CAAC,iBAC3B9D,OAAA;kBAAK0F,SAAS,EAAE,qBACdU,WAAW,GAAG,aAAa,GAAG,aAAa;gBAC1C;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GAzBCE,IAAI,CAAC3C,IAAI;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Bd,CAAC;UAET,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/F,OAAA;MAAM6E,QAAQ,EAAEhC,YAAY,CAACgC,QAAQ,CAAE;MAACa,SAAS,EAAC,gCAAgC;MAAAC,QAAA,GAE/ExD,WAAW,KAAK,CAAC,iBAChBnC,OAAA;QAAK0F,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3F,OAAA;UAAA2F,QAAA,eACE3F,OAAA;YAAI0F,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtE3F,OAAA,CAACZ,IAAI;cAACsG,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN/F,OAAA;UAAK0F,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD3F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAA,GACM4C,QAAQ,CAAC,WAAW,CAAC;cACzB8B,IAAI,EAAC,MAAM;cACXgB,SAAS,EAAC,6GAA6G;cACvHY,WAAW,EAAC;YAAuB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,EACDhD,MAAM,CAAC1C,SAAS,iBACfL,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAAC1C,SAAS,CAAC+E;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACvE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAA,GACM4C,QAAQ,CAAC,UAAU,CAAC;cACxB8B,IAAI,EAAC,MAAM;cACXgB,SAAS,EAAC,6GAA6G;cACvHY,WAAW,EAAC;YAAsB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,EACDhD,MAAM,CAACtC,QAAQ,iBACdT,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAACtC,QAAQ,CAAC2E;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACtE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3F,OAAA;gBAAA,GACM4C,QAAQ,CAAC,OAAO,CAAC;gBACrB8B,IAAI,EAAC,OAAO;gBACZgB,SAAS,EAAC,8GAA8G;gBACxHY,WAAW,EAAC;cAAkB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACF/F,OAAA,CAACX,IAAI;gBAACqG,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,EACLhD,MAAM,CAACrC,KAAK,iBACXV,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAACrC,KAAK,CAAC0E;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACnE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3F,OAAA;gBAAA,GACM4C,QAAQ,CAAC,aAAa,CAAC;gBAC3B8B,IAAI,EAAC,KAAK;gBACVgB,SAAS,EAAC,8GAA8G;gBACxHY,WAAW,EAAC;cAAwB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACF/F,OAAA,CAACV,KAAK;gBAACoG,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,EACLhD,MAAM,CAACpC,WAAW,iBACjBX,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAACpC,WAAW,CAACyE;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3F,OAAA;gBAAA,GACM4C,QAAQ,CAAC,aAAa,CAAC;gBAC3B8B,IAAI,EAAC,MAAM;gBACXgB,SAAS,EAAC;cAA8G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzH,CAAC,eACF/F,OAAA,CAACR,QAAQ;gBAACkG,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,EACLhD,MAAM,CAAChC,WAAW,iBACjBf,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAAChC,WAAW,CAACqE;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN/F,OAAA;UAAA2F,QAAA,eACE3F,OAAA;YAAI0F,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtE3F,OAAA,CAACZ,IAAI;cAACsG,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,uBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN/F,OAAA;UAAK0F,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD3F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3F,OAAA;gBAAA,GACM4C,QAAQ,CAAC,UAAU,CAAC;gBACxB8B,IAAI,EAAC,MAAM;gBACXgB,SAAS,EAAC,8GAA8G;gBACxHY,WAAW,EAAC;cAA0B;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACF/F,OAAA,CAACZ,IAAI;gBAACsG,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,EACLhD,MAAM,CAACvB,QAAQ,iBACdxB,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAACvB,QAAQ,CAAC4D;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACtE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3F,OAAA;gBAAA,GACM4C,QAAQ,CAAC,UAAU,CAAC;gBACxB8B,IAAI,EAAC,UAAU;gBACfgB,SAAS,EAAC,8GAA8G;gBACxHY,WAAW,EAAC;cAA0B;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACF/F,OAAA,CAACF,IAAI;gBAAC4F,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,EACLhD,MAAM,CAACtB,QAAQ,iBACdzB,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAACtB,QAAQ,CAAC2D;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACtE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/F,OAAA;YAAK0F,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3F,OAAA;gBAAA,GACM4C,QAAQ,CAAC,iBAAiB,CAAC;gBAC/B8B,IAAI,EAAC,UAAU;gBACfgB,SAAS,EAAC,8GAA8G;gBACxHY,WAAW,EAAC;cAAuB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACF/F,OAAA,CAACF,IAAI;gBAAC4F,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,EACLhD,MAAM,CAACrB,eAAe,iBACrB1B,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAACrB,eAAe,CAAC0D;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC7E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5D,WAAW,KAAK,CAAC,iBAChBnC,OAAA;QAAK0F,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3F,OAAA;UAAA2F,QAAA,eACE3F,OAAA;YAAI0F,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtE3F,OAAA,CAACT,MAAM;cAACmG,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8BAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN/F,OAAA;UAAK0F,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpD3F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3F,OAAA;gBAAA,GACM4C,QAAQ,CAAC,cAAc,CAAC;gBAC5B8B,IAAI,EAAC,MAAM;gBACX6B,SAAS,EAAC,IAAI;gBACdb,SAAS,EAAC,8GAA8G;gBACxHY,WAAW,EAAC;cAAwB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACF/F,OAAA,CAACP,UAAU;gBAACiG,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,EACLhD,MAAM,CAAClC,YAAY,iBAClBb,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAAClC,YAAY,CAACuE;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC1E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B3F,OAAA;gBAAA,GACM4C,QAAQ,CAAC,WAAW,CAAC;gBACzB8B,IAAI,EAAC,MAAM;gBACX6B,SAAS,EAAC,IAAI;gBACdb,SAAS,EAAC,8GAA8G;gBACxHY,WAAW,EAAC,YAAY;gBACxBE,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAY;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACF/F,OAAA,CAACP,UAAU;gBAACiG,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,EACLhD,MAAM,CAACjC,SAAS,iBACfd,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAACjC,SAAS,CAACsE;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACvE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/F,OAAA;YAAK0F,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAA,GACM4C,QAAQ,CAAC,SAAS,CAAC;cACvB8D,IAAI,EAAE,CAAE;cACRhB,SAAS,EAAC,6GAA6G;cACvHY,WAAW,EAAC;YAA6B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,EACDhD,MAAM,CAAC3B,OAAO,iBACbpB,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAAC3B,OAAO,CAACgE;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACrE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAA,GACM4C,QAAQ,CAAC,MAAM,CAAC;cACpB8B,IAAI,EAAC,MAAM;cACXgB,SAAS,EAAC,6GAA6G;cACvHY,WAAW,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACDhD,MAAM,CAAC1B,IAAI,iBACVrB,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAAC1B,IAAI,CAAC+D;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAClE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAA,GACM4C,QAAQ,CAAC,OAAO,CAAC;cACrB8C,SAAS,EAAC,6GAA6G;cAAAC,QAAA,gBAEvH3F,OAAA;gBAAQ2G,KAAK,EAAC,EAAE;gBAAAhB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC/F,OAAA;gBAAQ2G,KAAK,EAAC,gBAAgB;gBAAAhB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtD/F,OAAA;gBAAQ2G,KAAK,EAAC,mBAAmB;gBAAAhB,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5D/F,OAAA;gBAAQ2G,KAAK,EAAC,OAAO;gBAAAhB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC/F,OAAA;gBAAQ2G,KAAK,EAAC,OAAO;gBAAAhB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpC/F,OAAA;gBAAQ2G,KAAK,EAAC,cAAc;gBAAAhB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClD/F,OAAA;gBAAQ2G,KAAK,EAAC,KAAK;gBAAAhB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC/F,OAAA;gBAAQ2G,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC/F,OAAA;gBAAQ2G,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC/F,OAAA;gBAAQ2G,KAAK,EAAC,kBAAkB;gBAAAhB,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1D/F,OAAA;gBAAQ2G,KAAK,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C/F,OAAA;gBAAQ2G,KAAK,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C/F,OAAA;gBAAQ2G,KAAK,EAAC,QAAQ;gBAAAhB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC/F,OAAA;gBAAQ2G,KAAK,EAAC,gBAAgB;gBAAAhB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtD/F,OAAA;gBAAQ2G,KAAK,EAAC,aAAa;gBAAAhB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD/F,OAAA;gBAAQ2G,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC/F,OAAA;gBAAQ2G,KAAK,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C/F,OAAA;gBAAQ2G,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC/F,OAAA;gBAAQ2G,KAAK,EAAC,UAAU;gBAAAhB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1C/F,OAAA;gBAAQ2G,KAAK,EAAC,QAAQ;gBAAAhB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC/F,OAAA;gBAAQ2G,KAAK,EAAC,QAAQ;gBAAAhB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC/F,OAAA;gBAAQ2G,KAAK,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C/F,OAAA;gBAAQ2G,KAAK,EAAC,QAAQ;gBAAAhB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC/F,OAAA;gBAAQ2G,KAAK,EAAC,YAAY;gBAAAhB,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9C/F,OAAA;gBAAQ2G,KAAK,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C/F,OAAA;gBAAQ2G,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC/F,OAAA;gBAAQ2G,KAAK,EAAC,eAAe;gBAAAhB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpD/F,OAAA;gBAAQ2G,KAAK,EAAC,aAAa;gBAAAhB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD/F,OAAA;gBAAQ2G,KAAK,EAAC,aAAa;gBAAAhB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChD/F,OAAA;gBAAQ2G,KAAK,EAAC,OAAO;gBAAAhB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACRhD,MAAM,CAACzB,KAAK,iBACXtB,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAACzB,KAAK,CAAC8D;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACnE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAO0F,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/F,OAAA;cAAA,GACM4C,QAAQ,CAAC,SAAS,CAAC;cACvB8B,IAAI,EAAC,MAAM;cACX6B,SAAS,EAAC,GAAG;cACbb,SAAS,EAAC,6GAA6G;cACvHY,WAAW,EAAC;YAAkB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,EACDhD,MAAM,CAACxB,OAAO,iBACbvB,OAAA;cAAG0F,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE5C,MAAM,CAACxB,OAAO,CAAC6D;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACrE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5D,WAAW,KAAK,CAAC,iBAChBnC,OAAA;QAAK0F,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3F,OAAA;UAAA2F,QAAA,gBACE3F,OAAA;YAAI0F,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtE3F,OAAA,CAACN,MAAM;cAACgG,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL/F,OAAA;YAAG0F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN/F,OAAA;UAAK0F,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnD,CACC;YAAEiB,GAAG,EAAE,YAAY;YAAEC,KAAK,EAAE,aAAa;YAAEtG,QAAQ,EAAE;UAAK,CAAC,EAC3D;YAAEqG,GAAG,EAAE,SAAS;YAAEC,KAAK,EAAE,UAAU;YAAEtG,QAAQ,EAAE;UAAK,CAAC,EACrD;YAAEqG,GAAG,EAAE,OAAO;YAAEC,KAAK,EAAE,qBAAqB;YAAEtG,QAAQ,EAAE;UAAK,CAAC,EAC9D;YAAEqG,GAAG,EAAE,cAAc;YAAEC,KAAK,EAAE,yBAAyB;YAAEtG,QAAQ,EAAE;UAAM,CAAC,EAC1E;YAAEqG,GAAG,EAAE,eAAe;YAAEC,KAAK,EAAE,gCAAgC;YAAEtG,QAAQ,EAAE;UAAM,CAAC,CACnF,CAACyF,GAAG,CAAEnC,GAAG,iBACR7D,OAAA;YAAmB0F,SAAS,EAAC,+FAA+F;YAAAC,QAAA,eAC1H3F,OAAA;cAAK0F,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3F,OAAA,CAACN,MAAM;gBAACgG,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtD/F,OAAA;gBAAK0F,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnB3F,OAAA;kBAAO0F,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC/B3F,OAAA;oBAAM0F,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,GAC3D9B,GAAG,CAACgD,KAAK,EAAC,GAAC,EAAChD,GAAG,CAACtD,QAAQ,IAAI,GAAG;kBAAA;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACP/F,OAAA;oBACE0E,IAAI,EAAC,MAAM;oBACXgB,SAAS,EAAC,QAAQ;oBAClBoB,MAAM,EAAC,sBAAsB;oBAC7BC,QAAQ,EAAGC,CAAC,IAAK3C,gBAAgB,CAACR,GAAG,CAAC+C,GAAG,EAAEI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACF/F,OAAA;oBAAM0F,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EACL1D,SAAS,CAACwB,GAAG,CAAC+C,GAAG,CAAC,iBACjB5G,OAAA;gBAAK0F,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3E3F,OAAA,CAACL,QAAQ;kBAAC+F,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpC1D,SAAS,CAACwB,GAAG,CAAC+C,GAAG,CAAC,CAACtD,IAAI;cAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GAzBElC,GAAG,CAAC+C,GAAG;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5D,WAAW,KAAK,CAAC,iBAChBnC,OAAA;QAAK0F,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB3F,OAAA;UAAA2F,QAAA,gBACE3F,OAAA;YAAI0F,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtE3F,OAAA,CAACL,QAAQ;cAAC+F,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL/F,OAAA;YAAG0F,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN/F,OAAA;UAAK0F,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClD3F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAI0F,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxE/F,OAAA;cAAK0F,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7C3F,OAAA;gBAAA2F,QAAA,gBACE3F,OAAA;kBAAM0F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5C/F,OAAA;kBAAM0F,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAAE1C,SAAS,CAAC,WAAW,CAAC,EAAC,GAAC,EAACA,SAAS,CAAC,UAAU,CAAC;gBAAA;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC,eACN/F,OAAA;gBAAA2F,QAAA,gBACE3F,OAAA;kBAAM0F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7C/F,OAAA;kBAAM0F,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAE1C,SAAS,CAAC,OAAO;gBAAC;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACN/F,OAAA;gBAAA2F,QAAA,gBACE3F,OAAA;kBAAM0F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7C/F,OAAA;kBAAM0F,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAE1C,SAAS,CAAC,aAAa;gBAAC;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACN/F,OAAA;gBAAA2F,QAAA,gBACE3F,OAAA;kBAAM0F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrD/F,OAAA;kBAAM0F,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAE1C,SAAS,CAAC,aAAa;gBAAC;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAI0F,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtE/F,OAAA;cAAK0F,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7C3F,OAAA;gBAAA2F,QAAA,gBACE3F,OAAA;kBAAM0F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9C/F,OAAA;kBAAM0F,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAE1C,SAAS,CAAC,cAAc;gBAAC;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACN/F,OAAA;gBAAA2F,QAAA,gBACE3F,OAAA;kBAAM0F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3C/F,OAAA;kBAAM0F,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAE1C,SAAS,CAAC,WAAW;gBAAC;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACN/F,OAAA;gBAAK0F,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB3F,OAAA;kBAAM0F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/C/F,OAAA;kBAAM0F,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GACjC1C,SAAS,CAAC,SAAS,CAAC,EAAC,IAAE,EAACA,SAAS,CAAC,MAAM,CAAC,EAAC,IAAE,EAACA,SAAS,CAAC,OAAO,CAAC,EAAC,KAAG,EAACA,SAAS,CAAC,SAAS,CAAC;gBAAA;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAI0F,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvE/F,OAAA;cAAK0F,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7C3F,OAAA;gBAAA2F,QAAA,gBACE3F,OAAA;kBAAM0F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChD/F,OAAA;kBAAM0F,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAE1C,SAAS,CAAC,UAAU;gBAAC;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eACN/F,OAAA;gBAAA2F,QAAA,gBACE3F,OAAA;kBAAM0F,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChD/F,OAAA;kBAAM0F,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/F,OAAA;YAAA2F,QAAA,gBACE3F,OAAA;cAAI0F,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtE/F,OAAA;cAAK0F,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAC5CwB,MAAM,CAACC,OAAO,CAAC/E,SAAS,CAAC,CAAC2D,GAAG,CAAC,CAAC,CAACY,GAAG,EAAErC,IAAI,CAAC,KACzCA,IAAI,iBACFvE,OAAA;gBAAe0F,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBACzD3F,OAAA,CAACL,QAAQ;kBAAC+F,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpCa,GAAG,CAACS,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,IAAI,EAAEC,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC;cAAA,GAF/DX,GAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGR,CAER;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/F,OAAA;UAAK0F,SAAS,EAAC,kDAAkD;UAAAC,QAAA,eAC/D3F,OAAA;YAAK0F,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB3F,OAAA;cAAK0F,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B3F,OAAA,CAACL,QAAQ;gBAAC+F,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACN/F,OAAA;cAAK0F,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB3F,OAAA;gBAAI0F,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAElD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/F,OAAA;gBAAK0F,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,eACzC3F,OAAA;kBAAI0F,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC7C3F,OAAA;oBAAA2F,QAAA,EAAI;kBAA0D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnE/F,OAAA;oBAAA2F,QAAA,EAAI;kBAAyD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClE/F,OAAA;oBAAA2F,QAAA,EAAI;kBAAoD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7D/F,OAAA;oBAAA2F,QAAA,EAAI;kBAAuD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD/F,OAAA;QAAK0F,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjE3F,OAAA;UACE0E,IAAI,EAAC,QAAQ;UACb8C,OAAO,EAAEpD,cAAe;UACxBqD,QAAQ,EAAEtF,WAAW,KAAK,CAAE;UAC5BuD,SAAS,EAAC,oQAAoQ;UAAAC,QAAA,EAC/Q;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAER5D,WAAW,GAAG,CAAC,gBACdnC,OAAA;UACE0E,IAAI,EAAC,QAAQ;UACb8C,OAAO,EAAEhE,UAAW;UACpBkC,SAAS,EAAC,wNAAwN;UAAAC,QAAA,EACnO;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAET/F,OAAA;UACE0E,IAAI,EAAC,QAAQ;UACb+C,QAAQ,EAAExF,YAAa;UACvByD,SAAS,EAAC,2QAA2Q;UAAAC,QAAA,EAEpR1D,YAAY,gBACXjC,OAAA,CAAAE,SAAA;YAAAyF,QAAA,gBACE3F,OAAA;cAAK0F,SAAS,EAAC;YAAgE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iBAExF;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjE,EAAA,CA/qBID,iBAAiB;EAAA,QACJ7C,WAAW,EACXC,WAAW,EAiBxBJ,OAAO;AAAA;AAAA6I,EAAA,GAnBP7F,iBAAiB;AAirBvB,eAAeA,iBAAiB;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}