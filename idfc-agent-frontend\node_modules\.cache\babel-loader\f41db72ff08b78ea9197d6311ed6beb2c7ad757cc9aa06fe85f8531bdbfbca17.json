{"ast": null, "code": "import axios from 'axios';\nimport { toast } from 'react-toastify';\nconst API_BASE_URL = 'https://localhost:7093/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle errors\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  var _error$response, _error$response2, _error$response2$data;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'An error occurred';\n  toast.error(message);\n  return Promise.reject(error);\n});\n\n// Auth API\nexport const authAPI = {\n  login: async credentials => {\n    const response = await api.post('/auth/login', credentials);\n    return response.data;\n  },\n  logout: async () => {\n    const response = await api.post('/auth/logout');\n    return response.data;\n  },\n  getProfile: async () => {\n    const response = await api.get('/auth/profile');\n    return response.data;\n  },\n  changePassword: async passwordData => {\n    const response = await api.post('/auth/change-password', passwordData);\n    return response.data;\n  }\n};\n\n// Agent API\nexport const agentAPI = {\n  register: async agentData => {\n    const response = await api.post('/agents/register', agentData);\n    return response.data;\n  },\n  getAll: async (page = 1, pageSize = 10, status = null) => {\n    const params = {\n      page,\n      pageSize\n    };\n    if (status) params.status = status;\n    const response = await api.get('/agents', {\n      params\n    });\n    return response.data;\n  },\n  getById: async id => {\n    const response = await api.get(`/agents/${id}`);\n    return response.data;\n  },\n  update: async (id, agentData) => {\n    const response = await api.put(`/agents/${id}`, agentData);\n    return response.data;\n  },\n  updateStatus: async (id, status, comments = '') => {\n    const response = await api.patch(`/agents/${id}/status`, {\n      status,\n      comments\n    });\n    return response.data;\n  },\n  delete: async id => {\n    const response = await api.delete(`/agents/${id}`);\n    return response.data;\n  },\n  getStatusHistory: async id => {\n    const response = await api.get(`/agents/${id}/status-history`);\n    return response.data;\n  }\n};\n\n// Document API (placeholder for future implementation)\nexport const documentAPI = {\n  upload: async (agentId, file, documentType) => {\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('documentType', documentType);\n    const response = await api.post(`/agents/${agentId}/documents`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  },\n  getByAgent: async agentId => {\n    const response = await api.get(`/agents/${agentId}/documents`);\n    return response.data;\n  },\n  download: async documentId => {\n    const response = await api.get(`/documents/${documentId}/download`, {\n      responseType: 'blob'\n    });\n    return response.data;\n  },\n  updateStatus: async (documentId, status, comments = '') => {\n    const response = await api.patch(`/documents/${documentId}/status`, {\n      status,\n      comments\n    });\n    return response.data;\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "toast", "API_BASE_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "_error$response2", "_error$response2$data", "status", "removeItem", "window", "location", "href", "message", "data", "authAPI", "login", "credentials", "post", "logout", "getProfile", "get", "changePassword", "passwordData", "agentAPI", "register", "agentData", "getAll", "page", "pageSize", "params", "getById", "id", "update", "put", "updateStatus", "comments", "patch", "delete", "getStatusHistory", "documentAPI", "upload", "agentId", "file", "documentType", "formData", "FormData", "append", "getByAgent", "download", "documentId", "responseType"], "sources": ["D:/Augment-projects/IDFCAgentOnboardingAndManagementSolution/idfc-agent-frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\nimport { toast } from 'react-toastify';\n\nconst API_BASE_URL = 'https://localhost:7093/api';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle errors\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    \n    const message = error.response?.data?.message || 'An error occurred';\n    toast.error(message);\n    \n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  login: async (credentials) => {\n    const response = await api.post('/auth/login', credentials);\n    return response.data;\n  },\n  \n  logout: async () => {\n    const response = await api.post('/auth/logout');\n    return response.data;\n  },\n  \n  getProfile: async () => {\n    const response = await api.get('/auth/profile');\n    return response.data;\n  },\n  \n  changePassword: async (passwordData) => {\n    const response = await api.post('/auth/change-password', passwordData);\n    return response.data;\n  }\n};\n\n// Agent API\nexport const agentAPI = {\n  register: async (agentData) => {\n    const response = await api.post('/agents/register', agentData);\n    return response.data;\n  },\n  \n  getAll: async (page = 1, pageSize = 10, status = null) => {\n    const params = { page, pageSize };\n    if (status) params.status = status;\n    \n    const response = await api.get('/agents', { params });\n    return response.data;\n  },\n  \n  getById: async (id) => {\n    const response = await api.get(`/agents/${id}`);\n    return response.data;\n  },\n  \n  update: async (id, agentData) => {\n    const response = await api.put(`/agents/${id}`, agentData);\n    return response.data;\n  },\n  \n  updateStatus: async (id, status, comments = '') => {\n    const response = await api.patch(`/agents/${id}/status`, { status, comments });\n    return response.data;\n  },\n  \n  delete: async (id) => {\n    const response = await api.delete(`/agents/${id}`);\n    return response.data;\n  },\n  \n  getStatusHistory: async (id) => {\n    const response = await api.get(`/agents/${id}/status-history`);\n    return response.data;\n  }\n};\n\n// Document API (placeholder for future implementation)\nexport const documentAPI = {\n  upload: async (agentId, file, documentType) => {\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('documentType', documentType);\n    \n    const response = await api.post(`/agents/${agentId}/documents`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  },\n  \n  getByAgent: async (agentId) => {\n    const response = await api.get(`/agents/${agentId}/documents`);\n    return response.data;\n  },\n  \n  download: async (documentId) => {\n    const response = await api.get(`/documents/${documentId}/download`, {\n      responseType: 'blob',\n    });\n    return response.data;\n  },\n  \n  updateStatus: async (documentId, status, comments = '') => {\n    const response = await api.patch(`/documents/${documentId}/status`, { status, comments });\n    return response.data;\n  }\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,MAAMC,YAAY,GAAG,4BAA4B;;AAEjD;AACA,MAAMC,GAAG,GAAGH,KAAK,CAACI,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAI,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;EACT,IAAI,EAAAF,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,MAAK,GAAG,EAAE;IAClCV,YAAY,CAACW,UAAU,CAAC,OAAO,CAAC;IAChCX,YAAY,CAACW,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EAEA,MAAMC,OAAO,GAAG,EAAAP,gBAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBQ,IAAI,cAAAP,qBAAA,uBAApBA,qBAAA,CAAsBM,OAAO,KAAI,mBAAmB;EACpE1B,KAAK,CAACc,KAAK,CAACY,OAAO,CAAC;EAEpB,OAAOX,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMc,OAAO,GAAG;EACrBC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,MAAMb,QAAQ,GAAG,MAAMf,GAAG,CAAC6B,IAAI,CAAC,aAAa,EAAED,WAAW,CAAC;IAC3D,OAAOb,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDK,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,MAAMf,QAAQ,GAAG,MAAMf,GAAG,CAAC6B,IAAI,CAAC,cAAc,CAAC;IAC/C,OAAOd,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDM,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,MAAMhB,QAAQ,GAAG,MAAMf,GAAG,CAACgC,GAAG,CAAC,eAAe,CAAC;IAC/C,OAAOjB,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDQ,cAAc,EAAE,MAAOC,YAAY,IAAK;IACtC,MAAMnB,QAAQ,GAAG,MAAMf,GAAG,CAAC6B,IAAI,CAAC,uBAAuB,EAAEK,YAAY,CAAC;IACtE,OAAOnB,QAAQ,CAACU,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMU,QAAQ,GAAG;EACtBC,QAAQ,EAAE,MAAOC,SAAS,IAAK;IAC7B,MAAMtB,QAAQ,GAAG,MAAMf,GAAG,CAAC6B,IAAI,CAAC,kBAAkB,EAAEQ,SAAS,CAAC;IAC9D,OAAOtB,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDa,MAAM,EAAE,MAAAA,CAAOC,IAAI,GAAG,CAAC,EAAEC,QAAQ,GAAG,EAAE,EAAErB,MAAM,GAAG,IAAI,KAAK;IACxD,MAAMsB,MAAM,GAAG;MAAEF,IAAI;MAAEC;IAAS,CAAC;IACjC,IAAIrB,MAAM,EAAEsB,MAAM,CAACtB,MAAM,GAAGA,MAAM;IAElC,MAAMJ,QAAQ,GAAG,MAAMf,GAAG,CAACgC,GAAG,CAAC,SAAS,EAAE;MAAES;IAAO,CAAC,CAAC;IACrD,OAAO1B,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDiB,OAAO,EAAE,MAAOC,EAAE,IAAK;IACrB,MAAM5B,QAAQ,GAAG,MAAMf,GAAG,CAACgC,GAAG,CAAC,WAAWW,EAAE,EAAE,CAAC;IAC/C,OAAO5B,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDmB,MAAM,EAAE,MAAAA,CAAOD,EAAE,EAAEN,SAAS,KAAK;IAC/B,MAAMtB,QAAQ,GAAG,MAAMf,GAAG,CAAC6C,GAAG,CAAC,WAAWF,EAAE,EAAE,EAAEN,SAAS,CAAC;IAC1D,OAAOtB,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDqB,YAAY,EAAE,MAAAA,CAAOH,EAAE,EAAExB,MAAM,EAAE4B,QAAQ,GAAG,EAAE,KAAK;IACjD,MAAMhC,QAAQ,GAAG,MAAMf,GAAG,CAACgD,KAAK,CAAC,WAAWL,EAAE,SAAS,EAAE;MAAExB,MAAM;MAAE4B;IAAS,CAAC,CAAC;IAC9E,OAAOhC,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDwB,MAAM,EAAE,MAAON,EAAE,IAAK;IACpB,MAAM5B,QAAQ,GAAG,MAAMf,GAAG,CAACiD,MAAM,CAAC,WAAWN,EAAE,EAAE,CAAC;IAClD,OAAO5B,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDyB,gBAAgB,EAAE,MAAOP,EAAE,IAAK;IAC9B,MAAM5B,QAAQ,GAAG,MAAMf,GAAG,CAACgC,GAAG,CAAC,WAAWW,EAAE,iBAAiB,CAAC;IAC9D,OAAO5B,QAAQ,CAACU,IAAI;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAM0B,WAAW,GAAG;EACzBC,MAAM,EAAE,MAAAA,CAAOC,OAAO,EAAEC,IAAI,EAAEC,YAAY,KAAK;IAC7C,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEJ,IAAI,CAAC;IAC7BE,QAAQ,CAACE,MAAM,CAAC,cAAc,EAAEH,YAAY,CAAC;IAE7C,MAAMxC,QAAQ,GAAG,MAAMf,GAAG,CAAC6B,IAAI,CAAC,WAAWwB,OAAO,YAAY,EAAEG,QAAQ,EAAE;MACxErD,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOY,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDkC,UAAU,EAAE,MAAON,OAAO,IAAK;IAC7B,MAAMtC,QAAQ,GAAG,MAAMf,GAAG,CAACgC,GAAG,CAAC,WAAWqB,OAAO,YAAY,CAAC;IAC9D,OAAOtC,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDmC,QAAQ,EAAE,MAAOC,UAAU,IAAK;IAC9B,MAAM9C,QAAQ,GAAG,MAAMf,GAAG,CAACgC,GAAG,CAAC,cAAc6B,UAAU,WAAW,EAAE;MAClEC,YAAY,EAAE;IAChB,CAAC,CAAC;IACF,OAAO/C,QAAQ,CAACU,IAAI;EACtB,CAAC;EAEDqB,YAAY,EAAE,MAAAA,CAAOe,UAAU,EAAE1C,MAAM,EAAE4B,QAAQ,GAAG,EAAE,KAAK;IACzD,MAAMhC,QAAQ,GAAG,MAAMf,GAAG,CAACgD,KAAK,CAAC,cAAca,UAAU,SAAS,EAAE;MAAE1C,MAAM;MAAE4B;IAAS,CAAC,CAAC;IACzF,OAAOhC,QAAQ,CAACU,IAAI;EACtB;AACF,CAAC;AAED,eAAezB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}