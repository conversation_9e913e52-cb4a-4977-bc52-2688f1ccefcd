{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2v10\",\n  key: \"mnfbl\"\n}], [\"path\", {\n  d: \"m8.5 4 7 4\",\n  key: \"m1xjk3\"\n}], [\"path\", {\n  d: \"m8.5 8 7-4\",\n  key: \"t0m5j6\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"17\",\n  r: \"5\",\n  key: \"qbz8iq\"\n}]];\nconst NonBinary = createLucideIcon(\"non-binary\", __iconNode);\nexport { __iconNode, NonBinary as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "NonBinary", "createLucideIcon"], "sources": ["D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\node_modules\\lucide-react\\src\\icons\\non-binary.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 2v10', key: 'mnfbl' }],\n  ['path', { d: 'm8.5 4 7 4', key: 'm1xjk3' }],\n  ['path', { d: 'm8.5 8 7-4', key: 't0m5j6' }],\n  ['circle', { cx: '12', cy: '17', r: '5', key: 'qbz8iq' }],\n];\n\n/**\n * @component @name NonBinary\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMnYxMCIgLz4KICA8cGF0aCBkPSJtOC41IDQgNyA0IiAvPgogIDxwYXRoIGQ9Im04LjUgOCA3LTQiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxNyIgcj0iNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/non-binary\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst NonBinary = createLucideIcon('non-binary', __iconNode);\n\nexport default NonBinary;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAS,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GAC1D;AAaM,MAAAI,SAAA,GAAYC,gBAAiB,eAAcP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}