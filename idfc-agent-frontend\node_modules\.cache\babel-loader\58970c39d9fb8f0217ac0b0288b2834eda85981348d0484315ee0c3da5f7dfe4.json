{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\IDFCAgentOnboardingAndManagementSolution\\\\idfc-agent-frontend\\\\src\\\\pages\\\\AgentList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { agentAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { toast } from 'react-toastify';\nimport { Search, Filter, Eye, CheckCircle, XCircle, Clock, AlertCircle, ChevronLeft, ChevronRight, Download, RefreshCw } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AgentList = () => {\n  _s();\n  const {\n    isAdmin,\n    isReviewer\n  } = useAuth();\n  const [agents, setAgents] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [pageSize] = useState(10);\n  const [sortBy, setSortBy] = useState('createdAt');\n  const [sortOrder, setSortOrder] = useState('desc');\n  useEffect(() => {\n    fetchAgents();\n  }, [currentPage, statusFilter, sortBy, sortOrder]);\n  const fetchAgents = async () => {\n    setLoading(true);\n    try {\n      const response = await agentAPI.getAll(currentPage, pageSize, {\n        search: searchTerm,\n        status: statusFilter,\n        sortBy,\n        sortOrder\n      });\n      setAgents(response.data || []);\n      setTotalPages(Math.ceil((response.total || 0) / pageSize));\n    } catch (error) {\n      console.error('Error fetching agents:', error);\n      toast.error('Failed to fetch agents');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearch = () => {\n    setCurrentPage(1);\n    fetchAgents();\n  };\n  const handleStatusChange = async (agentId, newStatus, comments = '') => {\n    try {\n      await agentAPI.updateStatus(agentId, {\n        status: newStatus,\n        comments\n      });\n      toast.success(`Agent status updated to ${newStatus}`);\n      fetchAgents(); // Refresh the list\n    } catch (error) {\n      console.error('Error updating status:', error);\n      toast.error('Failed to update agent status');\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'Approved':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"h-5 w-5 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 16\n        }, this);\n      case 'Pending':\n        return /*#__PURE__*/_jsxDEV(Clock, {\n          className: \"h-5 w-5 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 16\n        }, this);\n      case 'Rejected':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"h-5 w-5 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 16\n        }, this);\n      case 'UnderReview':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"h-5 w-5 text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"h-5 w-5 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Approved':\n        return 'bg-green-100 text-green-800';\n      case 'Pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'Rejected':\n        return 'bg-red-100 text-red-800';\n      case 'UnderReview':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const handleSort = field => {\n    if (sortBy === field) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortBy(field);\n      setSortOrder('asc');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Agent Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-600\",\n          children: \"View and manage all agent applications and their status.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchAgents,\n          className: \"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), \"Refresh\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 gap-4 sm:grid-cols-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sm:col-span-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Search Agents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              onKeyPress: e => e.key === 'Enter' && handleSearch(),\n              className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n              placeholder: \"Search by name, email, phone, or ID...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Search, {\n              className: \"absolute left-3 top-2.5 h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Status Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: statusFilter,\n            onChange: e => setStatusFilter(e.target.value),\n            className: \"block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Pending\",\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"UnderReview\",\n              children: \"Under Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Approved\",\n              children: \"Approved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Rejected\",\n              children: \"Rejected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Active\",\n              children: \"Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Inactive\",\n              children: \"Inactive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Suspended\",\n              children: \"Suspended\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-end\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(Filter, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), \"Apply Filters\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: [\"Agents (\", agents.length, \" of \", totalPages * pageSize, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                onClick: () => handleSort('agentId'),\n                children: \"Agent ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                onClick: () => handleSort('firstName'),\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                onClick: () => handleSort('email'),\n                children: \"Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                onClick: () => handleSort('status'),\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                onClick: () => handleSort('createdAt'),\n                children: \"Applied Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: agents.map(agent => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                children: [\"#\", agent.agentId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: [agent.firstName, \" \", agent.lastName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"PAN: \", agent.panNumber]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-900\",\n                    children: agent.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: agent.phoneNumber\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [getStatusIcon(agent.status), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`,\n                    children: agent.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: new Date(agent.createdAt).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: `/agents/${agent.agentId}`,\n                    className: \"text-blue-600 hover:text-blue-900 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 25\n                    }, this), \"View\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 23\n                  }, this), (isAdmin() || isReviewer()) && agent.status === 'Pending' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleStatusChange(agent.agentId, 'Approved'),\n                      className: \"text-green-600 hover:text-green-900 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 29\n                      }, this), \"Approve\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleStatusChange(agent.agentId, 'Rejected'),\n                      className: \"text-red-600 hover:text-red-900 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(XCircle, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 298,\n                        columnNumber: 29\n                      }, this), \"Reject\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this)]\n            }, agent.agentId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex justify-between sm:hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setCurrentPage(Math.max(currentPage - 1, 1)),\n            disabled: currentPage === 1,\n            className: \"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setCurrentPage(Math.min(currentPage + 1, totalPages)),\n            disabled: currentPage === totalPages,\n            className: \"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-700\",\n              children: [\"Showing\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: (currentPage - 1) * pageSize + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this), ' ', \"to\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: Math.min(currentPage * pageSize, totalPages * pageSize)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), ' ', \"of\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: totalPages * pageSize\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 19\n              }, this), ' ', \"results\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",\n              \"aria-label\": \"Pagination\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCurrentPage(Math.max(currentPage - 1, 1)),\n                disabled: currentPage === 1,\n                className: \"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this), [...Array(totalPages)].map((_, index) => {\n                const page = index + 1;\n                if (page === currentPage || page === 1 || page === totalPages || page >= currentPage - 1 && page <= currentPage + 1) {\n                  return /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setCurrentPage(page),\n                    className: `relative inline-flex items-center px-4 py-2 border text-sm font-medium ${page === currentPage ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}`,\n                    children: page\n                  }, page, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 25\n                  }, this);\n                } else if (page === currentPage - 2 || page === currentPage + 2) {\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700\",\n                    children: \"...\"\n                  }, page, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 25\n                  }, this);\n                }\n                return null;\n              }), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCurrentPage(Math.min(currentPage + 1, totalPages)),\n                disabled: currentPage === totalPages,\n                className: \"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: /*#__PURE__*/_jsxDEV(ChevronRight, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), agents.length === 0 && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900\",\n          children: \"No agents found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: searchTerm || statusFilter ? 'Try adjusting your search criteria or filters.' : 'No agent applications have been submitted yet.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentList, \"IIk5/NaMB0JtpZSKMDlZmmg0+Sw=\", false, function () {\n  return [useAuth];\n});\n_c = AgentList;\nexport default AgentList;\nvar _c;\n$RefreshReg$(_c, \"AgentList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "agentAPI", "useAuth", "toast", "Search", "Filter", "Eye", "CheckCircle", "XCircle", "Clock", "AlertCircle", "ChevronLeft", "ChevronRight", "Download", "RefreshCw", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AgentList", "_s", "isAdmin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "agents", "setAgents", "loading", "setLoading", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "pageSize", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "fetchAgents", "response", "getAll", "search", "status", "data", "Math", "ceil", "total", "error", "console", "handleSearch", "handleStatusChange", "agentId", "newStatus", "comments", "updateStatus", "success", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "handleSort", "field", "children", "onClick", "type", "value", "onChange", "e", "target", "onKeyPress", "key", "placeholder", "length", "map", "agent", "firstName", "lastName", "panNumber", "email", "phoneNumber", "Date", "createdAt", "toLocaleDateString", "to", "max", "disabled", "min", "Array", "_", "index", "page", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/IDFCAgentOnboardingAndManagementSolution/idfc-agent-frontend/src/pages/AgentList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { agentAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { toast } from 'react-toastify';\nimport {\n  Search,\n  Filter,\n  Eye,\n  CheckCircle,\n  XCircle,\n  Clock,\n  AlertCircle,\n  ChevronLeft,\n  ChevronRight,\n  Download,\n  RefreshCw\n} from 'lucide-react';\n\nconst AgentList = () => {\n  const { isAdmin, isReviewer } = useAuth();\n  const [agents, setAgents] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [pageSize] = useState(10);\n  const [sortBy, setSortBy] = useState('createdAt');\n  const [sortOrder, setSortOrder] = useState('desc');\n\n  useEffect(() => {\n    fetchAgents();\n  }, [currentPage, statusFilter, sortBy, sortOrder]);\n\n  const fetchAgents = async () => {\n    setLoading(true);\n    try {\n      const response = await agentAPI.getAll(currentPage, pageSize, {\n        search: searchTerm,\n        status: statusFilter,\n        sortBy,\n        sortOrder\n      });\n\n      setAgents(response.data || []);\n      setTotalPages(Math.ceil((response.total || 0) / pageSize));\n    } catch (error) {\n      console.error('Error fetching agents:', error);\n      toast.error('Failed to fetch agents');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = () => {\n    setCurrentPage(1);\n    fetchAgents();\n  };\n\n  const handleStatusChange = async (agentId, newStatus, comments = '') => {\n    try {\n      await agentAPI.updateStatus(agentId, { status: newStatus, comments });\n      toast.success(`Agent status updated to ${newStatus}`);\n      fetchAgents(); // Refresh the list\n    } catch (error) {\n      console.error('Error updating status:', error);\n      toast.error('Failed to update agent status');\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'Approved':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />;\n      case 'Pending':\n        return <Clock className=\"h-5 w-5 text-yellow-500\" />;\n      case 'Rejected':\n        return <XCircle className=\"h-5 w-5 text-red-500\" />;\n      case 'UnderReview':\n        return <AlertCircle className=\"h-5 w-5 text-blue-500\" />;\n      default:\n        return <AlertCircle className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Approved':\n        return 'bg-green-100 text-green-800';\n      case 'Pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'Rejected':\n        return 'bg-red-100 text-red-800';\n      case 'UnderReview':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const handleSort = (field) => {\n    if (sortBy === field) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortBy(field);\n      setSortOrder('asc');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Agent Management</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            View and manage all agent applications and their status.\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={fetchAgents}\n            className=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n          >\n            <RefreshCw className=\"h-4 w-4 mr-2\" />\n            Refresh\n          </button>\n          <button className=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\">\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export\n          </button>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n          <div className=\"sm:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Search Agents\n            </label>\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Search by name, email, phone, or ID...\"\n              />\n              <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Status Filter\n            </label>\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"\">All Status</option>\n              <option value=\"Pending\">Pending</option>\n              <option value=\"UnderReview\">Under Review</option>\n              <option value=\"Approved\">Approved</option>\n              <option value=\"Rejected\">Rejected</option>\n              <option value=\"Active\">Active</option>\n              <option value=\"Inactive\">Inactive</option>\n              <option value=\"Suspended\">Suspended</option>\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button\n              onClick={handleSearch}\n              className=\"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <Filter className=\"h-4 w-4 mr-2\" />\n              Apply Filters\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Agents Table */}\n      <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">\n            Agents ({agents.length} of {totalPages * pageSize})\n          </h3>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('agentId')}\n                >\n                  Agent ID\n                </th>\n                <th\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('firstName')}\n                >\n                  Name\n                </th>\n                <th\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('email')}\n                >\n                  Contact\n                </th>\n                <th\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('status')}\n                >\n                  Status\n                </th>\n                <th\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('createdAt')}\n                >\n                  Applied Date\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {agents.map((agent) => (\n                <tr key={agent.agentId} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                    #{agent.agentId}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {agent.firstName} {agent.lastName}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        PAN: {agent.panNumber}\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm text-gray-900\">{agent.email}</div>\n                      <div className=\"text-sm text-gray-500\">{agent.phoneNumber}</div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      {getStatusIcon(agent.status)}\n                      <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>\n                        {agent.status}\n                      </span>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {new Date(agent.createdAt).toLocaleDateString()}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <Link\n                        to={`/agents/${agent.agentId}`}\n                        className=\"text-blue-600 hover:text-blue-900 flex items-center\"\n                      >\n                        <Eye className=\"h-4 w-4 mr-1\" />\n                        View\n                      </Link>\n\n                      {(isAdmin() || isReviewer()) && agent.status === 'Pending' && (\n                        <>\n                          <button\n                            onClick={() => handleStatusChange(agent.agentId, 'Approved')}\n                            className=\"text-green-600 hover:text-green-900 flex items-center\"\n                          >\n                            <CheckCircle className=\"h-4 w-4 mr-1\" />\n                            Approve\n                          </button>\n                          <button\n                            onClick={() => handleStatusChange(agent.agentId, 'Rejected')}\n                            className=\"text-red-600 hover:text-red-900 flex items-center\"\n                          >\n                            <XCircle className=\"h-4 w-4 mr-1\" />\n                            Reject\n                          </button>\n                        </>\n                      )}\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {/* Pagination */}\n        {totalPages > 1 && (\n          <div className=\"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\">\n            <div className=\"flex-1 flex justify-between sm:hidden\">\n              <button\n                onClick={() => setCurrentPage(Math.max(currentPage - 1, 1))}\n                disabled={currentPage === 1}\n                className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Previous\n              </button>\n              <button\n                onClick={() => setCurrentPage(Math.min(currentPage + 1, totalPages))}\n                disabled={currentPage === totalPages}\n                className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Next\n              </button>\n            </div>\n            <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-700\">\n                  Showing{' '}\n                  <span className=\"font-medium\">{(currentPage - 1) * pageSize + 1}</span>\n                  {' '}to{' '}\n                  <span className=\"font-medium\">\n                    {Math.min(currentPage * pageSize, totalPages * pageSize)}\n                  </span>\n                  {' '}of{' '}\n                  <span className=\"font-medium\">{totalPages * pageSize}</span>\n                  {' '}results\n                </p>\n              </div>\n              <div>\n                <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">\n                  <button\n                    onClick={() => setCurrentPage(Math.max(currentPage - 1, 1))}\n                    disabled={currentPage === 1}\n                    className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    <ChevronLeft className=\"h-5 w-5\" />\n                  </button>\n\n                  {[...Array(totalPages)].map((_, index) => {\n                    const page = index + 1;\n                    if (page === currentPage || page === 1 || page === totalPages || (page >= currentPage - 1 && page <= currentPage + 1)) {\n                      return (\n                        <button\n                          key={page}\n                          onClick={() => setCurrentPage(page)}\n                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${\n                            page === currentPage\n                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'\n                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'\n                          }`}\n                        >\n                          {page}\n                        </button>\n                      );\n                    } else if (page === currentPage - 2 || page === currentPage + 2) {\n                      return (\n                        <span key={page} className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700\">\n                          ...\n                        </span>\n                      );\n                    }\n                    return null;\n                  })}\n\n                  <button\n                    onClick={() => setCurrentPage(Math.min(currentPage + 1, totalPages))}\n                    disabled={currentPage === totalPages}\n                    className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    <ChevronRight className=\"h-5 w-5\" />\n                  </button>\n                </nav>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Empty State */}\n      {agents.length === 0 && !loading && (\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <div className=\"text-center py-12\">\n            <AlertCircle className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No agents found</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchTerm || statusFilter\n                ? 'Try adjusting your search criteria or filters.'\n                : 'No agent applications have been submitted yet.'\n              }\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AgentList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,MAAM,EACNC,MAAM,EACNC,GAAG,EACHC,WAAW,EACXC,OAAO,EACPC,KAAK,EACLC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,QAAQ,EACRC,SAAS,QACJ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAGpB,OAAO,CAAC,CAAC;EACzC,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACqC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC/B,MAAM,CAACsC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAC,WAAW,CAAC;EACjD,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,MAAM,CAAC;EAElDC,SAAS,CAAC,MAAM;IACdyC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACT,WAAW,EAAEF,YAAY,EAAEO,MAAM,EAAEE,SAAS,CAAC,CAAC;EAElD,MAAME,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9Bd,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAMxC,QAAQ,CAACyC,MAAM,CAACX,WAAW,EAAEI,QAAQ,EAAE;QAC5DQ,MAAM,EAAEhB,UAAU;QAClBiB,MAAM,EAAEf,YAAY;QACpBO,MAAM;QACNE;MACF,CAAC,CAAC;MAEFd,SAAS,CAACiB,QAAQ,CAACI,IAAI,IAAI,EAAE,CAAC;MAC9BX,aAAa,CAACY,IAAI,CAACC,IAAI,CAAC,CAACN,QAAQ,CAACO,KAAK,IAAI,CAAC,IAAIb,QAAQ,CAAC,CAAC;IAC5D,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C9C,KAAK,CAAC8C,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,YAAY,GAAGA,CAAA,KAAM;IACzBnB,cAAc,CAAC,CAAC,CAAC;IACjBQ,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAMY,kBAAkB,GAAG,MAAAA,CAAOC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,GAAG,EAAE,KAAK;IACtE,IAAI;MACF,MAAMtD,QAAQ,CAACuD,YAAY,CAACH,OAAO,EAAE;QAAET,MAAM,EAAEU,SAAS;QAAEC;MAAS,CAAC,CAAC;MACrEpD,KAAK,CAACsD,OAAO,CAAC,2BAA2BH,SAAS,EAAE,CAAC;MACrDd,WAAW,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C9C,KAAK,CAAC8C,KAAK,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMS,aAAa,GAAId,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,oBAAO5B,OAAA,CAACT,WAAW;UAACoD,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,SAAS;QACZ,oBAAO/C,OAAA,CAACP,KAAK;UAACkD,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,UAAU;QACb,oBAAO/C,OAAA,CAACR,OAAO;UAACmD,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD,KAAK,aAAa;QAChB,oBAAO/C,OAAA,CAACN,WAAW;UAACiD,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1D;QACE,oBAAO/C,OAAA,CAACN,WAAW;UAACiD,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC5D;EACF,CAAC;EAED,MAAMC,cAAc,GAAIpB,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,6BAA6B;MACtC,KAAK,SAAS;QACZ,OAAO,+BAA+B;MACxC,KAAK,UAAU;QACb,OAAO,yBAAyB;MAClC,KAAK,aAAa;QAChB,OAAO,2BAA2B;MACpC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMqB,UAAU,GAAIC,KAAK,IAAK;IAC5B,IAAI9B,MAAM,KAAK8B,KAAK,EAAE;MACpB3B,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IACpD,CAAC,MAAM;MACLD,SAAS,CAAC6B,KAAK,CAAC;MAChB3B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,IAAId,OAAO,EAAE;IACX,oBACET,OAAA;MAAK2C,SAAS,EAAC,uCAAuC;MAAAQ,QAAA,eACpDnD,OAAA;QAAK2C,SAAS,EAAC;MAAgE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACE/C,OAAA;IAAK2C,SAAS,EAAC,WAAW;IAAAQ,QAAA,gBACxBnD,OAAA;MAAK2C,SAAS,EAAC,mCAAmC;MAAAQ,QAAA,gBAChDnD,OAAA;QAAAmD,QAAA,gBACEnD,OAAA;UAAI2C,SAAS,EAAC,kCAAkC;UAAAQ,QAAA,EAAC;QAAgB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtE/C,OAAA;UAAG2C,SAAS,EAAC,4BAA4B;UAAAQ,QAAA,EAAC;QAE1C;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN/C,OAAA;QAAK2C,SAAS,EAAC,gBAAgB;QAAAQ,QAAA,gBAC7BnD,OAAA;UACEoD,OAAO,EAAE5B,WAAY;UACrBmB,SAAS,EAAC,4IAA4I;UAAAQ,QAAA,gBAEtJnD,OAAA,CAACF,SAAS;YAAC6C,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/C,OAAA;UAAQ2C,SAAS,EAAC,4IAA4I;UAAAQ,QAAA,gBAC5JnD,OAAA,CAACH,QAAQ;YAAC8C,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA;MAAK2C,SAAS,EAAC,gCAAgC;MAAAQ,QAAA,eAC7CnD,OAAA;QAAK2C,SAAS,EAAC,uCAAuC;QAAAQ,QAAA,gBACpDnD,OAAA;UAAK2C,SAAS,EAAC,eAAe;UAAAQ,QAAA,gBAC5BnD,OAAA;YAAO2C,SAAS,EAAC,8CAA8C;YAAAQ,QAAA,EAAC;UAEhE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/C,OAAA;YAAK2C,SAAS,EAAC,UAAU;YAAAQ,QAAA,gBACvBnD,OAAA;cACEqD,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE3C,UAAW;cAClB4C,QAAQ,EAAGC,CAAC,IAAK5C,aAAa,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CI,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAIxB,YAAY,CAAC,CAAE;cACvDQ,SAAS,EAAC,6MAA6M;cACvNiB,WAAW,EAAC;YAAwC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACF/C,OAAA,CAACZ,MAAM;cAACuD,SAAS,EAAC;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA;UAAAmD,QAAA,gBACEnD,OAAA;YAAO2C,SAAS,EAAC,8CAA8C;YAAAQ,QAAA,EAAC;UAEhE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR/C,OAAA;YACEsD,KAAK,EAAEzC,YAAa;YACpB0C,QAAQ,EAAGC,CAAC,IAAK1C,eAAe,CAAC0C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACjDX,SAAS,EAAC,iIAAiI;YAAAQ,QAAA,gBAE3InD,OAAA;cAAQsD,KAAK,EAAC,EAAE;cAAAH,QAAA,EAAC;YAAU;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpC/C,OAAA;cAAQsD,KAAK,EAAC,SAAS;cAAAH,QAAA,EAAC;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC/C,OAAA;cAAQsD,KAAK,EAAC,aAAa;cAAAH,QAAA,EAAC;YAAY;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjD/C,OAAA;cAAQsD,KAAK,EAAC,UAAU;cAAAH,QAAA,EAAC;YAAQ;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C/C,OAAA;cAAQsD,KAAK,EAAC,UAAU;cAAAH,QAAA,EAAC;YAAQ;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C/C,OAAA;cAAQsD,KAAK,EAAC,QAAQ;cAAAH,QAAA,EAAC;YAAM;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC/C,OAAA;cAAQsD,KAAK,EAAC,UAAU;cAAAH,QAAA,EAAC;YAAQ;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C/C,OAAA;cAAQsD,KAAK,EAAC,WAAW;cAAAH,QAAA,EAAC;YAAS;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN/C,OAAA;UAAK2C,SAAS,EAAC,gBAAgB;UAAAQ,QAAA,eAC7BnD,OAAA;YACEoD,OAAO,EAAEjB,YAAa;YACtBQ,SAAS,EAAC,oOAAoO;YAAAQ,QAAA,gBAE9OnD,OAAA,CAACX,MAAM;cAACsD,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/C,OAAA;MAAK2C,SAAS,EAAC,4CAA4C;MAAAQ,QAAA,gBACzDnD,OAAA;QAAK2C,SAAS,EAAC,oCAAoC;QAAAQ,QAAA,eACjDnD,OAAA;UAAI2C,SAAS,EAAC,mCAAmC;UAAAQ,QAAA,GAAC,UACxC,EAAC5C,MAAM,CAACsD,MAAM,EAAC,MAAI,EAAC5C,UAAU,GAAGE,QAAQ,EAAC,GACpD;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN/C,OAAA;QAAK2C,SAAS,EAAC,iBAAiB;QAAAQ,QAAA,eAC9BnD,OAAA;UAAO2C,SAAS,EAAC,qCAAqC;UAAAQ,QAAA,gBACpDnD,OAAA;YAAO2C,SAAS,EAAC,YAAY;YAAAQ,QAAA,eAC3BnD,OAAA;cAAAmD,QAAA,gBACEnD,OAAA;gBACE2C,SAAS,EAAC,iHAAiH;gBAC3HS,OAAO,EAAEA,CAAA,KAAMH,UAAU,CAAC,SAAS,CAAE;gBAAAE,QAAA,EACtC;cAED;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/C,OAAA;gBACE2C,SAAS,EAAC,iHAAiH;gBAC3HS,OAAO,EAAEA,CAAA,KAAMH,UAAU,CAAC,WAAW,CAAE;gBAAAE,QAAA,EACxC;cAED;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/C,OAAA;gBACE2C,SAAS,EAAC,iHAAiH;gBAC3HS,OAAO,EAAEA,CAAA,KAAMH,UAAU,CAAC,OAAO,CAAE;gBAAAE,QAAA,EACpC;cAED;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/C,OAAA;gBACE2C,SAAS,EAAC,iHAAiH;gBAC3HS,OAAO,EAAEA,CAAA,KAAMH,UAAU,CAAC,QAAQ,CAAE;gBAAAE,QAAA,EACrC;cAED;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/C,OAAA;gBACE2C,SAAS,EAAC,iHAAiH;gBAC3HS,OAAO,EAAEA,CAAA,KAAMH,UAAU,CAAC,WAAW,CAAE;gBAAAE,QAAA,EACxC;cAED;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL/C,OAAA;gBAAI2C,SAAS,EAAC,gFAAgF;gBAAAQ,QAAA,EAAC;cAE/F;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR/C,OAAA;YAAO2C,SAAS,EAAC,mCAAmC;YAAAQ,QAAA,EACjD5C,MAAM,CAACuD,GAAG,CAAEC,KAAK,iBAChB/D,OAAA;cAAwB2C,SAAS,EAAC,kBAAkB;cAAAQ,QAAA,gBAClDnD,OAAA;gBAAI2C,SAAS,EAAC,+DAA+D;gBAAAQ,QAAA,GAAC,GAC3E,EAACY,KAAK,CAAC1B,OAAO;cAAA;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACL/C,OAAA;gBAAI2C,SAAS,EAAC,6BAA6B;gBAAAQ,QAAA,eACzCnD,OAAA;kBAAAmD,QAAA,gBACEnD,OAAA;oBAAK2C,SAAS,EAAC,mCAAmC;oBAAAQ,QAAA,GAC/CY,KAAK,CAACC,SAAS,EAAC,GAAC,EAACD,KAAK,CAACE,QAAQ;kBAAA;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACN/C,OAAA;oBAAK2C,SAAS,EAAC,uBAAuB;oBAAAQ,QAAA,GAAC,OAChC,EAACY,KAAK,CAACG,SAAS;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL/C,OAAA;gBAAI2C,SAAS,EAAC,6BAA6B;gBAAAQ,QAAA,eACzCnD,OAAA;kBAAAmD,QAAA,gBACEnD,OAAA;oBAAK2C,SAAS,EAAC,uBAAuB;oBAAAQ,QAAA,EAAEY,KAAK,CAACI;kBAAK;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1D/C,OAAA;oBAAK2C,SAAS,EAAC,uBAAuB;oBAAAQ,QAAA,EAAEY,KAAK,CAACK;kBAAW;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL/C,OAAA;gBAAI2C,SAAS,EAAC,6BAA6B;gBAAAQ,QAAA,eACzCnD,OAAA;kBAAK2C,SAAS,EAAC,mBAAmB;kBAAAQ,QAAA,GAC/BT,aAAa,CAACqB,KAAK,CAACnC,MAAM,CAAC,eAC5B5B,OAAA;oBAAM2C,SAAS,EAAE,gFAAgFK,cAAc,CAACe,KAAK,CAACnC,MAAM,CAAC,EAAG;oBAAAuB,QAAA,EAC7HY,KAAK,CAACnC;kBAAM;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL/C,OAAA;gBAAI2C,SAAS,EAAC,mDAAmD;gBAAAQ,QAAA,EAC9D,IAAIkB,IAAI,CAACN,KAAK,CAACO,SAAS,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACL/C,OAAA;gBAAI2C,SAAS,EAAC,iDAAiD;gBAAAQ,QAAA,eAC7DnD,OAAA;kBAAK2C,SAAS,EAAC,gBAAgB;kBAAAQ,QAAA,gBAC7BnD,OAAA,CAAChB,IAAI;oBACHwF,EAAE,EAAE,WAAWT,KAAK,CAAC1B,OAAO,EAAG;oBAC/BM,SAAS,EAAC,qDAAqD;oBAAAQ,QAAA,gBAE/DnD,OAAA,CAACV,GAAG;sBAACqD,SAAS,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAElC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAEN,CAAC1C,OAAO,CAAC,CAAC,IAAIC,UAAU,CAAC,CAAC,KAAKyD,KAAK,CAACnC,MAAM,KAAK,SAAS,iBACxD5B,OAAA,CAAAE,SAAA;oBAAAiD,QAAA,gBACEnD,OAAA;sBACEoD,OAAO,EAAEA,CAAA,KAAMhB,kBAAkB,CAAC2B,KAAK,CAAC1B,OAAO,EAAE,UAAU,CAAE;sBAC7DM,SAAS,EAAC,uDAAuD;sBAAAQ,QAAA,gBAEjEnD,OAAA,CAACT,WAAW;wBAACoD,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,WAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT/C,OAAA;sBACEoD,OAAO,EAAEA,CAAA,KAAMhB,kBAAkB,CAAC2B,KAAK,CAAC1B,OAAO,EAAE,UAAU,CAAE;sBAC7DM,SAAS,EAAC,mDAAmD;sBAAAQ,QAAA,gBAE7DnD,OAAA,CAACR,OAAO;wBAACmD,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEtC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACT,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA5DEgB,KAAK,CAAC1B,OAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6DlB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGL9B,UAAU,GAAG,CAAC,iBACbjB,OAAA;QAAK2C,SAAS,EAAC,uFAAuF;QAAAQ,QAAA,gBACpGnD,OAAA;UAAK2C,SAAS,EAAC,uCAAuC;UAAAQ,QAAA,gBACpDnD,OAAA;YACEoD,OAAO,EAAEA,CAAA,KAAMpC,cAAc,CAACc,IAAI,CAAC2C,GAAG,CAAC1D,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAE;YAC5D2D,QAAQ,EAAE3D,WAAW,KAAK,CAAE;YAC5B4B,SAAS,EAAC,2LAA2L;YAAAQ,QAAA,EACtM;UAED;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/C,OAAA;YACEoD,OAAO,EAAEA,CAAA,KAAMpC,cAAc,CAACc,IAAI,CAAC6C,GAAG,CAAC5D,WAAW,GAAG,CAAC,EAAEE,UAAU,CAAC,CAAE;YACrEyD,QAAQ,EAAE3D,WAAW,KAAKE,UAAW;YACrC0B,SAAS,EAAC,gMAAgM;YAAAQ,QAAA,EAC3M;UAED;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN/C,OAAA;UAAK2C,SAAS,EAAC,6DAA6D;UAAAQ,QAAA,gBAC1EnD,OAAA;YAAAmD,QAAA,eACEnD,OAAA;cAAG2C,SAAS,EAAC,uBAAuB;cAAAQ,QAAA,GAAC,SAC5B,EAAC,GAAG,eACXnD,OAAA;gBAAM2C,SAAS,EAAC,aAAa;gBAAAQ,QAAA,EAAE,CAACpC,WAAW,GAAG,CAAC,IAAII,QAAQ,GAAG;cAAC;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACtE,GAAG,EAAC,IAAE,EAAC,GAAG,eACX/C,OAAA;gBAAM2C,SAAS,EAAC,aAAa;gBAAAQ,QAAA,EAC1BrB,IAAI,CAAC6C,GAAG,CAAC5D,WAAW,GAAGI,QAAQ,EAAEF,UAAU,GAAGE,QAAQ;cAAC;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,EACN,GAAG,EAAC,IAAE,EAAC,GAAG,eACX/C,OAAA;gBAAM2C,SAAS,EAAC,aAAa;gBAAAQ,QAAA,EAAElC,UAAU,GAAGE;cAAQ;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC3D,GAAG,EAAC,SACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN/C,OAAA;YAAAmD,QAAA,eACEnD,OAAA;cAAK2C,SAAS,EAAC,2DAA2D;cAAC,cAAW,YAAY;cAAAQ,QAAA,gBAChGnD,OAAA;gBACEoD,OAAO,EAAEA,CAAA,KAAMpC,cAAc,CAACc,IAAI,CAAC2C,GAAG,CAAC1D,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAE;gBAC5D2D,QAAQ,EAAE3D,WAAW,KAAK,CAAE;gBAC5B4B,SAAS,EAAC,6LAA6L;gBAAAQ,QAAA,eAEvMnD,OAAA,CAACL,WAAW;kBAACgD,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,EAER,CAAC,GAAG6B,KAAK,CAAC3D,UAAU,CAAC,CAAC,CAAC6C,GAAG,CAAC,CAACe,CAAC,EAAEC,KAAK,KAAK;gBACxC,MAAMC,IAAI,GAAGD,KAAK,GAAG,CAAC;gBACtB,IAAIC,IAAI,KAAKhE,WAAW,IAAIgE,IAAI,KAAK,CAAC,IAAIA,IAAI,KAAK9D,UAAU,IAAK8D,IAAI,IAAIhE,WAAW,GAAG,CAAC,IAAIgE,IAAI,IAAIhE,WAAW,GAAG,CAAE,EAAE;kBACrH,oBACEf,OAAA;oBAEEoD,OAAO,EAAEA,CAAA,KAAMpC,cAAc,CAAC+D,IAAI,CAAE;oBACpCpC,SAAS,EAAE,0EACToC,IAAI,KAAKhE,WAAW,GAChB,+CAA+C,GAC/C,yDAAyD,EAC5D;oBAAAoC,QAAA,EAEF4B;kBAAI,GARAA,IAAI;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OASH,CAAC;gBAEb,CAAC,MAAM,IAAIgC,IAAI,KAAKhE,WAAW,GAAG,CAAC,IAAIgE,IAAI,KAAKhE,WAAW,GAAG,CAAC,EAAE;kBAC/D,oBACEf,OAAA;oBAAiB2C,SAAS,EAAC,+GAA+G;oBAAAQ,QAAA,EAAC;kBAE3I,GAFW4B,IAAI;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAET,CAAC;gBAEX;gBACA,OAAO,IAAI;cACb,CAAC,CAAC,eAEF/C,OAAA;gBACEoD,OAAO,EAAEA,CAAA,KAAMpC,cAAc,CAACc,IAAI,CAAC6C,GAAG,CAAC5D,WAAW,GAAG,CAAC,EAAEE,UAAU,CAAC,CAAE;gBACrEyD,QAAQ,EAAE3D,WAAW,KAAKE,UAAW;gBACrC0B,SAAS,EAAC,6LAA6L;gBAAAQ,QAAA,eAEvMnD,OAAA,CAACJ,YAAY;kBAAC+C,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLxC,MAAM,CAACsD,MAAM,KAAK,CAAC,IAAI,CAACpD,OAAO,iBAC9BT,OAAA;MAAK2C,SAAS,EAAC,gCAAgC;MAAAQ,QAAA,eAC7CnD,OAAA;QAAK2C,SAAS,EAAC,mBAAmB;QAAAQ,QAAA,gBAChCnD,OAAA,CAACN,WAAW;UAACiD,SAAS,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3D/C,OAAA;UAAI2C,SAAS,EAAC,wCAAwC;UAAAQ,QAAA,EAAC;QAAe;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E/C,OAAA;UAAG2C,SAAS,EAAC,4BAA4B;UAAAQ,QAAA,EACtCxC,UAAU,IAAIE,YAAY,GACvB,gDAAgD,GAChD;QAAgD;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAvYID,SAAS;EAAA,QACmBjB,OAAO;AAAA;AAAA8F,EAAA,GADnC7E,SAAS;AAyYf,eAAeA,SAAS;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}