Write-Host "Testing IDFC Agent API Login" -ForegroundColor Green

$apiUrl = "https://localhost:7001/api/auth/login"

# Test admin login
Write-Host "Testing admin login..." -ForegroundColor Yellow
$adminData = @{
    username = "admin"
    password = "Admin@123"
} | ConvertTo-Json

try {
    $adminResponse = Invoke-RestMethod -Uri $apiUrl -Method POST -Body $adminData -ContentType "application/json" -SkipCertificateCheck
    Write-Host "Admin login successful!" -ForegroundColor Green
    Write-Host "Token: $($adminResponse.token.Substring(0, 50))..." -ForegroundColor Cyan
} catch {
    Write-Host "Admin login failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test reviewer login
Write-Host "Testing reviewer login..." -ForegroundColor Yellow
$reviewerData = @{
    username = "reviewer"
    password = "Reviewer@123"
} | ConvertTo-<PERSON><PERSON>

try {
    $reviewerResponse = Invoke-RestMethod -Uri $apiUrl -Method POST -Body $reviewerData -ContentType "application/json" -SkipCertificateCheck
    Write-Host "Reviewer login successful!" -ForegroundColor Green
    Write-Host "Token: $($reviewerResponse.token.Substring(0, 50))..." -ForegroundColor Cyan
} catch {
    Write-Host "Reviewer login failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test wrong password
Write-Host "Testing wrong password..." -ForegroundColor Yellow
$wrongData = @{
    username = "admin"
    password = "WrongPassword"
} | ConvertTo-Json

try {
    $wrongResponse = Invoke-RestMethod -Uri $apiUrl -Method POST -Body $wrongData -ContentType "application/json" -SkipCertificateCheck
    Write-Host "Wrong password test failed - should have been rejected!" -ForegroundColor Red
} catch {
    Write-Host "Wrong password correctly rejected!" -ForegroundColor Green
}

Write-Host "Testing completed!" -ForegroundColor Green
