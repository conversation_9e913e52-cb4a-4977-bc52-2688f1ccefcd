using Microsoft.EntityFrameworkCore;
using IDFCAgentAPI.Data;
using IDFCAgentAPI.DTOs;
using IDFCAgentAPI.Models;

namespace IDFCAgentAPI.Services
{
    public class AgentService : IAgentService
    {
        private readonly ApplicationDbContext _context;
        private readonly IAuthService _authService;
        private readonly IWorkflowService _workflowService;

        public AgentService(ApplicationDbContext context, IAuthService authService, IWorkflowService workflowService)
        {
            _context = context;
            _authService = authService;
            _workflowService = workflowService;
        }

        public async Task<AgentResponseDto> RegisterAgentAsync(AgentRegistrationDto registrationDto)
        {
            // Check if email, Aadhar, PAN, or phone already exists
            if (await EmailExistsAsync(registrationDto.Email))
                throw new InvalidOperationException("Email already exists");

            if (await AadharExistsAsync(registrationDto.AadharNumber))
                throw new InvalidOperationException("Aadhar number already exists");

            if (await PanExistsAsync(registrationDto.PanNumber))
                throw new InvalidOperationException("PAN number already exists");

            if (await PhoneExistsAsync(registrationDto.PhoneNumber))
                throw new InvalidOperationException("Phone number already exists");

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Create Agent
                var agent = new Agent
                {
                    FirstName = registrationDto.FirstName,
                    LastName = registrationDto.LastName,
                    Email = registrationDto.Email,
                    PhoneNumber = registrationDto.PhoneNumber,
                    AadharNumber = registrationDto.AadharNumber,
                    PanNumber = registrationDto.PanNumber,
                    Address = registrationDto.Address,
                    City = registrationDto.City,
                    State = registrationDto.State,
                    PinCode = registrationDto.PinCode,
                    DateOfBirth = registrationDto.DateOfBirth,
                    Status = AgentStatus.Pending,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Agents.Add(agent);
                await _context.SaveChangesAsync();

                // Create User account
                var user = new User
                {
                    Username = registrationDto.Username,
                    Email = registrationDto.Email,
                    PasswordHash = _authService.HashPassword(registrationDto.Password),
                    Role = UserRole.Agent,
                    AgentId = agent.AgentId,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                // Create initial status history
                var statusHistory = new AgentStatusHistory
                {
                    AgentId = agent.AgentId,
                    FromStatus = AgentStatus.Pending,
                    ToStatus = AgentStatus.Pending,
                    Comments = "Agent registration completed",
                    ChangedBy = "System",
                    ChangedAt = DateTime.UtcNow
                };

                _context.AgentStatusHistories.Add(statusHistory);
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();

                return MapToResponseDto(agent);
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<AgentResponseDto?> GetAgentByIdAsync(int agentId)
        {
            var agent = await _context.Agents.FindAsync(agentId);
            return agent != null ? MapToResponseDto(agent) : null;
        }

        public async Task<AgentResponseDto?> GetAgentByEmailAsync(string email)
        {
            var agent = await _context.Agents.FirstOrDefaultAsync(a => a.Email == email);
            return agent != null ? MapToResponseDto(agent) : null;
        }

        public async Task<IEnumerable<AgentResponseDto>> GetAllAgentsAsync()
        {
            var agents = await _context.Agents.ToListAsync();
            return agents.Select(MapToResponseDto);
        }

        public async Task<IEnumerable<AgentResponseDto>> GetAgentsByStatusAsync(AgentStatus status)
        {
            var agents = await _context.Agents.Where(a => a.Status == status).ToListAsync();
            return agents.Select(MapToResponseDto);
        }

        public async Task<AgentResponseDto?> UpdateAgentAsync(int agentId, AgentUpdateDto updateDto)
        {
            var agent = await _context.Agents.FindAsync(agentId);
            if (agent == null) return null;

            // Update only provided fields
            if (!string.IsNullOrEmpty(updateDto.FirstName))
                agent.FirstName = updateDto.FirstName;

            if (!string.IsNullOrEmpty(updateDto.LastName))
                agent.LastName = updateDto.LastName;

            if (!string.IsNullOrEmpty(updateDto.Email) && updateDto.Email != agent.Email)
            {
                if (await EmailExistsAsync(updateDto.Email))
                    throw new InvalidOperationException("Email already exists");
                agent.Email = updateDto.Email;
            }

            if (!string.IsNullOrEmpty(updateDto.PhoneNumber) && updateDto.PhoneNumber != agent.PhoneNumber)
            {
                if (await PhoneExistsAsync(updateDto.PhoneNumber))
                    throw new InvalidOperationException("Phone number already exists");
                agent.PhoneNumber = updateDto.PhoneNumber;
            }

            if (!string.IsNullOrEmpty(updateDto.Address))
                agent.Address = updateDto.Address;

            if (!string.IsNullOrEmpty(updateDto.City))
                agent.City = updateDto.City;

            if (!string.IsNullOrEmpty(updateDto.State))
                agent.State = updateDto.State;

            if (!string.IsNullOrEmpty(updateDto.PinCode))
                agent.PinCode = updateDto.PinCode;

            agent.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return MapToResponseDto(agent);
        }

        public async Task<bool> UpdateAgentStatusAsync(int agentId, AgentStatus newStatus, string changedBy, string? comments = null)
        {
            // Use workflow service for status transitions with validation
            return await _workflowService.ProcessStatusTransitionAsync(agentId, newStatus, changedBy, comments);
        }

        public async Task<bool> DeleteAgentAsync(int agentId)
        {
            var agent = await _context.Agents.FindAsync(agentId);
            if (agent == null) return false;

            _context.Agents.Remove(agent);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<AgentStatusHistory>> GetAgentStatusHistoryAsync(int agentId)
        {
            return await _context.AgentStatusHistories
                .Where(h => h.AgentId == agentId)
                .OrderByDescending(h => h.ChangedAt)
                .ToListAsync();
        }

        public async Task<bool> AgentExistsAsync(int agentId)
        {
            return await _context.Agents.AnyAsync(a => a.AgentId == agentId);
        }

        public async Task<bool> EmailExistsAsync(string email)
        {
            return await _context.Agents.AnyAsync(a => a.Email == email);
        }

        public async Task<bool> AadharExistsAsync(string aadharNumber)
        {
            return await _context.Agents.AnyAsync(a => a.AadharNumber == aadharNumber);
        }

        public async Task<bool> PanExistsAsync(string panNumber)
        {
            return await _context.Agents.AnyAsync(a => a.PanNumber == panNumber);
        }

        public async Task<bool> PhoneExistsAsync(string phoneNumber)
        {
            return await _context.Agents.AnyAsync(a => a.PhoneNumber == phoneNumber);
        }

        private static AgentResponseDto MapToResponseDto(Agent agent)
        {
            return new AgentResponseDto
            {
                AgentId = agent.AgentId,
                FirstName = agent.FirstName,
                LastName = agent.LastName,
                Email = agent.Email,
                PhoneNumber = agent.PhoneNumber,
                AadharNumber = agent.AadharNumber,
                PanNumber = agent.PanNumber,
                Address = agent.Address,
                City = agent.City,
                State = agent.State,
                PinCode = agent.PinCode,
                DateOfBirth = agent.DateOfBirth,
                Status = agent.Status.ToString(),
                CreatedAt = agent.CreatedAt,
                UpdatedAt = agent.UpdatedAt
            };
        }
    }
}
