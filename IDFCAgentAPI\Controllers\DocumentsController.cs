using IDFCAgentAPI.Models;
using IDFCAgentAPI.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace IDFCAgentAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class DocumentsController : ControllerBase
    {
        private readonly IDocumentService _documentService;
        private readonly ILogger<DocumentsController> _logger;

        public DocumentsController(IDocumentService documentService, ILogger<DocumentsController> logger)
        {
            _documentService = documentService;
            _logger = logger;
        }

        /// <summary>
        /// Upload a document for an agent
        /// </summary>
        [HttpPost("agents/{agentId}/upload")]
        public async Task<IActionResult> UploadDocument(int agentId, [FromForm] DocumentUploadRequest request)
        {
            try
            {
                if (request.File == null || request.File.Length == 0)
                {
                    return BadRequest(new { message = "File is required" });
                }

                // Check if user has permission to upload for this agent
                var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                var userAgentId = User.FindFirst("AgentId")?.Value;

                // Agents can only upload their own documents, admins/reviewers can upload for any agent
                if (userRole == "Agent" && userAgentId != agentId.ToString())
                {
                    return Forbid("You can only upload documents for your own profile");
                }

                var document = await _documentService.UploadDocumentAsync(agentId, request.DocumentType, request.File);

                return Ok(new
                {
                    message = "Document uploaded successfully",
                    document = new
                    {
                        document.DocumentId,
                        document.DocumentType,
                        document.FileName,
                        document.FileSize,
                        document.Status,
                        document.UploadedAt
                    }
                });
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error uploading document for agent {agentId}");
                return StatusCode(500, new { message = "An error occurred while uploading the document" });
            }
        }

        /// <summary>
        /// Get all documents for an agent
        /// </summary>
        [HttpGet("agents/{agentId}")]
        public async Task<IActionResult> GetAgentDocuments(int agentId)
        {
            try
            {
                // Check permissions
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                var userAgentId = User.FindFirst("AgentId")?.Value;

                if (userRole == "Agent" && userAgentId != agentId.ToString())
                {
                    return Forbid("You can only view your own documents");
                }

                var documents = await _documentService.GetDocumentsByAgentIdAsync(agentId);

                var result = documents.Select(d => new
                {
                    d.DocumentId,
                    d.DocumentType,
                    d.FileName,
                    d.FileSize,
                    d.MimeType,
                    d.Status,
                    d.ReviewComments,
                    d.UploadedAt,
                    d.ReviewedAt,
                    d.ReviewedBy
                });

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving documents for agent {agentId}");
                return StatusCode(500, new { message = "An error occurred while retrieving documents" });
            }
        }

        /// <summary>
        /// Get documents by status (Admin/Reviewer only)
        /// </summary>
        [HttpGet("status/{status}")]
        [Authorize(Roles = "Admin,Reviewer")]
        public async Task<IActionResult> GetDocumentsByStatus(DocumentStatus status)
        {
            try
            {
                var documents = await _documentService.GetDocumentsByStatusAsync(status);

                var result = documents.Select(d => new
                {
                    d.DocumentId,
                    d.AgentId,
                    AgentName = $"{d.Agent.FirstName} {d.Agent.LastName}",
                    d.DocumentType,
                    d.FileName,
                    d.FileSize,
                    d.Status,
                    d.UploadedAt,
                    d.ReviewedAt,
                    d.ReviewedBy
                });

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving documents by status {status}");
                return StatusCode(500, new { message = "An error occurred while retrieving documents" });
            }
        }

        /// <summary>
        /// Download a document
        /// </summary>
        [HttpGet("{documentId}/download")]
        public async Task<IActionResult> DownloadDocument(int documentId)
        {
            try
            {
                var document = await _documentService.GetDocumentByIdAsync(documentId);
                if (document == null)
                {
                    return NotFound(new { message = "Document not found" });
                }

                // Check permissions
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                var userAgentId = User.FindFirst("AgentId")?.Value;

                if (userRole == "Agent" && userAgentId != document.AgentId.ToString())
                {
                    return Forbid("You can only download your own documents");
                }

                var fileContent = await _documentService.GetDocumentContentAsync(documentId);
                if (fileContent == null)
                {
                    return NotFound(new { message = "Document file not found" });
                }

                return File(fileContent, document.MimeType, document.FileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error downloading document {documentId}");
                return StatusCode(500, new { message = "An error occurred while downloading the document" });
            }
        }

        /// <summary>
        /// Update document status (Admin/Reviewer only)
        /// </summary>
        [HttpPatch("{documentId}/status")]
        [Authorize(Roles = "Admin,Reviewer")]
        public async Task<IActionResult> UpdateDocumentStatus(int documentId, [FromBody] DocumentStatusUpdateRequest request)
        {
            try
            {
                var reviewerName = User.FindFirst(ClaimTypes.Name)?.Value ?? "Unknown";
                
                var success = await _documentService.UpdateDocumentStatusAsync(
                    documentId, 
                    request.Status, 
                    reviewerName, 
                    request.Comments);

                if (!success)
                {
                    return NotFound(new { message = "Document not found" });
                }

                return Ok(new { message = "Document status updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating document status for document {documentId}");
                return StatusCode(500, new { message = "An error occurred while updating document status" });
            }
        }

        /// <summary>
        /// Delete a document (Admin only)
        /// </summary>
        [HttpDelete("{documentId}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteDocument(int documentId)
        {
            try
            {
                var success = await _documentService.DeleteDocumentAsync(documentId);
                if (!success)
                {
                    return NotFound(new { message = "Document not found" });
                }

                return Ok(new { message = "Document deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting document {documentId}");
                return StatusCode(500, new { message = "An error occurred while deleting the document" });
            }
        }

        /// <summary>
        /// Get document details by ID
        /// </summary>
        [HttpGet("{documentId}")]
        public async Task<IActionResult> GetDocument(int documentId)
        {
            try
            {
                var document = await _documentService.GetDocumentByIdAsync(documentId);
                if (document == null)
                {
                    return NotFound(new { message = "Document not found" });
                }

                // Check permissions
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                var userAgentId = User.FindFirst("AgentId")?.Value;

                if (userRole == "Agent" && userAgentId != document.AgentId.ToString())
                {
                    return Forbid("You can only view your own documents");
                }

                var result = new
                {
                    document.DocumentId,
                    document.AgentId,
                    document.DocumentType,
                    document.FileName,
                    document.FileSize,
                    document.MimeType,
                    document.Status,
                    document.ReviewComments,
                    document.UploadedAt,
                    document.ReviewedAt,
                    document.ReviewedBy
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving document {documentId}");
                return StatusCode(500, new { message = "An error occurred while retrieving the document" });
            }
        }
    }

    public class DocumentUploadRequest
    {
        public DocumentType DocumentType { get; set; }
        public IFormFile File { get; set; } = null!;
    }

    public class DocumentStatusUpdateRequest
    {
        public DocumentStatus Status { get; set; }
        public string? Comments { get; set; }
    }
}
