[{"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "GetAllAgents", "RelativePath": "api/Agents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[IDFCAgentAPI.DTOs.AgentResponseDto, IDFCAgentAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "GetAgent", "RelativePath": "api/Agents/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.AgentResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "UpdateAgent", "RelativePath": "api/Agents/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateDto", "Type": "IDFCAgentAPI.DTOs.AgentUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.AgentResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "DeleteAgent", "RelativePath": "api/Agents/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "UpdateAgentStatus", "RelativePath": "api/Agents/{id}/status", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "statusDto", "Type": "IDFCAgentAPI.Controllers.UpdateStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "GetAgentStatusHistory", "RelativePath": "api/Agents/{id}/status-history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[IDFCAgentAPI.Models.AgentStatusHistory, IDFCAgentAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "RegisterAgent", "RelativePath": "api/Agents/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registrationDto", "Type": "IDFCAgentAPI.DTOs.AgentRegistrationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.AgentResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "GetAgentsByStatus", "RelativePath": "api/Agents/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[IDFCAgentAPI.DTOs.AgentResponseDto, IDFCAgentAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/Auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "changePasswordDto", "Type": "IDFCAgentAPI.DTOs.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "IDFCAgentAPI.DTOs.LoginDto", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.LoginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.AuthController", "Method": "GetProfile", "RelativePath": "api/Auth/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshTokenDto", "Type": "IDFCAgentAPI.DTOs.RefreshTokenDto", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.LoginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.DocumentsController", "Method": "DeleteDocument", "RelativePath": "api/Documents/{documentId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.DocumentsController", "Method": "GetDocument", "RelativePath": "api/Documents/{documentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.DocumentsController", "Method": "DownloadDocument", "RelativePath": "api/Documents/{documentId}/download", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.DocumentsController", "Method": "UpdateDocumentStatus", "RelativePath": "api/Documents/{documentId}/status", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "documentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "IDFCAgentAPI.Controllers.DocumentStatusUpdateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.DocumentsController", "Method": "GetAgentDocuments", "RelativePath": "api/Documents/agents/{agentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.DocumentsController", "Method": "UploadDocument", "RelativePath": "api/Documents/agents/{agentId}/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "DocumentType", "Type": "System.String", "IsRequired": false}, {"Name": "File", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.DocumentsController", "Method": "GetDocumentsByStatus", "RelativePath": "api/Documents/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.WorkflowController", "Method": "AutoProgressWorkflow", "RelativePath": "api/Workflow/agents/{agentId}/auto-progress", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.WorkflowController", "Method": "GetEstimatedCompletionTime", "RelativePath": "api/Workflow/agents/{agentId}/estimated-completion", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.WorkflowController", "Method": "IsWorkflowComplete", "RelativePath": "api/Workflow/agents/{agentId}/is-complete", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.WorkflowController", "Method": "GetNextWorkflowStep", "RelativePath": "api/Workflow/agents/{agentId}/next-step", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.Services.WorkflowStepResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.WorkflowController", "Method": "RequiresManualReview", "RelativePath": "api/Workflow/agents/{agentId}/requires-review", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.WorkflowController", "Method": "GetWorkflowSummary", "RelativePath": "api/Workflow/agents/{agentId}/summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.Services.WorkflowSummary", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.WorkflowController", "Method": "ProcessStatusTransition", "RelativePath": "api/Workflow/agents/{agentId}/transition", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "IDFCAgentAPI.Controllers.ProcessTransitionRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.WorkflowController", "Method": "GetValidNextStatuses", "RelativePath": "api/Workflow/agents/{agentId}/valid-statuses", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[IDFCAgentAPI.Models.AgentStatus, IDFCAgentAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.WorkflowController", "Method": "ValidateStatusTransition", "RelativePath": "api/Workflow/agents/{agentId}/validate-transition", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "agentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "IDFCAgentAPI.Controllers.ValidateTransitionRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.Services.WorkflowValidationResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]