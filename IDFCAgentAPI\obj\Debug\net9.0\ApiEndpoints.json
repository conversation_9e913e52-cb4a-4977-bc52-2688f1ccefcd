[{"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "GetAllAgents", "RelativePath": "api/Agents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[IDFCAgentAPI.DTOs.AgentResponseDto, IDFCAgentAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "GetAgent", "RelativePath": "api/Agents/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.AgentResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "UpdateAgent", "RelativePath": "api/Agents/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateDto", "Type": "IDFCAgentAPI.DTOs.AgentUpdateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.AgentResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "DeleteAgent", "RelativePath": "api/Agents/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "UpdateAgentStatus", "RelativePath": "api/Agents/{id}/status", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "statusDto", "Type": "IDFCAgentAPI.Controllers.UpdateStatusDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "GetAgentStatusHistory", "RelativePath": "api/Agents/{id}/status-history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[IDFCAgentAPI.Models.AgentStatusHistory, IDFCAgentAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "RegisterAgent", "RelativePath": "api/Agents/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registrationDto", "Type": "IDFCAgentAPI.DTOs.AgentRegistrationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.AgentResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AgentsController", "Method": "GetAgentsByStatus", "RelativePath": "api/Agents/status/{status}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[IDFCAgentAPI.DTOs.AgentResponseDto, IDFCAgentAPI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/Auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "changePasswordDto", "Type": "IDFCAgentAPI.DTOs.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "IDFCAgentAPI.DTOs.LoginDto", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.LoginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "IDFCAgentAPI.Controllers.AuthController", "Method": "GetProfile", "RelativePath": "api/Auth/profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "IDFCAgentAPI.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshTokenDto", "Type": "IDFCAgentAPI.DTOs.RefreshTokenDto", "IsRequired": true}], "ReturnTypes": [{"Type": "IDFCAgentAPI.DTOs.LoginResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]