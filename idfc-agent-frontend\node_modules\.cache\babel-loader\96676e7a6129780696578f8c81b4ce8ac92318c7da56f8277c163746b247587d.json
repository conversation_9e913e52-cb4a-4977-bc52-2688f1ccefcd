{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\IDFCAgentOnboardingAndManagementSolution\\\\idfc-agent-frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Layout from './components/Layout';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport AgentRegistration from './pages/AgentRegistration';\nimport AgentList from './pages/AgentList';\nimport AgentDetails from './pages/AgentDetails';\nimport Profile from './pages/Profile';\nimport DocumentManagement from './pages/DocumentManagement';\nimport 'react-toastify/dist/ReactToastify.css';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: [/*#__PURE__*/_jsxDEV(ToastContainer, {\n          position: \"top-right\",\n          autoClose: 5000,\n          hideProgressBar: false,\n          newestOnTop: false,\n          closeOnClick: true,\n          rtl: false,\n          pauseOnFocusLoss: true,\n          draggable: true,\n          pauseOnHover: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              index: true,\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/dashboard\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"dashboard\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"register\",\n              element: /*#__PURE__*/_jsxDEV(AgentRegistration, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"agents\",\n              element: /*#__PURE__*/_jsxDEV(AgentList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"agents/:id\",\n              element: /*#__PURE__*/_jsxDEV(AgentDetails, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"profile\",\n              element: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "ToastContainer", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "Layout", "<PERSON><PERSON>", "Dashboard", "AgentRegistration", "AgentList", "AgentDetails", "Profile", "DocumentManagement", "jsxDEV", "_jsxDEV", "App", "children", "className", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "index", "to", "replace", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/IDFCAgentOnboardingAndManagementSolution/idfc-agent-frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ToastContainer } from 'react-toastify';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Layout from './components/Layout';\nimport Login from './pages/Login';\nimport Dashboard from './pages/Dashboard';\nimport AgentRegistration from './pages/AgentRegistration';\nimport AgentList from './pages/AgentList';\nimport AgentDetails from './pages/AgentDetails';\nimport Profile from './pages/Profile';\nimport DocumentManagement from './pages/DocumentManagement';\nimport 'react-toastify/dist/ReactToastify.css';\nimport './App.css';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <ToastContainer\n            position=\"top-right\"\n            autoClose={5000}\n            hideProgressBar={false}\n            newestOnTop={false}\n            closeOnClick\n            rtl={false}\n            pauseOnFocusLoss\n            draggable\n            pauseOnHover\n          />\n\n          <Routes>\n            <Route path=\"/login\" element={<Login />} />\n\n            <Route path=\"/\" element={\n              <ProtectedRoute>\n                <Layout />\n              </ProtectedRoute>\n            }>\n              <Route index element={<Navigate to=\"/dashboard\" replace />} />\n              <Route path=\"dashboard\" element={<Dashboard />} />\n              <Route path=\"register\" element={<AgentRegistration />} />\n              <Route path=\"agents\" element={<AgentList />} />\n              <Route path=\"agents/:id\" element={<AgentDetails />} />\n              <Route path=\"profile\" element={<Profile />} />\n            </Route>\n\n            <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} />\n          </Routes>\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAO,uCAAuC;AAC9C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACX,YAAY;IAAAa,QAAA,eACXF,OAAA,CAAChB,MAAM;MAAAkB,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,gBAClBF,OAAA,CAACZ,cAAc;UACbgB,QAAQ,EAAC,WAAW;UACpBC,SAAS,EAAE,IAAK;UAChBC,eAAe,EAAE,KAAM;UACvBC,WAAW,EAAE,KAAM;UACnBC,YAAY;UACZC,GAAG,EAAE,KAAM;UACXC,gBAAgB;UAChBC,SAAS;UACTC,YAAY;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAEFhB,OAAA,CAACf,MAAM;UAAAiB,QAAA,gBACLF,OAAA,CAACd,KAAK;YAAC+B,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAElB,OAAA,CAACR,KAAK;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE3ChB,OAAA,CAACd,KAAK;YAAC+B,IAAI,EAAC,GAAG;YAACC,OAAO,eACrBlB,OAAA,CAACV,cAAc;cAAAY,QAAA,eACbF,OAAA,CAACT,MAAM;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACjB;YAAAd,QAAA,gBACCF,OAAA,CAACd,KAAK;cAACiC,KAAK;cAACD,OAAO,eAAElB,OAAA,CAACb,QAAQ;gBAACiC,EAAE,EAAC,YAAY;gBAACC,OAAO;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DhB,OAAA,CAACd,KAAK;cAAC+B,IAAI,EAAC,WAAW;cAACC,OAAO,eAAElB,OAAA,CAACP,SAAS;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClDhB,OAAA,CAACd,KAAK;cAAC+B,IAAI,EAAC,UAAU;cAACC,OAAO,eAAElB,OAAA,CAACN,iBAAiB;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDhB,OAAA,CAACd,KAAK;cAAC+B,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAElB,OAAA,CAACL,SAAS;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/ChB,OAAA,CAACd,KAAK;cAAC+B,IAAI,EAAC,YAAY;cAACC,OAAO,eAAElB,OAAA,CAACJ,YAAY;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtDhB,OAAA,CAACd,KAAK;cAAC+B,IAAI,EAAC,SAAS;cAACC,OAAO,eAAElB,OAAA,CAACH,OAAO;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eAERhB,OAAA,CAACd,KAAK;YAAC+B,IAAI,EAAC,GAAG;YAACC,OAAO,eAAElB,OAAA,CAACb,QAAQ;cAACiC,EAAE,EAAC,YAAY;cAACC,OAAO;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACM,EAAA,GAvCQrB,GAAG;AAyCZ,eAAeA,GAAG;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}