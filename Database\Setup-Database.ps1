# =============================================
# IDFC Agent Database Setup PowerShell Script
# =============================================
# This script executes the SQL setup script and verifies the database creation

param(
    [string]$ServerInstance = "(localdb)\mssqllocaldb",
    [string]$DatabaseName = "IDFCAgentDB",
    [string]$SqlScriptPath = "IDFCAgentDB_Setup.sql"
)

Write-Host "===============================================" -ForegroundColor Green
Write-Host "IDFC Agent Database Setup Script" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green

# Check if SQL Server LocalDB is available
Write-Host "Checking SQL Server LocalDB availability..." -ForegroundColor Yellow

try {
    $localDbInfo = sqllocaldb info
    if ($localDbInfo -contains "mssqllocaldb") {
        Write-Host "✓ SQL Server LocalDB is available" -ForegroundColor Green
    } else {
        Write-Host "✗ SQL Server LocalDB not found. Please install SQL Server LocalDB." -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Error checking LocalDB: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Start LocalDB instance if not running
Write-Host "Starting LocalDB instance..." -ForegroundColor Yellow
try {
    sqllocaldb start mssqllocaldb
    Write-Host "✓ LocalDB instance started" -ForegroundColor Green
} catch {
    Write-Host "⚠ LocalDB may already be running: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Check if SQL script file exists
$scriptFullPath = Join-Path $PSScriptRoot $SqlScriptPath
if (-not (Test-Path $scriptFullPath)) {
    Write-Host "✗ SQL script file not found: $scriptFullPath" -ForegroundColor Red
    exit 1
}

Write-Host "✓ SQL script file found: $scriptFullPath" -ForegroundColor Green

# Execute the SQL script
Write-Host "Executing SQL setup script..." -ForegroundColor Yellow

try {
    # Use sqlcmd to execute the script
    $result = sqlcmd -S $ServerInstance -i $scriptFullPath -b
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ SQL script executed successfully" -ForegroundColor Green
        Write-Host $result -ForegroundColor Cyan
    } else {
        Write-Host "✗ Error executing SQL script" -ForegroundColor Red
        Write-Host $result -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Error executing SQL script: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify database creation
Write-Host "Verifying database setup..." -ForegroundColor Yellow

try {
    $verificationQuery = @"
SELECT 
    'Database' AS Component,
    DB_NAME() AS Name,
    'Created' AS Status
UNION ALL
SELECT 
    'Tables' AS Component,
    CAST(COUNT(*) AS NVARCHAR(10)) AS Name,
    'Created' AS Status
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
UNION ALL
SELECT 
    'Views' AS Component,
    CAST(COUNT(*) AS NVARCHAR(10)) AS Name,
    'Created' AS Status
FROM INFORMATION_SCHEMA.VIEWS
UNION ALL
SELECT 
    'Stored Procedures' AS Component,
    CAST(COUNT(*) AS NVARCHAR(10)) AS Name,
    'Created' AS Status
FROM INFORMATION_SCHEMA.ROUTINES 
WHERE ROUTINE_TYPE = 'PROCEDURE'
UNION ALL
SELECT 
    'Functions' AS Component,
    CAST(COUNT(*) AS NVARCHAR(10)) AS Name,
    'Created' AS Status
FROM INFORMATION_SCHEMA.ROUTINES 
WHERE ROUTINE_TYPE = 'FUNCTION'
"@

    $verificationResult = sqlcmd -S $ServerInstance -d $DatabaseName -Q $verificationQuery -h -1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Database verification completed" -ForegroundColor Green
        Write-Host "Database Components:" -ForegroundColor Cyan
        Write-Host $verificationResult -ForegroundColor White
    } else {
        Write-Host "⚠ Database verification had issues" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ Error during verification: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Test connection with sample query
Write-Host "Testing database connection..." -ForegroundColor Yellow

try {
    $testQuery = "SELECT COUNT(*) AS UserCount FROM Users; SELECT COUNT(*) AS AgentCount FROM Agents;"
    $testResult = sqlcmd -S $ServerInstance -d $DatabaseName -Q $testQuery -h -1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Database connection test successful" -ForegroundColor Green
        Write-Host "Sample Data:" -ForegroundColor Cyan
        Write-Host $testResult -ForegroundColor White
    } else {
        Write-Host "✗ Database connection test failed" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Error testing connection: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "===============================================" -ForegroundColor Green
Write-Host "Database Setup Summary" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Green
Write-Host "Server: $ServerInstance" -ForegroundColor White
Write-Host "Database: $DatabaseName" -ForegroundColor White
Write-Host "Connection String for appsettings.json:" -ForegroundColor Yellow
Write-Host "Server=$ServerInstance;Database=$DatabaseName;Trusted_Connection=true;MultipleActiveResultSets=true" -ForegroundColor Cyan
Write-Host ""
Write-Host "Default Login Credentials:" -ForegroundColor Yellow
Write-Host "Admin - Username: admin, Password: Admin@123" -ForegroundColor White
Write-Host "Reviewer - Username: reviewer, Password: Reviewer@123" -ForegroundColor White
Write-Host ""
Write-Host "Setup completed! You can now run the .NET Core API." -ForegroundColor Green
