using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using IDFCAgentAPI.Services;
using IDFCAgentAPI.Models;
using System.Security.Claims;

namespace IDFCAgentAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class WorkflowController : ControllerBase
    {
        private readonly IWorkflowService _workflowService;
        private readonly ILogger<WorkflowController> _logger;

        public WorkflowController(IWorkflowService workflowService, ILogger<WorkflowController> logger)
        {
            _workflowService = workflowService;
            _logger = logger;
        }

        /// <summary>
        /// Get workflow summary for an agent
        /// </summary>
        [HttpGet("agents/{agentId}/summary")]
        public async Task<ActionResult<WorkflowSummary>> GetWorkflowSummary(int agentId)
        {
            try
            {
                // Check authorization - agents can only view their own workflow
                if (!CanAccessAgent(agentId))
                {
                    return Forbid();
                }

                var summary = await _workflowService.GetWorkflowSummaryAsync(agentId);
                return Ok(summary);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting workflow summary for agent {agentId}");
                return StatusCode(500, new { message = "An error occurred while retrieving workflow summary" });
            }
        }

        /// <summary>
        /// Get next workflow step for an agent
        /// </summary>
        [HttpGet("agents/{agentId}/next-step")]
        public async Task<ActionResult<WorkflowStepResult>> GetNextWorkflowStep(int agentId)
        {
            try
            {
                if (!CanAccessAgent(agentId))
                {
                    return Forbid();
                }

                var nextStep = await _workflowService.GetNextWorkflowStepAsync(agentId);
                return Ok(nextStep);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting next workflow step for agent {agentId}");
                return StatusCode(500, new { message = "An error occurred while retrieving next workflow step" });
            }
        }

        /// <summary>
        /// Get valid next statuses for an agent
        /// </summary>
        [HttpGet("agents/{agentId}/valid-statuses")]
        [Authorize(Roles = "Admin,Reviewer")]
        public async Task<ActionResult<IEnumerable<AgentStatus>>> GetValidNextStatuses(int agentId)
        {
            try
            {
                var validStatuses = await _workflowService.GetValidNextStatusesAsync(agentId);
                return Ok(validStatuses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting valid statuses for agent {agentId}");
                return StatusCode(500, new { message = "An error occurred while retrieving valid statuses" });
            }
        }

        /// <summary>
        /// Validate status transition for an agent
        /// </summary>
        [HttpPost("agents/{agentId}/validate-transition")]
        [Authorize(Roles = "Admin,Reviewer")]
        public async Task<ActionResult<WorkflowValidationResult>> ValidateStatusTransition(int agentId, [FromBody] ValidateTransitionRequest request)
        {
            try
            {
                var validation = await _workflowService.ValidateStatusTransitionAsync(agentId, request.NewStatus);
                return Ok(validation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error validating status transition for agent {agentId}");
                return StatusCode(500, new { message = "An error occurred while validating status transition" });
            }
        }

        /// <summary>
        /// Process status transition with workflow validation
        /// </summary>
        [HttpPost("agents/{agentId}/transition")]
        [Authorize(Roles = "Admin,Reviewer")]
        public async Task<ActionResult> ProcessStatusTransition(int agentId, [FromBody] ProcessTransitionRequest request)
        {
            try
            {
                var currentUsername = GetCurrentUsername();
                var success = await _workflowService.ProcessStatusTransitionAsync(
                    agentId, 
                    request.NewStatus, 
                    currentUsername, 
                    request.Comments);

                if (!success)
                {
                    return BadRequest(new { message = "Status transition failed validation or processing" });
                }

                return Ok(new { message = "Status transition processed successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing status transition for agent {agentId}");
                return StatusCode(500, new { message = "An error occurred while processing status transition" });
            }
        }

        /// <summary>
        /// Auto-progress workflow for an agent
        /// </summary>
        [HttpPost("agents/{agentId}/auto-progress")]
        [Authorize(Roles = "Admin,Reviewer")]
        public async Task<ActionResult> AutoProgressWorkflow(int agentId)
        {
            try
            {
                var progressMade = await _workflowService.AutoProgressWorkflowAsync(agentId);
                
                if (progressMade)
                {
                    return Ok(new { message = "Workflow auto-progressed successfully" });
                }
                else
                {
                    return Ok(new { message = "No auto-progression available at this time" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error auto-progressing workflow for agent {agentId}");
                return StatusCode(500, new { message = "An error occurred while auto-progressing workflow" });
            }
        }

        /// <summary>
        /// Check if workflow is complete for an agent
        /// </summary>
        [HttpGet("agents/{agentId}/is-complete")]
        public async Task<ActionResult<bool>> IsWorkflowComplete(int agentId)
        {
            try
            {
                if (!CanAccessAgent(agentId))
                {
                    return Forbid();
                }

                var isComplete = await _workflowService.IsWorkflowCompleteAsync(agentId);
                return Ok(new { isComplete });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking workflow completion for agent {agentId}");
                return StatusCode(500, new { message = "An error occurred while checking workflow completion" });
            }
        }

        /// <summary>
        /// Check if agent requires manual review
        /// </summary>
        [HttpGet("agents/{agentId}/requires-review")]
        [Authorize(Roles = "Admin,Reviewer")]
        public async Task<ActionResult<bool>> RequiresManualReview(int agentId)
        {
            try
            {
                var requiresReview = await _workflowService.RequiresManualReviewAsync(agentId);
                return Ok(new { requiresReview });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking manual review requirement for agent {agentId}");
                return StatusCode(500, new { message = "An error occurred while checking manual review requirement" });
            }
        }

        /// <summary>
        /// Get estimated completion time for an agent's workflow
        /// </summary>
        [HttpGet("agents/{agentId}/estimated-completion")]
        public async Task<ActionResult> GetEstimatedCompletionTime(int agentId)
        {
            try
            {
                if (!CanAccessAgent(agentId))
                {
                    return Forbid();
                }

                var estimatedTime = await _workflowService.GetEstimatedCompletionTimeAsync(agentId);
                return Ok(new { estimatedCompletionTime = estimatedTime });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting estimated completion time for agent {agentId}");
                return StatusCode(500, new { message = "An error occurred while getting estimated completion time" });
            }
        }

        private bool CanAccessAgent(int agentId)
        {
            var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
            
            // Admins and reviewers can access any agent
            if (userRole == "Admin" || userRole == "Reviewer")
                return true;

            // Agents can only access their own data
            if (userRole == "Agent")
            {
                var currentAgentId = GetCurrentAgentId();
                return currentAgentId == agentId;
            }

            return false;
        }

        private int? GetCurrentAgentId()
        {
            var agentIdClaim = User.FindFirst("AgentId")?.Value;
            return int.TryParse(agentIdClaim, out var agentId) ? agentId : null;
        }

        private string GetCurrentUsername()
        {
            return User.FindFirst(ClaimTypes.Name)?.Value ?? "";
        }
    }

    public class ValidateTransitionRequest
    {
        public AgentStatus NewStatus { get; set; }
    }

    public class ProcessTransitionRequest
    {
        public AgentStatus NewStatus { get; set; }
        public string? Comments { get; set; }
    }
}
