using IDFCAgentAPI.DTOs;
using IDFCAgentAPI.Models;

namespace IDFCAgentAPI.Services
{
    public interface IAuthService
    {
        Task<LoginResponseDto?> LoginAsync(LoginDto loginDto);
        Task<LoginResponseDto?> RefreshTokenAsync(string refreshToken);
        Task<bool> ChangePasswordAsync(int userId, ChangePasswordDto changePasswordDto);
        Task<bool> LogoutAsync(int userId);
        string GenerateJwtToken(User user);
        string GenerateRefreshToken();
        Task<User?> GetUserByUsernameAsync(string username);
        Task<User?> GetUserByIdAsync(int userId);
        bool VerifyPassword(string password, string hashedPassword);
        string HashPassword(string password);
    }
}
