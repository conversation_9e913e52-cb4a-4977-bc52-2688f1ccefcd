[{"D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\index.js": "1", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\App.js": "2", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\reportWebVitals.js": "3", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Dashboard.js": "4", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\contexts\\AuthContext.js": "5", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentRegistration.js": "6", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentDetails.js": "7", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Login.js": "8", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Profile.js": "9", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentList.js": "10", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\ProtectedRoute.js": "11", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\Layout.js": "12", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\services\\api.js": "13"}, {"size": 535, "mtime": 1751460716165, "results": "14", "hashOfConfig": "15"}, {"size": 1864, "mtime": 1751460981545, "results": "16", "hashOfConfig": "15"}, {"size": 362, "mtime": 1751460717977, "results": "17", "hashOfConfig": "15"}, {"size": 11042, "mtime": 1751461080678, "results": "18", "hashOfConfig": "15"}, {"size": 3122, "mtime": 1751460967722, "results": "19", "hashOfConfig": "15"}, {"size": 26229, "mtime": 1751463364529, "results": "20", "hashOfConfig": "15"}, {"size": 18530, "mtime": 1751463706938, "results": "21", "hashOfConfig": "15"}, {"size": 5319, "mtime": 1751461041358, "results": "22", "hashOfConfig": "15"}, {"size": 14909, "mtime": 1751463607133, "results": "23", "hashOfConfig": "15"}, {"size": 17566, "mtime": 1751463692427, "results": "24", "hashOfConfig": "15"}, {"size": 982, "mtime": 1751460994827, "results": "25", "hashOfConfig": "15"}, {"size": 5787, "mtime": 1751461019244, "results": "26", "hashOfConfig": "15"}, {"size": 3634, "mtime": 1751462556364, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10bdkdv", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\index.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\App.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\reportWebVitals.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Dashboard.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\contexts\\AuthContext.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentRegistration.js", ["67", "68", "69"], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentDetails.js", ["70", "71", "72"], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Login.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Profile.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentList.js", ["73"], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\Layout.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\services\\api.js", [], [], {"ruleId": "74", "severity": 1, "message": "75", "line": 17, "column": 3, "nodeType": "76", "messageId": "77", "endLine": 17, "endColumn": 11}, {"ruleId": "74", "severity": 1, "message": "78", "line": 18, "column": 3, "nodeType": "76", "messageId": "77", "endLine": 18, "endColumn": 13}, {"ruleId": "79", "severity": 1, "message": "80", "line": 82, "column": 5, "nodeType": "81", "messageId": "82", "endLine": 98, "endColumn": 6}, {"ruleId": "74", "severity": 1, "message": "83", "line": 12, "column": 3, "nodeType": "76", "messageId": "77", "endLine": 12, "endColumn": 11}, {"ruleId": "74", "severity": 1, "message": "84", "line": 21, "column": 3, "nodeType": "76", "messageId": "77", "endLine": 21, "endColumn": 16}, {"ruleId": "85", "severity": 1, "message": "86", "line": 40, "column": 6, "nodeType": "87", "endLine": 40, "endColumn": 10, "suggestions": "88"}, {"ruleId": "85", "severity": 1, "message": "89", "line": 34, "column": 6, "nodeType": "87", "endLine": 34, "endColumn": 52, "suggestions": "90"}, "no-unused-vars", "'Building' is defined but never used.", "Identifier", "unusedVar", "'DollarSign' is defined but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'Calendar' is defined but never used.", "'MessageSquare' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAgentDetails'. Either include it or remove the dependency array.", "ArrayExpression", ["91"], "React Hook useEffect has a missing dependency: 'fetchAgents'. Either include it or remove the dependency array.", ["92"], {"desc": "93", "fix": "94"}, {"desc": "95", "fix": "96"}, "Update the dependencies array to be: [fetchAgentDetails, id]", {"range": "97", "text": "98"}, "Update the dependencies array to be: [currentPage, statusFilter, sortBy, sortOrder, fetchAgents]", {"range": "99", "text": "100"}, [1070, 1074], "[fetchAgentDetails, id]", [955, 1001], "[currentPage, statusFilter, sortBy, sortOrder, fetchAgents]"]