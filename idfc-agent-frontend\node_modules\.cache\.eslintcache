[{"D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\index.js": "1", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\App.js": "2", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\reportWebVitals.js": "3", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Dashboard.js": "4", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\contexts\\AuthContext.js": "5", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentRegistration.js": "6", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentDetails.js": "7", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Login.js": "8", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Profile.js": "9", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentList.js": "10", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\ProtectedRoute.js": "11", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\Layout.js": "12", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\services\\api.js": "13", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\DocumentManagement.js": "14", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\WorkflowTimeline.js": "15", "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\WorkflowStatusUpdate.js": "16"}, {"size": 535, "mtime": 1751460716165, "results": "17", "hashOfConfig": "18"}, {"size": 2076, "mtime": 1751521537023, "results": "19", "hashOfConfig": "18"}, {"size": 362, "mtime": 1751460717977, "results": "20", "hashOfConfig": "18"}, {"size": 11042, "mtime": 1751461080678, "results": "21", "hashOfConfig": "18"}, {"size": 3122, "mtime": 1751460967722, "results": "22", "hashOfConfig": "18"}, {"size": 26684, "mtime": 1751521695322, "results": "23", "hashOfConfig": "18"}, {"size": 15831, "mtime": 1751465402943, "results": "24", "hashOfConfig": "18"}, {"size": 6113, "mtime": 1751521747638, "results": "25", "hashOfConfig": "18"}, {"size": 14909, "mtime": 1751463607133, "results": "26", "hashOfConfig": "18"}, {"size": 17566, "mtime": 1751463692427, "results": "27", "hashOfConfig": "18"}, {"size": 982, "mtime": 1751460994827, "results": "28", "hashOfConfig": "18"}, {"size": 5916, "mtime": 1751522325883, "results": "29", "hashOfConfig": "18"}, {"size": 5805, "mtime": 1751465133278, "results": "30", "hashOfConfig": "18"}, {"size": 14627, "mtime": 1751464676058, "results": "31", "hashOfConfig": "18"}, {"size": 9479, "mtime": 1751465119524, "results": "32", "hashOfConfig": "18"}, {"size": 11350, "mtime": 1751465273411, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10bdkdv", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\index.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\App.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\reportWebVitals.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Dashboard.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\contexts\\AuthContext.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentRegistration.js", ["82", "83", "84"], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentDetails.js", ["85", "86", "87"], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Login.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\Profile.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\AgentList.js", ["88"], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\Layout.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\services\\api.js", [], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\pages\\DocumentManagement.js", ["89"], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\WorkflowTimeline.js", ["90"], [], "D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\src\\components\\WorkflowStatusUpdate.js", ["91", "92", "93", "94"], [], {"ruleId": "95", "severity": 1, "message": "96", "line": 17, "column": 3, "nodeType": "97", "messageId": "98", "endLine": 17, "endColumn": 11}, {"ruleId": "95", "severity": 1, "message": "99", "line": 18, "column": 3, "nodeType": "97", "messageId": "98", "endLine": 18, "endColumn": 13}, {"ruleId": "100", "severity": 1, "message": "101", "line": 83, "column": 5, "nodeType": "102", "messageId": "103", "endLine": 99, "endColumn": 6}, {"ruleId": "95", "severity": 1, "message": "104", "line": 14, "column": 3, "nodeType": "97", "messageId": "98", "endLine": 14, "endColumn": 11}, {"ruleId": "95", "severity": 1, "message": "105", "line": 23, "column": 3, "nodeType": "97", "messageId": "98", "endLine": 23, "endColumn": 16}, {"ruleId": "106", "severity": 1, "message": "107", "line": 40, "column": 6, "nodeType": "108", "endLine": 40, "endColumn": 10, "suggestions": "109"}, {"ruleId": "106", "severity": 1, "message": "110", "line": 34, "column": 6, "nodeType": "108", "endLine": 34, "endColumn": 52, "suggestions": "111"}, {"ruleId": "106", "severity": 1, "message": "112", "line": 37, "column": 6, "nodeType": "108", "endLine": 37, "endColumn": 22, "suggestions": "113"}, {"ruleId": "106", "severity": 1, "message": "114", "line": 23, "column": 6, "nodeType": "108", "endLine": 23, "endColumn": 15, "suggestions": "115"}, {"ruleId": "95", "severity": 1, "message": "116", "line": 10, "column": 3, "nodeType": "97", "messageId": "98", "endLine": 10, "endColumn": 7}, {"ruleId": "95", "severity": 1, "message": "117", "line": 12, "column": 3, "nodeType": "97", "messageId": "98", "endLine": 12, "endColumn": 9}, {"ruleId": "106", "severity": 1, "message": "118", "line": 26, "column": 6, "nodeType": "108", "endLine": 26, "endColumn": 15, "suggestions": "119"}, {"ruleId": "106", "severity": 1, "message": "120", "line": 32, "column": 6, "nodeType": "108", "endLine": 32, "endColumn": 22, "suggestions": "121"}, "no-unused-vars", "'Building' is defined but never used.", "Identifier", "unusedVar", "'DollarSign' is defined but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'Calendar' is defined but never used.", "'MessageSquare' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAgentDetails'. Either include it or remove the dependency array.", "ArrayExpression", ["122"], "React Hook useEffect has a missing dependency: 'fetchAgents'. Either include it or remove the dependency array.", ["123"], "React Hook useEffect has a missing dependency: 'fetchDocuments'. Either include it or remove the dependency array.", ["124"], "React Hook useEffect has a missing dependency: 'fetchWorkflowSummary'. Either include it or remove the dependency array.", ["125"], "'User' is defined but never used.", "'Shield' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchValidStatuses'. Either include it or remove the dependency array.", ["126"], "React Hook useEffect has a missing dependency: 'validateTransition'. Either include it or remove the dependency array.", ["127"], {"desc": "128", "fix": "129"}, {"desc": "130", "fix": "131"}, {"desc": "132", "fix": "133"}, {"desc": "134", "fix": "135"}, {"desc": "136", "fix": "137"}, {"desc": "138", "fix": "139"}, "Update the dependencies array to be: [fetchAgentDetails, id]", {"range": "140", "text": "141"}, "Update the dependencies array to be: [currentPage, statusFilter, sortBy, sortOrder, fetchAgents]", {"range": "142", "text": "143"}, "Update the dependencies array to be: [fetchDocuments, selectedStatus]", {"range": "144", "text": "145"}, "Update the dependencies array to be: [agentId, fetchWorkflowSummary]", {"range": "146", "text": "147"}, "Update the dependencies array to be: [agentId, fetchValidStatuses]", {"range": "148", "text": "149"}, "Update the dependencies array to be: [selectedStatus, validateTransition]", {"range": "150", "text": "151"}, [1106, 1110], "[fetchAgentDetails, id]", [955, 1001], "[currentPage, statusFilter, sortBy, sortOrder, fetchAgents]", [1318, 1334], "[fetchDocuments, selectedStatus]", [574, 583], "[agentId, fetchWorkflowSummary]", [752, 761], "[agentId, fetchValidStatuses]", [850, 866], "[selectedStatus, validateTransition]"]