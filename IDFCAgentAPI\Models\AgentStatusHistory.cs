using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IDFCAgentAPI.Models
{
    public class AgentStatusHistory
    {
        [Key]
        public int HistoryId { get; set; }

        [Required]
        public int AgentId { get; set; }

        [Required]
        public AgentStatus FromStatus { get; set; }

        [Required]
        public AgentStatus ToStatus { get; set; }

        [StringLength(500)]
        public string? Comments { get; set; }

        [Required]
        [StringLength(255)]
        public string ChangedBy { get; set; } = string.Empty;

        public DateTime ChangedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        [ForeignKey("AgentId")]
        public virtual Agent Agent { get; set; } = null!;
    }
}
