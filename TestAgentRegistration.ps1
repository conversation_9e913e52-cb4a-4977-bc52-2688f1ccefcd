Write-Host "Testing Agent Registration API" -ForegroundColor Green

$apiUrl = "https://localhost:7093/api/agents/register"

# Test agent registration data
$agentData = @{
    firstName = "Jane"
    lastName = "Doe"
    email = "<EMAIL>"
    phoneNumber = "9876543210"
    aadharNumber = "123456789012"
    panNumber = "**********"
    address = "123 Main Street"
    city = "Mumbai"
    state = "Maharashtra"
    pinCode = "400001"
    dateOfBirth = "1990-01-01T00:00:00.000Z"
    username = "janedoe"
    password = "Password@123"
} | ConvertTo-Json

Write-Host "Sending registration request..." -ForegroundColor Yellow
Write-Host "Data: $agentData" -ForegroundColor Cyan

try {
    # Skip certificate validation for localhost testing
    [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}
    $response = Invoke-RestMethod -Uri $apiUrl -Method POST -Body $agentData -ContentType "application/json"
    Write-Host "Registration successful!" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
} catch {
    Write-Host "Registration failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "Status Code: $statusCode" -ForegroundColor Red
        
        # Try to read the response content
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseContent = $reader.ReadToEnd()
            Write-Host "Response Content: $responseContent" -ForegroundColor Red
        } catch {
            Write-Host "Could not read response content" -ForegroundColor Red
        }
    }
}

Write-Host "Testing completed!" -ForegroundColor Green
