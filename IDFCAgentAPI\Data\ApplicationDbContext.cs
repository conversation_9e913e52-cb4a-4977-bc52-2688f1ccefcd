using Microsoft.EntityFrameworkCore;
using IDFCAgentAPI.Models;

namespace IDFCAgentAPI.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Agent> Agents { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<AgentDocument> AgentDocuments { get; set; }
        public DbSet<AgentStatusHistory> AgentStatusHistories { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Agent configuration
            modelBuilder.Entity<Agent>(entity =>
            {
                entity.HasKey(e => e.AgentId);
                entity.Property(e => e.Email).HasMaxLength(255);
                entity.Property(e => e.AadharNumber).HasMaxLength(12);
                entity.Property(e => e.PanNumber).HasMaxLength(10);
                entity.HasIndex(e => e.Email).IsUnique();
                entity.HasIndex(e => e.AadharNumber).IsUnique();
                entity.HasIndex(e => e.PanNumber).IsUnique();
                entity.HasIndex(e => e.PhoneNumber).IsUnique();
            });

            // User configuration
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.UserId);
                entity.Property(e => e.Email).HasMaxLength(255);
                entity.Property(e => e.Username).HasMaxLength(100);
                entity.HasIndex(e => e.Email).IsUnique();
                entity.HasIndex(e => e.Username).IsUnique();
                
                // One-to-one relationship between User and Agent
                entity.HasOne(u => u.Agent)
                      .WithOne(a => a.User)
                      .HasForeignKey<User>(u => u.AgentId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // AgentDocument configuration
            modelBuilder.Entity<AgentDocument>(entity =>
            {
                entity.HasKey(e => e.DocumentId);
                entity.HasOne(d => d.Agent)
                      .WithMany(a => a.Documents)
                      .HasForeignKey(d => d.AgentId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // AgentStatusHistory configuration
            modelBuilder.Entity<AgentStatusHistory>(entity =>
            {
                entity.HasKey(e => e.HistoryId);
                entity.HasOne(h => h.Agent)
                      .WithMany(a => a.StatusHistory)
                      .HasForeignKey(h => h.AgentId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed admin user
            modelBuilder.Entity<User>().HasData(
                new User
                {
                    UserId = 1,
                    Username = "admin",
                    Email = "<EMAIL>",
                    PasswordHash = "$2a$11$8K1p/a0dL2LkzjU4Lq5uLOeIjLQH5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5u", // Admin@123
                    Role = UserRole.Admin,
                    IsActive = true,
                    CreatedAt = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc)
                }
            );
        }
    }
}
