using IDFCAgentAPI.DTOs;
using IDFCAgentAPI.Models;

namespace IDFCAgentAPI.Services
{
    public interface IAgentService
    {
        Task<AgentResponseDto> RegisterAgentAsync(AgentRegistrationDto registrationDto);
        Task<AgentResponseDto?> GetAgentByIdAsync(int agentId);
        Task<AgentResponseDto?> GetAgentByEmailAsync(string email);
        Task<IEnumerable<AgentResponseDto>> GetAllAgentsAsync();
        Task<IEnumerable<AgentResponseDto>> GetAgentsByStatusAsync(AgentStatus status);
        Task<AgentResponseDto?> UpdateAgentAsync(int agentId, AgentUpdateDto updateDto);
        Task<bool> UpdateAgentStatusAsync(int agentId, AgentStatus newStatus, string changedBy, string? comments = null);
        Task<bool> DeleteAgentAsync(int agentId);
        Task<IEnumerable<AgentStatusHistory>> GetAgentStatusHistoryAsync(int agentId);
        Task<bool> AgentExistsAsync(int agentId);
        Task<bool> EmailExistsAsync(string email);
        Task<bool> AadharExistsAsync(string aadharNumber);
        Task<bool> PanExistsAsync(string panNumber);
        Task<bool> PhoneExistsAsync(string phoneNumber);
    }
}
