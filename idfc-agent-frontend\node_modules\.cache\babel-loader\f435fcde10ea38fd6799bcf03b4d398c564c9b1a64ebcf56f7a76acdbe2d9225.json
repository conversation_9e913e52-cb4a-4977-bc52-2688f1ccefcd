{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\IDFCAgentOnboardingAndManagementSolution\\\\idfc-agent-frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Home, Users, UserPlus, User, LogOut, Menu, X, FileText } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Layout = () => {\n  _s();\n  const {\n    user,\n    logout,\n    isAgent,\n    isAdmin,\n    isReviewer\n  } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const handleLogout = async () => {\n    await logout();\n    navigate('/login');\n  };\n  const navigation = [{\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: Home,\n    show: true\n  }, {\n    name: 'Register Agent',\n    href: '/register',\n    icon: UserPlus,\n    show: isAgent()\n  }, {\n    name: 'Agents',\n    href: '/agents',\n    icon: Users,\n    show: isAdmin() || isReviewer()\n  }, {\n    name: 'Documents',\n    href: '/documents',\n    icon: FileText,\n    show: isAdmin() || isReviewer()\n  }, {\n    name: 'Profile',\n    href: '/profile',\n    icon: User,\n    show: true\n  }].filter(item => item.show);\n  const isActive = href => location.pathname === href;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `fixed inset-0 z-40 lg:hidden ${sidebarOpen ? '' : 'hidden'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n        onClick: () => setSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative flex-1 flex flex-col max-w-xs w-full bg-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 right-0 -mr-12 pt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n            onClick: () => setSidebarOpen(false),\n            children: /*#__PURE__*/_jsxDEV(X, {\n              className: \"h-6 w-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 h-0 pt-5 pb-4 overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0 flex items-center px-4\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: \"IDFC Agent Portal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"mt-5 px-2 space-y-1\",\n            children: navigation.map(item => {\n              const Icon = item.icon;\n              return /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                className: `${isActive(item.href) ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-2 py-2 text-base font-medium rounded-md`,\n                onClick: () => setSidebarOpen(false),\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  className: \"mr-4 h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 21\n                }, this), item.name]\n              }, item.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col min-h-0 bg-white border-r border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center flex-shrink-0 px-4\",\n            children: /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: \"IDFC Agent Portal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"mt-5 flex-1 px-2 space-y-1\",\n            children: navigation.map(item => {\n              const Icon = item.icon;\n              return /*#__PURE__*/_jsxDEV(Link, {\n                to: item.href,\n                className: `${isActive(item.href) ? 'bg-blue-100 text-blue-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'} group flex items-center px-2 py-2 text-sm font-medium rounded-md`,\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  className: \"mr-3 h-6 w-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 21\n                }, this), item.name]\n              }, item.name, true, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"lg:pl-64 flex flex-col flex-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sticky top-0 z-10 lg:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n          onClick: () => setSidebarOpen(true),\n          children: /*#__PURE__*/_jsxDEV(Menu, {\n            className: \"h-6 w-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow-sm border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between h-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: [\"Welcome, \", user === null || user === void 0 ? void 0 : user.username]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: [\"Role: \", user === null || user === void 0 ? void 0 : user.role]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleLogout,\n                className: \"flex items-center text-gray-500 hover:text-gray-700\",\n                children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                  className: \"h-5 w-5 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), \"Logout\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(Layout, \"xS3qBCwkHC2Yn2ZcnpKYSNSbVx8=\", false, function () {\n  return [useAuth, useLocation, useNavigate];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "useState", "Outlet", "Link", "useLocation", "useNavigate", "useAuth", "Home", "Users", "UserPlus", "User", "LogOut", "<PERSON><PERSON>", "X", "FileText", "jsxDEV", "_jsxDEV", "Layout", "_s", "user", "logout", "isAgent", "isAdmin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "location", "navigate", "sidebarOpen", "setSidebarOpen", "handleLogout", "navigation", "name", "href", "icon", "show", "filter", "item", "isActive", "pathname", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "Icon", "to", "username", "role", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/IDFCAgentOnboardingAndManagementSolution/idfc-agent-frontend/src/components/Layout.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport {\n  Home,\n  Users,\n  UserPlus,\n  User,\n  LogOut,\n  Menu,\n  X,\n  FileText\n} from 'lucide-react';\n\nconst Layout = () => {\n  const { user, logout, isAgent, isAdmin, isReviewer } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  const handleLogout = async () => {\n    await logout();\n    navigate('/login');\n  };\n\n  const navigation = [\n    { name: 'Dashboard', href: '/dashboard', icon: Home, show: true },\n    { name: 'Register Agent', href: '/register', icon: UserPlus, show: isAgent() },\n    { name: 'Agents', href: '/agents', icon: Users, show: isAdmin() || isReviewer() },\n    { name: 'Documents', href: '/documents', icon: FileText, show: isAdmin() || isReviewer() },\n    { name: 'Profile', href: '/profile', icon: User, show: true },\n  ].filter(item => item.show);\n\n  const isActive = (href) => location.pathname === href;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-40 lg:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)}></div>\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex-shrink-0 flex items-center px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">IDFC Agent Portal</h1>\n            </div>\n            <nav className=\"mt-5 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const Icon = item.icon;\n                return (\n                  <Link\n                    key={item.name}\n                    to={item.href}\n                    className={`${\n                      isActive(item.href)\n                        ? 'bg-blue-100 text-blue-900'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    } group flex items-center px-2 py-2 text-base font-medium rounded-md`}\n                    onClick={() => setSidebarOpen(false)}\n                  >\n                    <Icon className=\"mr-4 h-6 w-6\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0\">\n        <div className=\"flex-1 flex flex-col min-h-0 bg-white border-r border-gray-200\">\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <div className=\"flex items-center flex-shrink-0 px-4\">\n              <h1 className=\"text-xl font-bold text-gray-900\">IDFC Agent Portal</h1>\n            </div>\n            <nav className=\"mt-5 flex-1 px-2 space-y-1\">\n              {navigation.map((item) => {\n                const Icon = item.icon;\n                return (\n                  <Link\n                    key={item.name}\n                    to={item.href}\n                    className={`${\n                      isActive(item.href)\n                        ? 'bg-blue-100 text-blue-900'\n                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                    } group flex items-center px-2 py-2 text-sm font-medium rounded-md`}\n                  >\n                    <Icon className=\"mr-3 h-6 w-6\" />\n                    {item.name}\n                  </Link>\n                );\n              })}\n            </nav>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64 flex flex-col flex-1\">\n        <div className=\"sticky top-0 z-10 lg:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50\">\n          <button\n            className=\"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        {/* Top bar */}\n        <div className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between h-16\">\n              <div className=\"flex items-center\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">\n                  Welcome, {user?.username}\n                </h2>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-sm text-gray-500\">Role: {user?.role}</span>\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center text-gray-500 hover:text-gray-700\"\n                >\n                  <LogOut className=\"h-5 w-5 mr-1\" />\n                  Logout\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1\">\n          <div className=\"py-6\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              <Outlet />\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACzE,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,IAAI,EACJC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,CAAC,EACDC,QAAQ,QACH,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC,OAAO;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAChE,MAAMkB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM2B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMR,MAAM,CAAC,CAAC;IACdK,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAMI,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAEzB,IAAI;IAAE0B,IAAI,EAAE;EAAK,CAAC,EACjE;IAAEH,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAEvB,QAAQ;IAAEwB,IAAI,EAAEZ,OAAO,CAAC;EAAE,CAAC,EAC9E;IAAES,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAExB,KAAK;IAAEyB,IAAI,EAAEX,OAAO,CAAC,CAAC,IAAIC,UAAU,CAAC;EAAE,CAAC,EACjF;IAAEO,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAElB,QAAQ;IAAEmB,IAAI,EAAEX,OAAO,CAAC,CAAC,IAAIC,UAAU,CAAC;EAAE,CAAC,EAC1F;IAAEO,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAEtB,IAAI;IAAEuB,IAAI,EAAE;EAAK,CAAC,CAC9D,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACF,IAAI,CAAC;EAE3B,MAAMG,QAAQ,GAAIL,IAAI,IAAKP,QAAQ,CAACa,QAAQ,KAAKN,IAAI;EAErD,oBACEf,OAAA;IAAKsB,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCvB,OAAA;MAAKsB,SAAS,EAAE,gCAAgCZ,WAAW,GAAG,EAAE,GAAG,QAAQ,EAAG;MAAAa,QAAA,gBAC5EvB,OAAA;QAAKsB,SAAS,EAAC,yCAAyC;QAACE,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAAC,KAAK;MAAE;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrG5B,OAAA;QAAKsB,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBACrEvB,OAAA;UAAKsB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDvB,OAAA;YACEsB,SAAS,EAAC,gIAAgI;YAC1IE,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAAC,KAAK,CAAE;YAAAY,QAAA,eAErCvB,OAAA,CAACH,CAAC;cAACyB,SAAS,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5B,OAAA;UAAKsB,SAAS,EAAC,sCAAsC;UAAAC,QAAA,gBACnDvB,OAAA;YAAKsB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,eACnDvB,OAAA;cAAIsB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACN5B,OAAA;YAAKsB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EACjCV,UAAU,CAACgB,GAAG,CAAEV,IAAI,IAAK;cACxB,MAAMW,IAAI,GAAGX,IAAI,CAACH,IAAI;cACtB,oBACEhB,OAAA,CAACb,IAAI;gBAEH4C,EAAE,EAAEZ,IAAI,CAACJ,IAAK;gBACdO,SAAS,EAAE,GACTF,QAAQ,CAACD,IAAI,CAACJ,IAAI,CAAC,GACf,2BAA2B,GAC3B,oDAAoD,qEACY;gBACtES,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAAC,KAAK,CAAE;gBAAAY,QAAA,gBAErCvB,OAAA,CAAC8B,IAAI;kBAACR,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChCT,IAAI,CAACL,IAAI;cAAA,GAVLK,IAAI,CAACL,IAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWV,CAAC;YAEX,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5B,OAAA;MAAKsB,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEvB,OAAA;QAAKsB,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAC7EvB,OAAA;UAAKsB,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DvB,OAAA;YAAKsB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,eACnDvB,OAAA;cAAIsB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACN5B,OAAA;YAAKsB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EACxCV,UAAU,CAACgB,GAAG,CAAEV,IAAI,IAAK;cACxB,MAAMW,IAAI,GAAGX,IAAI,CAACH,IAAI;cACtB,oBACEhB,OAAA,CAACb,IAAI;gBAEH4C,EAAE,EAAEZ,IAAI,CAACJ,IAAK;gBACdO,SAAS,EAAE,GACTF,QAAQ,CAACD,IAAI,CAACJ,IAAI,CAAC,GACf,2BAA2B,GAC3B,oDAAoD,mEACU;gBAAAQ,QAAA,gBAEpEvB,OAAA,CAAC8B,IAAI;kBAACR,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChCT,IAAI,CAACL,IAAI;cAAA,GATLK,IAAI,CAACL,IAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUV,CAAC;YAEX,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5B,OAAA;MAAKsB,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC5CvB,OAAA;QAAKsB,SAAS,EAAC,kEAAkE;QAAAC,QAAA,eAC/EvB,OAAA;UACEsB,SAAS,EAAC,qLAAqL;UAC/LE,OAAO,EAAEA,CAAA,KAAMb,cAAc,CAAC,IAAI,CAAE;UAAAY,QAAA,eAEpCvB,OAAA,CAACJ,IAAI;YAAC0B,SAAS,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN5B,OAAA;QAAKsB,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1DvB,OAAA;UAAKsB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnCvB,OAAA;YAAKsB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxCvB,OAAA;cAAKsB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,eAChCvB,OAAA;gBAAIsB,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,GAAC,WACzC,EAACpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,QAAQ;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACN5B,OAAA;cAAKsB,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CvB,OAAA;gBAAMsB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,QAAM,EAACpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,IAAI;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjE5B,OAAA;gBACEwB,OAAO,EAAEZ,YAAa;gBACtBU,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,gBAE/DvB,OAAA,CAACL,MAAM;kBAAC2B,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAErC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5B,OAAA;QAAMsB,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACtBvB,OAAA;UAAKsB,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBvB,OAAA;YAAKsB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,eACrDvB,OAAA,CAACd,MAAM;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA1IID,MAAM;EAAA,QAC6CX,OAAO,EAC7CF,WAAW,EACXC,WAAW;AAAA;AAAA6C,EAAA,GAHxBjC,MAAM;AA4IZ,eAAeA,MAAM;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}