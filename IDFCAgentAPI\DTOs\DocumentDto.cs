using IDFCAgentAPI.Models;
using System.ComponentModel.DataAnnotations;

namespace IDFCAgentAPI.DTOs
{
    public class DocumentUploadDto
    {
        [Required]
        public DocumentType DocumentType { get; set; }
        
        [Required]
        public IFormFile File { get; set; } = null!;
    }

    public class DocumentResponseDto
    {
        public int DocumentId { get; set; }
        public int AgentId { get; set; }
        public DocumentType DocumentType { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FileSize { get; set; } = string.Empty;
        public string MimeType { get; set; } = string.Empty;
        public DocumentStatus Status { get; set; }
        public string? ReviewComments { get; set; }
        public DateTime UploadedAt { get; set; }
        public DateTime? ReviewedAt { get; set; }
        public string? ReviewedBy { get; set; }
        public string? AgentName { get; set; }
    }

    public class DocumentStatusUpdateDto
    {
        [Required]
        public DocumentStatus Status { get; set; }
        
        [StringLength(500)]
        public string? Comments { get; set; }
    }

    public class DocumentReviewDto
    {
        public int DocumentId { get; set; }
        public int AgentId { get; set; }
        public string AgentName { get; set; } = string.Empty;
        public DocumentType DocumentType { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FileSize { get; set; } = string.Empty;
        public DocumentStatus Status { get; set; }
        public DateTime UploadedAt { get; set; }
        public string? ReviewComments { get; set; }
        public DateTime? ReviewedAt { get; set; }
        public string? ReviewedBy { get; set; }
        public bool CanDownload { get; set; }
        public bool CanReview { get; set; }
    }

    public class DocumentSummaryDto
    {
        public DocumentType DocumentType { get; set; }
        public string DocumentTypeName { get; set; } = string.Empty;
        public DocumentStatus Status { get; set; }
        public string StatusName { get; set; } = string.Empty;
        public bool IsRequired { get; set; }
        public bool IsUploaded { get; set; }
        public DateTime? UploadedAt { get; set; }
        public string? ReviewComments { get; set; }
    }

    public class BulkDocumentActionDto
    {
        [Required]
        public List<int> DocumentIds { get; set; } = new();
        
        [Required]
        public DocumentStatus Status { get; set; }
        
        [StringLength(500)]
        public string? Comments { get; set; }
    }
}
