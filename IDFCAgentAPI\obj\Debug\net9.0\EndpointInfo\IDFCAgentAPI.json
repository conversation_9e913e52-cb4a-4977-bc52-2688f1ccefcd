{"openapi": "3.0.4", "info": {"title": "IDFC Agent Management API", "description": "API for IDFC FASTag TSP Agent Onboarding and Management System", "version": "v1"}, "paths": {"/api/Agents/register": {"post": {"tags": ["Agents"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentRegistrationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentRegistrationDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AgentRegistrationDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AgentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AgentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentResponseDto"}}}}}}}, "/api/Agents/{id}": {"get": {"tags": ["Agents"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AgentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AgentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentResponseDto"}}}}}}, "put": {"tags": ["Agents"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AgentUpdateDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AgentResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AgentResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AgentResponseDto"}}}}}}, "delete": {"tags": ["Agents"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Agents": {"get": {"tags": ["Agents"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentResponseDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentResponseDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentResponseDto"}}}}}}}}, "/api/Agents/status/{status}": {"get": {"tags": ["Agents"], "parameters": [{"name": "status", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/AgentStatus"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentResponseDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentResponseDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentResponseDto"}}}}}}}}, "/api/Agents/{id}/status": {"patch": {"tags": ["Agents"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateStatusDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Agents/{id}/status-history": {"get": {"tags": ["Agents"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentStatusHistory"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentStatusHistory"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AgentStatusHistory"}}}}}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}}}}}}, "/api/Auth/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}}}}}}, "/api/Auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/profile": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDto"}}}}}}}}, "components": {"schemas": {"Agent": {"required": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dateOfBirth", "email", "firstName", "lastName", "panNumber", "phoneNumber", "status"], "type": "object", "properties": {"agentId": {"type": "integer", "format": "int32"}, "firstName": {"maxLength": 100, "minLength": 0, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 0, "type": "string"}, "email": {"maxLength": 255, "minLength": 0, "type": "string", "format": "email"}, "phoneNumber": {"maxLength": 15, "minLength": 0, "type": "string", "format": "tel"}, "aadharNumber": {"maxLength": 12, "minLength": 0, "type": "string"}, "panNumber": {"maxLength": 10, "minLength": 0, "type": "string"}, "address": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "city": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "state": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "pinCode": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time"}, "status": {"$ref": "#/components/schemas/AgentStatus"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "updatedBy": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "documents": {"type": "array", "items": {"$ref": "#/components/schemas/AgentDocument"}, "nullable": true}, "statusHistory": {"type": "array", "items": {"$ref": "#/components/schemas/AgentStatusHistory"}, "nullable": true}, "user": {"$ref": "#/components/schemas/User"}}, "additionalProperties": false}, "AgentDocument": {"required": ["agentId", "documentType", "fileName", "filePath"], "type": "object", "properties": {"documentId": {"type": "integer", "format": "int32"}, "agentId": {"type": "integer", "format": "int32"}, "documentType": {"$ref": "#/components/schemas/DocumentType"}, "fileName": {"maxLength": 255, "minLength": 0, "type": "string"}, "filePath": {"maxLength": 500, "minLength": 0, "type": "string"}, "fileSize": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "mimeType": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "status": {"$ref": "#/components/schemas/DocumentStatus"}, "reviewComments": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "uploadedAt": {"type": "string", "format": "date-time"}, "reviewedAt": {"type": "string", "format": "date-time", "nullable": true}, "reviewedBy": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "agent": {"$ref": "#/components/schemas/Agent"}}, "additionalProperties": false}, "AgentRegistrationDto": {"required": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "address", "city", "dateOfBirth", "email", "firstName", "lastName", "panNumber", "password", "phoneNumber", "pinCode", "state", "username"], "type": "object", "properties": {"firstName": {"maxLength": 100, "minLength": 0, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 0, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}, "phoneNumber": {"minLength": 1, "type": "string", "format": "tel"}, "aadharNumber": {"maxLength": 12, "minLength": 12, "type": "string"}, "panNumber": {"maxLength": 10, "minLength": 10, "type": "string"}, "address": {"minLength": 1, "type": "string"}, "city": {"minLength": 1, "type": "string"}, "state": {"minLength": 1, "type": "string"}, "pinCode": {"maxLength": 6, "minLength": 6, "type": "string"}, "dateOfBirth": {"type": "string", "format": "date-time"}, "username": {"maxLength": 100, "minLength": 0, "type": "string"}, "password": {"maxLength": 100, "minLength": 6, "type": "string"}}, "additionalProperties": false}, "AgentResponseDto": {"type": "object", "properties": {"agentId": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "aadharNumber": {"type": "string", "nullable": true}, "panNumber": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "pinCode": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AgentStatus": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "AgentStatusHistory": {"required": ["agentId", "changedBy", "fromStatus", "to<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"historyId": {"type": "integer", "format": "int32"}, "agentId": {"type": "integer", "format": "int32"}, "fromStatus": {"$ref": "#/components/schemas/AgentStatus"}, "toStatus": {"$ref": "#/components/schemas/AgentStatus"}, "comments": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}, "changedBy": {"maxLength": 255, "minLength": 0, "type": "string"}, "changedAt": {"type": "string", "format": "date-time"}, "agent": {"$ref": "#/components/schemas/Agent"}}, "additionalProperties": false}, "AgentUpdateDto": {"type": "object", "properties": {"firstName": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "lastName": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "email": {"type": "string", "format": "email", "nullable": true}, "phoneNumber": {"type": "string", "format": "tel", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "pinCode": {"maxLength": 6, "minLength": 6, "type": "string", "nullable": true}}, "additionalProperties": false}, "ChangePasswordDto": {"required": ["confirmPassword", "currentPassword", "newPassword"], "type": "object", "properties": {"currentPassword": {"minLength": 1, "type": "string"}, "newPassword": {"maxLength": 100, "minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "DocumentStatus": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "DocumentType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7], "type": "integer", "format": "int32"}, "LoginDto": {"required": ["password", "username"], "type": "object", "properties": {"username": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "LoginResponseDto": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "user": {"$ref": "#/components/schemas/UserDto"}, "expiresAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "RefreshTokenDto": {"required": ["refreshToken"], "type": "object", "properties": {"refreshToken": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "UpdateStatusDto": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/AgentStatus"}, "comments": {"type": "string", "nullable": true}}, "additionalProperties": false}, "User": {"required": ["email", "passwordHash", "role", "username"], "type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "username": {"maxLength": 100, "minLength": 0, "type": "string"}, "email": {"maxLength": 255, "minLength": 0, "type": "string", "format": "email"}, "passwordHash": {"minLength": 1, "type": "string"}, "role": {"$ref": "#/components/schemas/UserRole"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "lastLoginAt": {"type": "string", "format": "date-time"}, "agentId": {"type": "integer", "format": "int32", "nullable": true}, "agent": {"$ref": "#/components/schemas/Agent"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "agentId": {"type": "integer", "format": "int32", "nullable": true}, "lastLoginAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UserRole": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Enter 'Bearer' [space] and then your token in the text input below.", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}