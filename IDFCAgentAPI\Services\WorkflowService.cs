using IDFCAgentAPI.Data;
using IDFCAgentAPI.Models;
using Microsoft.EntityFrameworkCore;

namespace IDFCAgentAPI.Services
{
    public class WorkflowService : IWorkflowService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<WorkflowService> _logger;

        // Define workflow steps and their requirements
        private readonly Dictionary<AgentStatus, WorkflowStepDefinition> _workflowSteps = new()
        {
            {
                AgentStatus.Pending,
                new WorkflowStepDefinition
                {
                    Name = "Application Submitted",
                    Description = "Agent has submitted their application",
                    RequiredDocuments = new List<DocumentType>(),
                    RequiresManualReview = false,
                    EstimatedDuration = TimeSpan.FromHours(1),
                    NextStatuses = new List<AgentStatus> { AgentStatus.UnderReview }
                }
            },
            {
                AgentStatus.UnderReview,
                new WorkflowStepDefinition
                {
                    Name = "Under Review",
                    Description = "Application and documents are being reviewed",
                    RequiredDocuments = new List<DocumentType> 
                    { 
                        DocumentType.AadharCard, 
                        DocumentType.PanCard, 
                        DocumentType.Photo, 
                        DocumentType.BankPassbook 
                    },
                    RequiresManualReview = true,
                    EstimatedDuration = TimeSpan.FromDays(2),
                    NextStatuses = new List<AgentStatus> { AgentStatus.Approved, AgentStatus.Rejected }
                }
            },
            {
                AgentStatus.Approved,
                new WorkflowStepDefinition
                {
                    Name = "Approved",
                    Description = "Application has been approved",
                    RequiredDocuments = new List<DocumentType>(),
                    RequiresManualReview = false,
                    EstimatedDuration = TimeSpan.FromHours(2),
                    NextStatuses = new List<AgentStatus> { AgentStatus.Active }
                }
            },
            {
                AgentStatus.Active,
                new WorkflowStepDefinition
                {
                    Name = "Active",
                    Description = "Agent is active and operational",
                    RequiredDocuments = new List<DocumentType>(),
                    RequiresManualReview = false,
                    EstimatedDuration = TimeSpan.Zero,
                    NextStatuses = new List<AgentStatus> { AgentStatus.Inactive, AgentStatus.Suspended }
                }
            },
            {
                AgentStatus.Rejected,
                new WorkflowStepDefinition
                {
                    Name = "Rejected",
                    Description = "Application has been rejected",
                    RequiredDocuments = new List<DocumentType>(),
                    RequiresManualReview = false,
                    EstimatedDuration = TimeSpan.Zero,
                    NextStatuses = new List<AgentStatus>()
                }
            },
            {
                AgentStatus.Inactive,
                new WorkflowStepDefinition
                {
                    Name = "Inactive",
                    Description = "Agent is temporarily inactive",
                    RequiredDocuments = new List<DocumentType>(),
                    RequiresManualReview = true,
                    EstimatedDuration = TimeSpan.Zero,
                    NextStatuses = new List<AgentStatus> { AgentStatus.Active }
                }
            },
            {
                AgentStatus.Suspended,
                new WorkflowStepDefinition
                {
                    Name = "Suspended",
                    Description = "Agent has been suspended",
                    RequiredDocuments = new List<DocumentType>(),
                    RequiresManualReview = true,
                    EstimatedDuration = TimeSpan.Zero,
                    NextStatuses = new List<AgentStatus> { AgentStatus.Active, AgentStatus.Inactive }
                }
            }
        };

        public WorkflowService(ApplicationDbContext context, ILogger<WorkflowService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<bool> CanTransitionToStatusAsync(int agentId, AgentStatus fromStatus, AgentStatus toStatus)
        {
            if (!_workflowSteps.ContainsKey(fromStatus))
                return false;

            var currentStep = _workflowSteps[fromStatus];
            return currentStep.NextStatuses.Contains(toStatus);
        }

        public async Task<WorkflowValidationResult> ValidateStatusTransitionAsync(int agentId, AgentStatus newStatus)
        {
            var result = new WorkflowValidationResult { IsValid = true };

            try
            {
                var agent = await _context.Agents
                    .Include(a => a.Documents)
                    .FirstOrDefaultAsync(a => a.AgentId == agentId);

                if (agent == null)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "Agent not found";
                    return result;
                }

                // Check if transition is allowed
                if (!await CanTransitionToStatusAsync(agentId, agent.Status, newStatus))
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"Cannot transition from {agent.Status} to {newStatus}";
                    return result;
                }

                // Check document requirements for the new status
                if (_workflowSteps.ContainsKey(newStatus))
                {
                    var stepDef = _workflowSteps[newStatus];
                    var missingDocs = new List<string>();

                    foreach (var requiredDocType in stepDef.RequiredDocuments)
                    {
                        var document = agent.Documents.FirstOrDefault(d => d.DocumentType == requiredDocType);
                        if (document == null)
                        {
                            missingDocs.Add(requiredDocType.ToString());
                        }
                        else if (document.Status != DocumentStatus.Approved)
                        {
                            result.RequiredActions.Add($"Document {requiredDocType} must be approved");
                        }
                    }

                    if (missingDocs.Any())
                    {
                        result.MissingDocuments = missingDocs;
                        result.RequiredActions.Add($"Upload missing documents: {string.Join(", ", missingDocs)}");
                    }

                    result.RequiresManualReview = stepDef.RequiresManualReview;
                }

                // Additional validation rules
                switch (newStatus)
                {
                    case AgentStatus.Approved:
                        // All required documents must be approved
                        var requiredDocs = new[] { DocumentType.AadharCard, DocumentType.PanCard, DocumentType.Photo, DocumentType.BankPassbook };
                        foreach (var docType in requiredDocs)
                        {
                            var doc = agent.Documents.FirstOrDefault(d => d.DocumentType == docType);
                            if (doc == null || doc.Status != DocumentStatus.Approved)
                            {
                                result.IsValid = false;
                                result.ErrorMessage = $"All required documents must be approved before approval";
                                break;
                            }
                        }
                        break;

                    case AgentStatus.Active:
                        // Agent must be approved first
                        if (agent.Status != AgentStatus.Approved)
                        {
                            result.IsValid = false;
                            result.ErrorMessage = "Agent must be approved before becoming active";
                        }
                        break;
                }

                if (result.RequiredActions.Any() && newStatus == AgentStatus.Approved)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "Cannot approve agent with pending requirements";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error validating status transition for agent {agentId}");
                result.IsValid = false;
                result.ErrorMessage = "An error occurred during validation";
            }

            return result;
        }

        public async Task<bool> ProcessStatusTransitionAsync(int agentId, AgentStatus newStatus, string changedBy, string? comments = null)
        {
            try
            {
                var validation = await ValidateStatusTransitionAsync(agentId, newStatus);
                if (!validation.IsValid)
                {
                    _logger.LogWarning($"Status transition validation failed for agent {agentId}: {validation.ErrorMessage}");
                    return false;
                }

                var agent = await _context.Agents.FindAsync(agentId);
                if (agent == null) return false;

                var oldStatus = agent.Status;
                agent.Status = newStatus;
                agent.UpdatedAt = DateTime.UtcNow;
                agent.UpdatedBy = changedBy;

                // Create status history record
                var statusHistory = new AgentStatusHistory
                {
                    AgentId = agentId,
                    FromStatus = oldStatus,
                    ToStatus = newStatus,
                    Comments = comments,
                    ChangedBy = changedBy,
                    ChangedAt = DateTime.UtcNow
                };

                _context.AgentStatusHistories.Add(statusHistory);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Agent {agentId} status changed from {oldStatus} to {newStatus} by {changedBy}");

                // Try to auto-progress if possible
                await AutoProgressWorkflowAsync(agentId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing status transition for agent {agentId}");
                return false;
            }
        }

        public async Task<bool> AutoProgressWorkflowAsync(int agentId)
        {
            try
            {
                var agent = await _context.Agents
                    .Include(a => a.Documents)
                    .FirstOrDefaultAsync(a => a.AgentId == agentId);

                if (agent == null) return false;

                bool progressMade = false;

                // Auto-progress logic based on current status
                switch (agent.Status)
                {
                    case AgentStatus.Pending:
                        // Auto-progress to UnderReview if basic info is complete
                        if (!string.IsNullOrEmpty(agent.FirstName) && !string.IsNullOrEmpty(agent.Email))
                        {
                            await ProcessStatusTransitionAsync(agentId, AgentStatus.UnderReview, "System", "Auto-progressed to review");
                            progressMade = true;
                        }
                        break;

                    case AgentStatus.Approved:
                        // Auto-progress to Active after approval
                        await ProcessStatusTransitionAsync(agentId, AgentStatus.Active, "System", "Auto-activated after approval");
                        progressMade = true;
                        break;
                }

                return progressMade;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in auto-progress workflow for agent {agentId}");
                return false;
            }
        }

        public async Task<WorkflowStepResult> GetNextWorkflowStepAsync(int agentId)
        {
            var agent = await _context.Agents.FindAsync(agentId);
            if (agent == null)
                throw new ArgumentException("Agent not found");

            var currentStep = _workflowSteps[agent.Status];
            var nextStatus = currentStep.NextStatuses.FirstOrDefault();

            return new WorkflowStepResult
            {
                StepName = currentStep.Name,
                Description = currentStep.Description,
                CurrentStatus = agent.Status,
                NextStatus = nextStatus,
                CanAutoProgress = !currentStep.RequiresManualReview,
                RequiredActions = await GetRequiredActionsAsync(agentId, nextStatus),
                EstimatedDuration = currentStep.EstimatedDuration
            };
        }

        public async Task<IEnumerable<AgentStatus>> GetValidNextStatusesAsync(int agentId)
        {
            var agent = await _context.Agents.FindAsync(agentId);
            if (agent == null) return new List<AgentStatus>();

            if (_workflowSteps.ContainsKey(agent.Status))
            {
                return _workflowSteps[agent.Status].NextStatuses;
            }

            return new List<AgentStatus>();
        }

        public async Task<WorkflowSummary> GetWorkflowSummaryAsync(int agentId)
        {
            var agent = await _context.Agents
                .Include(a => a.StatusHistory)
                .Include(a => a.Documents)
                .FirstOrDefaultAsync(a => a.AgentId == agentId);

            if (agent == null)
                throw new ArgumentException("Agent not found");

            var allStatuses = new[] { AgentStatus.Pending, AgentStatus.UnderReview, AgentStatus.Approved, AgentStatus.Active };
            var completedSteps = 0;
            var steps = new List<WorkflowStep>();

            foreach (var status in allStatuses)
            {
                var statusHistory = agent.StatusHistory.FirstOrDefault(h => h.ToStatus == status);
                var isCompleted = statusHistory != null;
                var isCurrent = agent.Status == status;

                if (isCompleted) completedSteps++;

                steps.Add(new WorkflowStep
                {
                    Name = _workflowSteps[status].Name,
                    Status = status,
                    IsCompleted = isCompleted,
                    IsCurrent = isCurrent,
                    CompletedAt = statusHistory?.ChangedAt,
                    CompletedBy = statusHistory?.ChangedBy,
                    Requirements = GetStepRequirements(status),
                    RequiresManualReview = _workflowSteps[status].RequiresManualReview
                });
            }

            var totalTimeSpent = agent.StatusHistory.Any() 
                ? DateTime.UtcNow - agent.CreatedAt 
                : TimeSpan.Zero;

            return new WorkflowSummary
            {
                AgentId = agentId,
                CurrentStatus = agent.Status,
                CurrentStepName = _workflowSteps[agent.Status].Name,
                CompletedSteps = completedSteps,
                TotalSteps = allStatuses.Length,
                ProgressPercentage = (double)completedSteps / allStatuses.Length * 100,
                Steps = steps,
                TotalTimeSpent = totalTimeSpent,
                EstimatedRemainingTime = await GetEstimatedCompletionTimeAsync(agentId),
                IsBlocked = await IsWorkflowBlockedAsync(agentId),
                BlockedReason = await GetBlockedReasonAsync(agentId)
            };
        }

        public async Task<bool> IsWorkflowCompleteAsync(int agentId)
        {
            var agent = await _context.Agents.FindAsync(agentId);
            return agent?.Status == AgentStatus.Active;
        }

        public async Task<bool> RequiresManualReviewAsync(int agentId)
        {
            var agent = await _context.Agents.FindAsync(agentId);
            if (agent == null) return false;

            return _workflowSteps.ContainsKey(agent.Status) && _workflowSteps[agent.Status].RequiresManualReview;
        }

        public async Task<TimeSpan> GetEstimatedCompletionTimeAsync(int agentId)
        {
            var agent = await _context.Agents.FindAsync(agentId);
            if (agent == null) return TimeSpan.Zero;

            var remainingSteps = _workflowSteps
                .Where(kvp => (int)kvp.Key > (int)agent.Status)
                .Select(kvp => kvp.Value);

            return TimeSpan.FromTicks(remainingSteps.Sum(step => step.EstimatedDuration.Ticks));
        }

        private async Task<List<string>> GetRequiredActionsAsync(int agentId, AgentStatus? targetStatus)
        {
            var actions = new List<string>();
            
            if (targetStatus.HasValue)
            {
                var validation = await ValidateStatusTransitionAsync(agentId, targetStatus.Value);
                actions.AddRange(validation.RequiredActions);
            }

            return actions;
        }

        private List<string> GetStepRequirements(AgentStatus status)
        {
            if (!_workflowSteps.ContainsKey(status))
                return new List<string>();

            var step = _workflowSteps[status];
            var requirements = new List<string>();

            foreach (var docType in step.RequiredDocuments)
            {
                requirements.Add($"Upload and approve {docType}");
            }

            if (step.RequiresManualReview)
            {
                requirements.Add("Manual review required");
            }

            return requirements;
        }

        private async Task<bool> IsWorkflowBlockedAsync(int agentId)
        {
            var validation = await ValidateStatusTransitionAsync(agentId, AgentStatus.Approved);
            return !validation.IsValid && validation.RequiredActions.Any();
        }

        private async Task<string?> GetBlockedReasonAsync(int agentId)
        {
            var validation = await ValidateStatusTransitionAsync(agentId, AgentStatus.Approved);
            return validation.IsValid ? null : validation.ErrorMessage;
        }
    }

    public class WorkflowStepDefinition
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<DocumentType> RequiredDocuments { get; set; } = new();
        public bool RequiresManualReview { get; set; }
        public TimeSpan EstimatedDuration { get; set; }
        public List<AgentStatus> NextStatuses { get; set; } = new();
    }
}
