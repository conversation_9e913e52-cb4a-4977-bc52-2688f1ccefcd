﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace IDFCAgentAPI.Migrations
{
    /// <inheritdoc />
    public partial class FixSeedData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "UserId",
                keyValue: 1,
                columns: new[] { "CreatedAt", "PasswordHash" },
                values: new object[] { new DateTime(2024, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc), "$2a$11$8K1p/a0dL2LkzjU4Lq5uLOeIjLQH5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5Z5u" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "UserId",
                keyValue: 1,
                columns: new[] { "CreatedAt", "PasswordHash" },
                values: new object[] { new DateTime(2025, 7, 2, 12, 37, 19, 597, DateTimeKind.Utc).AddTicks(9109), "$2a$11$UKtOYZ/r46orA7jd2Ou9pOa/AnXCftbdA4D7l1VQP.sYTPY4vBGMG" });
        }
    }
}
