{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.75 7.09a3 3 0 0 1 2.16 2.16\",\n  key: \"1d4wjd\"\n}], [\"path\", {\n  d: \"M17.072 17.072c-1.634 2.17-3.527 3.912-4.471 4.727a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 1.432-4.568\",\n  key: \"12yil7\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M8.475 2.818A8 8 0 0 1 20 10c0 1.183-.31 2.377-.81 3.533\",\n  key: \"lhrkcz\"\n}], [\"path\", {\n  d: \"M9.13 9.13a3 3 0 0 0 3.74 3.74\",\n  key: \"13wojd\"\n}]];\nconst MapPinOff = createLucideIcon(\"map-pin-off\", __iconNode);\nexport { __iconNode, MapPinOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "MapPinOff", "createLucideIcon"], "sources": ["D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\node_modules\\lucide-react\\src\\icons\\map-pin-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12.75 7.09a3 3 0 0 1 2.16 2.16', key: '1d4wjd' }],\n  [\n    'path',\n    {\n      d: 'M17.072 17.072c-1.634 2.17-3.527 3.912-4.471 4.727a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 1.432-4.568',\n      key: '12yil7',\n    },\n  ],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n  ['path', { d: 'M8.475 2.818A8 8 0 0 1 20 10c0 1.183-.31 2.377-.81 3.533', key: 'lhrkcz' }],\n  ['path', { d: 'M9.13 9.13a3 3 0 0 0 3.74 3.74', key: '13wojd' }],\n];\n\n/**\n * @component @name MapPinOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuNzUgNy4wOWEzIDMgMCAwIDEgMi4xNiAyLjE2IiAvPgogIDxwYXRoIGQ9Ik0xNy4wNzIgMTcuMDcyYy0xLjYzNCAyLjE3LTMuNTI3IDMuOTEyLTQuNDcxIDQuNzI3YTEgMSAwIDAgMS0xLjIwMiAwQzkuNTM5IDIwLjE5MyA0IDE0Ljk5MyA0IDEwYTggOCAwIDAgMSAxLjQzMi00LjU2OCIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgogIDxwYXRoIGQ9Ik04LjQ3NSAyLjgxOEE4IDggMCAwIDEgMjAgMTBjMCAxLjE4My0uMzEgMi4zNzctLjgxIDMuNTMzIiAvPgogIDxwYXRoIGQ9Ik05LjEzIDkuMTNhMyAzIDAgMCAwIDMuNzQgMy43NCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/map-pin-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MapPinOff = createLucideIcon('map-pin-off', __iconNode);\n\nexport default MapPinOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,iCAAmC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,0DAA4D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzF,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAU,GACjE;AAaM,MAAAC,SAAA,GAAYC,gBAAiB,gBAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}