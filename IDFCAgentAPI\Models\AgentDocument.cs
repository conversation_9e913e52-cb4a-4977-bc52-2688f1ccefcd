using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IDFCAgentAPI.Models
{
    public class AgentDocument
    {
        [Key]
        public int DocumentId { get; set; }

        [Required]
        public int AgentId { get; set; }

        [Required]
        public DocumentType DocumentType { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [StringLength(100)]
        public string FileSize { get; set; } = string.Empty;

        [StringLength(50)]
        public string MimeType { get; set; } = string.Empty;

        public DocumentStatus Status { get; set; } = DocumentStatus.Pending;

        [StringLength(500)]
        public string? ReviewComments { get; set; }

        public DateTime UploadedAt { get; set; } = DateTime.UtcNow;
        public DateTime? ReviewedAt { get; set; }

        [StringLength(255)]
        public string? ReviewedBy { get; set; }

        // Navigation properties
        [ForeignKey("AgentId")]
        public virtual Agent Agent { get; set; } = null!;
    }

    public enum DocumentType
    {
        AadharCard = 0,
        PanCard = 1,
        Photo = 2,
        BankPassbook = 3,
        AddressProof = 4,
        BusinessRegistration = 5,
        GSTCertificate = 6,
        Other = 7
    }

    public enum DocumentStatus
    {
        Pending = 0,
        UnderReview = 1,
        Approved = 2,
        Rejected = 3,
        ResubmissionRequired = 4
    }
}
