Write-Host "Testing Complete Agent Registration with Document Upload" -ForegroundColor Green

# Skip certificate validation for localhost testing
[System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}

$apiUrl = "https://localhost:7093/api/agents/register"

# Test agent registration data
$agentData = @{
    firstName = "Bob"
    lastName = "Johnson"
    email = "<EMAIL>"
    phoneNumber = "9876543212"
    aadharNumber = "123456789014"
    panNumber = "**********"
    address = "456 Oak Street"
    city = "Delhi"
    state = "Delhi"
    pinCode = "110001"
    dateOfBirth = "1992-05-15T00:00:00.000Z"
    username = "bob<PERSON><PERSON><PERSON>"
    password = "Password@123"
} | ConvertTo-Json

Write-Host "Step 1: Registering agent..." -ForegroundColor Yellow
Write-Host "Data: $agentData" -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri $apiUrl -Method POST -Body $agentData -ContentType "application/json"
    Write-Host "Registration successful!" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
    
    $agentId = $response.agentId
    Write-Host "Agent ID: $agentId" -ForegroundColor Green
    
    # Step 2: Login as admin to get authentication token
    Write-Host "`nStep 2: Logging in as admin..." -ForegroundColor Yellow

    $adminLogin = @{
        username = "admin"
        password = "Admin@123"
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "https://localhost:7093/api/auth/login" -Method POST -Body $adminLogin -ContentType "application/json"
    $token = $loginResponse.token
    $headers = @{ Authorization = "Bearer $token" }
    Write-Host "Admin login successful!" -ForegroundColor Green

    # Step 3: Test document upload
    Write-Host "`nStep 3: Testing document upload..." -ForegroundColor Yellow

    # Create a test file
    $testFilePath = "test-document.txt"
    "This is a test document for agent $agentId" | Out-File -FilePath $testFilePath -Encoding UTF8

    # Upload document
    $documentUploadUrl = "https://localhost:7093/api/documents/agents/$agentId/upload"

    # Create multipart form data
    $boundary = [System.Guid]::NewGuid().ToString()
    $LF = "`r`n"

    $fileBytes = [System.IO.File]::ReadAllBytes($testFilePath)
    $fileEnc = [System.Text.Encoding]::GetEncoding('iso-8859-1').GetString($fileBytes)

    $bodyLines = (
        "--$boundary",
        "Content-Disposition: form-data; name=`"file`"; filename=`"test-document.txt`"",
        "Content-Type: text/plain$LF",
        $fileEnc,
        "--$boundary",
        "Content-Disposition: form-data; name=`"documentType`"$LF",
        "AadharCard",
        "--$boundary--$LF"
    ) -join $LF

    try {
        $documentResponse = Invoke-RestMethod -Uri $documentUploadUrl -Method POST -Body $bodyLines -ContentType "multipart/form-data; boundary=$boundary" -Headers $headers
        Write-Host "Document upload successful!" -ForegroundColor Green
        Write-Host "Document Response: $($documentResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
    } catch {
        Write-Host "Document upload failed!" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }

    # Clean up test file
    Remove-Item $testFilePath -ErrorAction SilentlyContinue

    # Step 4: Test agents list API
    Write-Host "`nStep 4: Testing agents list API..." -ForegroundColor Yellow
    
    $agentsResponse = Invoke-RestMethod -Uri "https://localhost:7093/api/agents" -Method GET -Headers $headers
    Write-Host "Agents found: $($agentsResponse.Count)" -ForegroundColor Green
    $agentsResponse | ForEach-Object {
        Write-Host "Agent: $($_.firstName) $($_.lastName) - $($_.email) - Status: $($_.status)" -ForegroundColor Cyan
    }
    
    # Step 5: Check documents for the agent
    Write-Host "`nStep 5: Checking documents for agent $agentId..." -ForegroundColor Yellow
    
    $documentsResponse = Invoke-RestMethod -Uri "https://localhost:7093/api/documents/agents/$agentId" -Method GET -Headers $headers
    Write-Host "Documents found: $($documentsResponse.Count)" -ForegroundColor Green
    $documentsResponse | ForEach-Object {
        Write-Host "Document: $($_.documentType) - $($_.fileName) - Status: $($_.status)" -ForegroundColor Cyan
    }
    
} catch {
    Write-Host "Registration failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "Status Code: $statusCode" -ForegroundColor Red
        
        # Try to read the response content
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseContent = $reader.ReadToEnd()
            Write-Host "Response Content: $responseContent" -ForegroundColor Red
        } catch {
            Write-Host "Could not read response content" -ForegroundColor Red
        }
    }
}

Write-Host "`nTesting completed!" -ForegroundColor Green
