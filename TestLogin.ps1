# Test login functionality with the fixed password hashes
Write-Host "Testing IDFC Agent API Login Functionality" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

# Test data
$apiUrl = "https://localhost:7001"
$adminCredentials = @{
    username = "admin"
    password = "Admin@123"
}

$reviewerCredentials = @{
    username = "reviewer"
    password = "Reviewer@123"
}

# Function to test login
function Test-Login {
    param(
        [string]$Username,
        [string]$Password,
        [string]$UserType
    )
    
    Write-Host "`nTesting $UserType login..." -ForegroundColor Yellow
    Write-Host "Username: $Username"
    Write-Host "Password: $Password"
    
    try {
        $loginData = @{
            username = $Username
            password = $Password
        } | ConvertTo-Json
        
        $response = Invoke-RestMethod -Uri "$apiUrl/api/auth/login" -Method POST -Body $loginData -ContentType "application/json" -SkipCertificateCheck
        
        Write-Host "✓ Login successful!" -ForegroundColor Green
        Write-Host "Token received: $($response.token.Substring(0, 50))..." -ForegroundColor Cyan
        Write-Host "User ID: $($response.user.userId)" -ForegroundColor Cyan
        Write-Host "Role: $($response.user.role)" -ForegroundColor Cyan
        Write-Host "Email: $($response.user.email)" -ForegroundColor Cyan
        
        return $response.token
    }
    catch {
        Write-Host "✗ Login failed!" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "Status Code: $statusCode" -ForegroundColor Red
        }
        return $null
    }
}

# Wait for API to start
Write-Host "Waiting for API to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Test admin login
$adminToken = Test-Login -Username $adminCredentials.username -Password $adminCredentials.password -UserType "Admin"

# Test reviewer login
$reviewerToken = Test-Login -Username $reviewerCredentials.username -Password $reviewerCredentials.password -UserType "Reviewer"

# Test with wrong password
Write-Host "`nTesting with wrong password..." -ForegroundColor Yellow
try {
    $wrongData = @{
        username = "admin"
        password = "WrongPassword"
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri "$apiUrl/api/auth/login" -Method POST -Body $wrongData -ContentType "application/json" -SkipCertificateCheck
    Write-Host "✗ Wrong password test failed - login should have been rejected!" -ForegroundColor Red
}
catch {
    Write-Host "✓ Wrong password correctly rejected!" -ForegroundColor Green
}

Write-Host "`n==========================================" -ForegroundColor Green
Write-Host "Login testing completed!" -ForegroundColor Green

if ($adminToken -and $reviewerToken) {
    Write-Host "✓ All tests passed! Password verification is working correctly." -ForegroundColor Green
} else {
    Write-Host "✗ Some tests failed. Please check the API logs." -ForegroundColor Red
}
}
