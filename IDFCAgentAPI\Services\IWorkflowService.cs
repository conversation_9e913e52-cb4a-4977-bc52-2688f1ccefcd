using IDFCAgentAPI.Models;

namespace IDFCAgentAPI.Services
{
    public interface IWorkflowService
    {
        Task<bool> CanTransitionToStatusAsync(int agentId, AgentStatus fromStatus, AgentStatus toStatus);
        Task<WorkflowValidationResult> ValidateStatusTransitionAsync(int agentId, AgentStatus newStatus);
        Task<bool> ProcessStatusTransitionAsync(int agentId, AgentStatus newStatus, string changedBy, string? comments = null);
        Task<bool> AutoProgressWorkflowAsync(int agentId);
        Task<WorkflowStepResult> GetNextWorkflowStepAsync(int agentId);
        Task<IEnumerable<AgentStatus>> GetValidNextStatusesAsync(int agentId);
        Task<WorkflowSummary> GetWorkflowSummaryAsync(int agentId);
        Task<bool> IsWorkflowCompleteAsync(int agentId);
        Task<bool> RequiresManualReviewAsync(int agentId);
        Task<TimeSpan> GetEstimatedCompletionTimeAsync(int agentId);
    }

    public class WorkflowValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> RequiredActions { get; set; } = new();
        public List<string> MissingDocuments { get; set; } = new();
        public bool RequiresManualReview { get; set; }
    }

    public class WorkflowStepResult
    {
        public string StepName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public AgentStatus CurrentStatus { get; set; }
        public AgentStatus? NextStatus { get; set; }
        public bool CanAutoProgress { get; set; }
        public List<string> RequiredActions { get; set; } = new();
        public TimeSpan EstimatedDuration { get; set; }
    }

    public class WorkflowSummary
    {
        public int AgentId { get; set; }
        public AgentStatus CurrentStatus { get; set; }
        public string CurrentStepName { get; set; } = string.Empty;
        public int CompletedSteps { get; set; }
        public int TotalSteps { get; set; }
        public double ProgressPercentage { get; set; }
        public List<WorkflowStep> Steps { get; set; } = new();
        public TimeSpan TotalTimeSpent { get; set; }
        public TimeSpan EstimatedRemainingTime { get; set; }
        public bool IsBlocked { get; set; }
        public string? BlockedReason { get; set; }
    }

    public class WorkflowStep
    {
        public string Name { get; set; } = string.Empty;
        public AgentStatus Status { get; set; }
        public bool IsCompleted { get; set; }
        public bool IsCurrent { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string? CompletedBy { get; set; }
        public List<string> Requirements { get; set; } = new();
        public bool RequiresManualReview { get; set; }
    }
}
