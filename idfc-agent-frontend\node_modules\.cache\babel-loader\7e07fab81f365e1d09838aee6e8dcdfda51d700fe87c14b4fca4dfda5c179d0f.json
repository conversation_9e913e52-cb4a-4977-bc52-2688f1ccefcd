{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"18\",\n  x2: \"18\",\n  y1: \"20\",\n  y2: \"4\",\n  key: \"cun8e5\"\n}], [\"polygon\", {\n  points: \"14,20 4,12 14,4\",\n  key: \"ypakod\"\n}]];\nconst StepBack = createLucideIcon(\"step-back\", __iconNode);\nexport { __iconNode, StepBack as default };", "map": {"version": 3, "names": ["__iconNode", "x1", "x2", "y1", "y2", "key", "points", "StepBack", "createLucideIcon"], "sources": ["D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\node_modules\\lucide-react\\src\\icons\\step-back.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '18', x2: '18', y1: '20', y2: '4', key: 'cun8e5' }],\n  ['polygon', { points: '14,20 4,12 14,4', key: 'ypakod' }],\n];\n\n/**\n * @component @name StepBack\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTgiIHgyPSIxOCIgeTE9IjIwIiB5Mj0iNCIgLz4KICA8cG9seWdvbiBwb2ludHM9IjE0LDIwIDQsMTIgMTQsNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/step-back\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst StepBack = createLucideIcon('step-back', __iconNode);\n\nexport default StepBack;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,SAAW;EAAEC,MAAA,EAAQ,iBAAmB;EAAAD,GAAA,EAAK;AAAU,GAC1D;AAaM,MAAAE,QAAA,GAAWC,gBAAiB,cAAaR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}