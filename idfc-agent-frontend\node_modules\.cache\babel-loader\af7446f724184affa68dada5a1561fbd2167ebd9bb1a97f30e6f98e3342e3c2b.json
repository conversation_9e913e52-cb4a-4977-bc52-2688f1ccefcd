{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\IDFCAgentOnboardingAndManagementSolution\\\\idfc-agent-frontend\\\\src\\\\pages\\\\AgentRegistration.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { agentAPI } from '../services/api';\nimport { toast } from 'react-toastify';\nimport { User, Mail, Phone, MapPin, Calendar, CreditCard, Upload, FileText, Building, DollarSign } from 'lucide-react';\n\n// Validation schema\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst schema = yup.object({\n  // Personal Information\n  firstName: yup.string().required('First name is required').min(2, 'Minimum 2 characters'),\n  lastName: yup.string().required('Last name is required').min(2, 'Minimum 2 characters'),\n  email: yup.string().email('Invalid email format').required('Email is required'),\n  phoneNumber: yup.string().matches(/^[0-9]{10}$/, 'Phone number must be 10 digits').required('Phone number is required'),\n  aadharNumber: yup.string().matches(/^[0-9]{12}$/, 'Aadhar number must be 12 digits').required('Aadhar number is required'),\n  panNumber: yup.string().matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Invalid PAN format').required('PAN number is required'),\n  dateOfBirth: yup.date().max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Must be at least 18 years old').required('Date of birth is required'),\n  // Address Information\n  address: yup.string().required('Address is required').min(10, 'Address too short'),\n  city: yup.string().required('City is required'),\n  state: yup.string().required('State is required'),\n  pinCode: yup.string().matches(/^[0-9]{6}$/, 'PIN code must be 6 digits').required('PIN code is required')\n});\nconst AgentRegistration = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [currentStep, setCurrentStep] = useState(1);\n  const [documents, setDocuments] = useState({\n    aadharCard: null,\n    panCard: null,\n    photo: null,\n    bankPassbook: null,\n    businessProof: null\n  });\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    },\n    trigger,\n    getValues\n  } = useForm({\n    resolver: yupResolver(schema),\n    mode: 'onChange'\n  });\n  const steps = [{\n    id: 1,\n    name: 'Personal Information',\n    icon: User\n  }, {\n    id: 2,\n    name: 'Address Details',\n    icon: MapPin\n  }, {\n    id: 3,\n    name: 'Document Upload',\n    icon: Upload\n  }, {\n    id: 4,\n    name: 'Review & Submit',\n    icon: FileText\n  }];\n  const handleNext = async () => {\n    let fieldsToValidate = [];\n    switch (currentStep) {\n      case 1:\n        fieldsToValidate = ['firstName', 'lastName', 'email', 'phoneNumber', 'dateOfBirth'];\n        break;\n      case 2:\n        fieldsToValidate = ['aadharNumber', 'panNumber', 'address', 'city', 'state', 'pinCode'];\n        break;\n      case 3:\n        // Validate documents\n        const requiredDocs = ['aadharCard', 'panCard', 'photo'];\n        const missingDocs = requiredDocs.filter(doc => !documents[doc]);\n        if (missingDocs.length > 0) {\n          toast.error(`Please upload: ${missingDocs.join(', ')}`);\n          return;\n        }\n        break;\n    }\n    if (fieldsToValidate.length > 0) {\n      const isValid = await trigger(fieldsToValidate);\n      if (!isValid) return;\n    }\n    setCurrentStep(prev => Math.min(prev + 1, 4));\n  };\n  const handlePrevious = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n  const handleFileUpload = (documentType, file) => {\n    if (!file) return;\n\n    // Validate file type\n    const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];\n    if (!allowedTypes.includes(file.type)) {\n      toast.error('Only JPEG, PNG, and PDF files are allowed');\n      return;\n    }\n\n    // Validate file size (5MB max)\n    if (file.size > 5 * 1024 * 1024) {\n      toast.error('File size must be less than 5MB');\n      return;\n    }\n    setDocuments(prev => ({\n      ...prev,\n      [documentType]: file\n    }));\n    toast.success(`${documentType} uploaded successfully`);\n  };\n  const onSubmit = async data => {\n    setIsSubmitting(true);\n    try {\n      // Format the data according to the API requirements\n      const formattedData = {\n        ...data,\n        dateOfBirth: new Date(data.dateOfBirth).toISOString()\n      };\n      console.log('Submitting registration data:', formattedData);\n      console.log('API endpoint:', 'https://localhost:7093/api/agents/register');\n      const response = await agentAPI.register(formattedData);\n      console.log('Registration response:', response);\n      if (response.success) {\n        toast.success('Agent registration submitted successfully!');\n        // Navigate based on whether this is public registration or admin registration\n        if (location.pathname === '/register') {\n          // Public registration - redirect to login\n          navigate('/login', {\n            state: {\n              message: 'Registration successful! Please login with your credentials.'\n            }\n          });\n        } else {\n          // Admin registration - stay in admin area\n          navigate('/agents');\n        }\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Registration error:', error);\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Registration failed. Please try again.';\n      toast.error(message);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900\",\n        children: \"Agent Registration\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-600\",\n        children: \"Complete your agent registration to become an IDFC FASTag agent.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        \"aria-label\": \"Progress\",\n        children: /*#__PURE__*/_jsxDEV(\"ol\", {\n          className: \"flex items-center\",\n          children: steps.map((step, stepIdx) => {\n            const Icon = step.icon;\n            const isCompleted = currentStep > step.id;\n            const isCurrent = currentStep === step.id;\n            return /*#__PURE__*/_jsxDEV(\"li\", {\n              className: `${stepIdx !== steps.length - 1 ? 'flex-1' : ''}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `flex items-center ${stepIdx !== steps.length - 1 ? 'w-full' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `\n                        flex items-center justify-center w-10 h-10 rounded-full border-2\n                        ${isCompleted ? 'bg-blue-600 border-blue-600 text-white' : isCurrent ? 'border-blue-600 text-blue-600' : 'border-gray-300 text-gray-500'}\n                      `,\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `ml-3 text-sm font-medium ${isCurrent ? 'text-blue-600' : isCompleted ? 'text-gray-900' : 'text-gray-500'}`,\n                    children: step.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this), stepIdx !== steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex-1 ml-4 h-0.5 ${isCompleted ? 'bg-blue-600' : 'bg-gray-300'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this)\n            }, step.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit(onSubmit),\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: [currentStep === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), \"Personal Information\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"First Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('firstName'),\n              type: \"text\",\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              placeholder: \"Enter your first name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this), errors.firstName && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.firstName.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Last Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('lastName'),\n              type: \"text\",\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              placeholder: \"Enter your last name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this), errors.lastName && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.lastName.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Email Address *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('email'),\n                type: \"email\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"Enter your email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Mail, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), errors.email && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.email.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Phone Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('phoneNumber'),\n                type: \"tel\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"10-digit mobile number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Phone, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), errors.phoneNumber && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.phoneNumber.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Date of Birth *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('dateOfBirth'),\n                type: \"date\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Calendar, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this), errors.dateOfBirth && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.dateOfBirth.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this), currentStep === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(MapPin, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), \"Address & Identity Details\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Aadhar Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('aadharNumber'),\n                type: \"text\",\n                maxLength: \"12\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"12-digit Aadhar number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(CreditCard, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this), errors.aadharNumber && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.aadharNumber.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"PAN Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('panNumber'),\n                type: \"text\",\n                maxLength: \"10\",\n                className: \"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                placeholder: \"**********\",\n                style: {\n                  textTransform: 'uppercase'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(CreditCard, {\n                className: \"absolute left-3 top-2.5 h-4 w-4 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), errors.panNumber && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.panNumber.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sm:col-span-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Address *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              ...register('address'),\n              rows: 3,\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              placeholder: \"Enter your complete address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this), errors.address && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.address.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"City *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('city'),\n              type: \"text\",\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              placeholder: \"Enter your city\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this), errors.city && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.city.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"State *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              ...register('state'),\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select State\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Andhra Pradesh\",\n                children: \"Andhra Pradesh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Arunachal Pradesh\",\n                children: \"Arunachal Pradesh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Assam\",\n                children: \"Assam\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Bihar\",\n                children: \"Bihar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Chhattisgarh\",\n                children: \"Chhattisgarh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Goa\",\n                children: \"Goa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Gujarat\",\n                children: \"Gujarat\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Haryana\",\n                children: \"Haryana\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Himachal Pradesh\",\n                children: \"Himachal Pradesh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Jharkhand\",\n                children: \"Jharkhand\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Karnataka\",\n                children: \"Karnataka\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Kerala\",\n                children: \"Kerala\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Madhya Pradesh\",\n                children: \"Madhya Pradesh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Maharashtra\",\n                children: \"Maharashtra\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Manipur\",\n                children: \"Manipur\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Meghalaya\",\n                children: \"Meghalaya\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Mizoram\",\n                children: \"Mizoram\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Nagaland\",\n                children: \"Nagaland\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Odisha\",\n                children: \"Odisha\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Punjab\",\n                children: \"Punjab\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Rajasthan\",\n                children: \"Rajasthan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Sikkim\",\n                children: \"Sikkim\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Tamil Nadu\",\n                children: \"Tamil Nadu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Telangana\",\n                children: \"Telangana\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Tripura\",\n                children: \"Tripura\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Uttar Pradesh\",\n                children: \"Uttar Pradesh\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Uttarakhand\",\n                children: \"Uttarakhand\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"West Bengal\",\n                children: \"West Bengal\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"Delhi\",\n                children: \"Delhi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 17\n            }, this), errors.state && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.state.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"PIN Code *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('pinCode'),\n              type: \"text\",\n              maxLength: \"6\",\n              className: \"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n              placeholder: \"6-digit PIN code\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 17\n            }, this), errors.pinCode && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.pinCode.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 11\n      }, this), currentStep === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(Upload, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this), \"Document Upload\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Please upload clear, readable copies of the following documents. Accepted formats: JPEG, PNG, PDF (Max 5MB each)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n          children: [{\n            key: 'aadharCard',\n            label: 'Aadhar Card',\n            required: true\n          }, {\n            key: 'panCard',\n            label: 'PAN Card',\n            required: true\n          }, {\n            key: 'photo',\n            label: 'Passport Size Photo',\n            required: true\n          }, {\n            key: 'bankPassbook',\n            label: 'Bank Passbook/Statement',\n            required: false\n          }, {\n            key: 'businessProof',\n            label: 'Business Proof (if applicable)',\n            required: false\n          }].map(doc => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(Upload, {\n                className: \"mx-auto h-12 w-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"cursor-pointer\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mt-2 block text-sm font-medium text-gray-900\",\n                    children: [doc.label, \" \", doc.required && '*']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    className: \"hidden\",\n                    accept: \".jpg,.jpeg,.png,.pdf\",\n                    onChange: e => handleFileUpload(doc.key, e.target.files[0])\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"mt-2 block text-xs text-gray-500\",\n                    children: \"Click to upload or drag and drop\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 21\n              }, this), documents[doc.key] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 text-sm text-green-600 flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(FileText, {\n                  className: \"w-4 h-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 25\n                }, this), documents[doc.key].name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 19\n            }, this)\n          }, doc.key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 11\n      }, this), currentStep === 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(FileText, {\n              className: \"w-5 h-5 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this), \"Review & Submit\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Please review all the information before submitting your application.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-50 rounded-lg p-6 space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Personal Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: [getValues('firstName'), \" \", getValues('lastName')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: getValues('email')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Phone:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: getValues('phoneNumber')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Date of Birth:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: getValues('dateOfBirth')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Identity & Address\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Aadhar:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: getValues('aadharNumber')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"PAN:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: getValues('panNumber')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-500\",\n                  children: \"Address:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-gray-900\",\n                  children: [getValues('address'), \", \", getValues('city'), \", \", getValues('state'), \" - \", getValues('pinCode')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900 mb-2\",\n              children: \"Uploaded Documents\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-2 text-sm\",\n              children: Object.entries(documents).map(([key, file]) => file && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center text-green-600\",\n                children: [/*#__PURE__*/_jsxDEV(FileText, {\n                  className: \"w-4 h-4 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 25\n                }, this), key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(FileText, {\n                className: \"h-5 w-5 text-blue-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-medium text-blue-800\",\n                children: \"Important Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 text-sm text-blue-700\",\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-disc list-inside space-y-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Your application will be reviewed within 3-5 business days\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"You will receive email notifications about status updates\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Ensure all uploaded documents are clear and readable\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 606,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: \"Any false information may lead to application rejection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between pt-6 border-t border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handlePrevious,\n          disabled: currentStep === 1,\n          className: \"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this), currentStep < 4 ? /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: handleNext,\n          className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          disabled: isSubmitting,\n          className: \"inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n          children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 19\n            }, this), \"Submitting...\"]\n          }, void 0, true) : 'Submit Application'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentRegistration, \"tPXQc+9Fe3n9ivanwpzZLb8Eq9M=\", false, function () {\n  return [useNavigate, useLocation, useForm];\n});\n_c = AgentRegistration;\nexport default AgentRegistration;\nvar _c;\n$RefreshReg$(_c, \"AgentRegistration\");", "map": {"version": 3, "names": ["React", "useState", "useForm", "yupResolver", "yup", "useNavigate", "useLocation", "agentAPI", "toast", "User", "Mail", "Phone", "MapPin", "Calendar", "CreditCard", "Upload", "FileText", "Building", "DollarSign", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "schema", "object", "firstName", "string", "required", "min", "lastName", "email", "phoneNumber", "matches", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "panNumber", "dateOfBirth", "date", "max", "Date", "now", "address", "city", "state", "pinCode", "AgentRegistration", "_s", "navigate", "location", "isSubmitting", "setIsSubmitting", "currentStep", "setCurrentStep", "documents", "setDocuments", "aadharCard", "panCard", "photo", "bankPassbook", "businessProof", "register", "handleSubmit", "formState", "errors", "trigger", "getV<PERSON>ues", "resolver", "mode", "steps", "id", "name", "icon", "handleNext", "fieldsToValidate", "requiredDocs", "missingDocs", "filter", "doc", "length", "error", "join", "<PERSON><PERSON><PERSON><PERSON>", "prev", "Math", "handlePrevious", "handleFileUpload", "documentType", "file", "allowedTypes", "includes", "type", "size", "success", "onSubmit", "data", "formattedData", "toISOString", "console", "log", "response", "pathname", "message", "_error$response", "_error$response$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "step", "stepIdx", "Icon", "isCompleted", "isCurrent", "placeholder", "max<PERSON><PERSON><PERSON>", "style", "textTransform", "rows", "value", "key", "label", "accept", "onChange", "e", "target", "files", "Object", "entries", "replace", "str", "toUpperCase", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/IDFCAgentOnboardingAndManagementSolution/idfc-agent-frontend/src/pages/AgentRegistration.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { yupResolver } from '@hookform/resolvers/yup';\nimport * as yup from 'yup';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { agentAPI } from '../services/api';\nimport { toast } from 'react-toastify';\nimport {\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  Calendar,\n  CreditCard,\n  Upload,\n  FileText,\n  Building,\n  DollarSign\n} from 'lucide-react';\n\n// Validation schema\nconst schema = yup.object({\n  // Personal Information\n  firstName: yup.string().required('First name is required').min(2, 'Minimum 2 characters'),\n  lastName: yup.string().required('Last name is required').min(2, 'Minimum 2 characters'),\n  email: yup.string().email('Invalid email format').required('Email is required'),\n  phoneNumber: yup.string()\n    .matches(/^[0-9]{10}$/, 'Phone number must be 10 digits')\n    .required('Phone number is required'),\n  aadharNumber: yup.string()\n    .matches(/^[0-9]{12}$/, 'Aadhar number must be 12 digits')\n    .required('Aadhar number is required'),\n  panNumber: yup.string()\n    .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Invalid PAN format')\n    .required('PAN number is required'),\n  dateOfBirth: yup.date()\n    .max(new Date(Date.now() - 18 * 365 * 24 * 60 * 60 * 1000), 'Must be at least 18 years old')\n    .required('Date of birth is required'),\n\n  // Address Information\n  address: yup.string().required('Address is required').min(10, 'Address too short'),\n  city: yup.string().required('City is required'),\n  state: yup.string().required('State is required'),\n  pinCode: yup.string()\n    .matches(/^[0-9]{6}$/, 'PIN code must be 6 digits')\n    .required('PIN code is required'),\n});\n\nconst AgentRegistration = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [currentStep, setCurrentStep] = useState(1);\n  const [documents, setDocuments] = useState({\n    aadharCard: null,\n    panCard: null,\n    photo: null,\n    bankPassbook: null,\n    businessProof: null\n  });\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    trigger,\n    getValues\n  } = useForm({\n    resolver: yupResolver(schema),\n    mode: 'onChange'\n  });\n\n  const steps = [\n    { id: 1, name: 'Personal Information', icon: User },\n    { id: 2, name: 'Address Details', icon: MapPin },\n    { id: 3, name: 'Document Upload', icon: Upload },\n    { id: 4, name: 'Review & Submit', icon: FileText }\n  ];\n\n  const handleNext = async () => {\n    let fieldsToValidate = [];\n\n    switch (currentStep) {\n      case 1:\n        fieldsToValidate = ['firstName', 'lastName', 'email', 'phoneNumber', 'dateOfBirth'];\n        break;\n      case 2:\n        fieldsToValidate = ['aadharNumber', 'panNumber', 'address', 'city', 'state', 'pinCode'];\n        break;\n      case 3:\n        // Validate documents\n        const requiredDocs = ['aadharCard', 'panCard', 'photo'];\n        const missingDocs = requiredDocs.filter(doc => !documents[doc]);\n        if (missingDocs.length > 0) {\n          toast.error(`Please upload: ${missingDocs.join(', ')}`);\n          return;\n        }\n        break;\n    }\n\n    if (fieldsToValidate.length > 0) {\n      const isValid = await trigger(fieldsToValidate);\n      if (!isValid) return;\n    }\n\n    setCurrentStep(prev => Math.min(prev + 1, 4));\n  };\n\n  const handlePrevious = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n\n  const handleFileUpload = (documentType, file) => {\n    if (!file) return;\n\n    // Validate file type\n    const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];\n    if (!allowedTypes.includes(file.type)) {\n      toast.error('Only JPEG, PNG, and PDF files are allowed');\n      return;\n    }\n\n    // Validate file size (5MB max)\n    if (file.size > 5 * 1024 * 1024) {\n      toast.error('File size must be less than 5MB');\n      return;\n    }\n\n    setDocuments(prev => ({\n      ...prev,\n      [documentType]: file\n    }));\n\n    toast.success(`${documentType} uploaded successfully`);\n  };\n\n  const onSubmit = async (data) => {\n    setIsSubmitting(true);\n\n    try {\n      // Format the data according to the API requirements\n      const formattedData = {\n        ...data,\n        dateOfBirth: new Date(data.dateOfBirth).toISOString(),\n      };\n\n      console.log('Submitting registration data:', formattedData);\n      console.log('API endpoint:', 'https://localhost:7093/api/agents/register');\n\n      const response = await agentAPI.register(formattedData);\n      console.log('Registration response:', response);\n\n      if (response.success) {\n        toast.success('Agent registration submitted successfully!');\n        // Navigate based on whether this is public registration or admin registration\n        if (location.pathname === '/register') {\n          // Public registration - redirect to login\n          navigate('/login', {\n            state: { message: 'Registration successful! Please login with your credentials.' }\n          });\n        } else {\n          // Admin registration - stay in admin area\n          navigate('/agents');\n        }\n      }\n    } catch (error) {\n      console.error('Registration error:', error);\n      const message = error.response?.data?.message || 'Registration failed. Please try again.';\n      toast.error(message);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">Agent Registration</h1>\n        <p className=\"mt-1 text-sm text-gray-600\">\n          Complete your agent registration to become an IDFC FASTag agent.\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <nav aria-label=\"Progress\">\n          <ol className=\"flex items-center\">\n            {steps.map((step, stepIdx) => {\n              const Icon = step.icon;\n              const isCompleted = currentStep > step.id;\n              const isCurrent = currentStep === step.id;\n\n              return (\n                <li key={step.name} className={`${stepIdx !== steps.length - 1 ? 'flex-1' : ''}`}>\n                  <div className={`flex items-center ${stepIdx !== steps.length - 1 ? 'w-full' : ''}`}>\n                    <div className=\"flex items-center\">\n                      <div className={`\n                        flex items-center justify-center w-10 h-10 rounded-full border-2\n                        ${isCompleted\n                          ? 'bg-blue-600 border-blue-600 text-white'\n                          : isCurrent\n                            ? 'border-blue-600 text-blue-600'\n                            : 'border-gray-300 text-gray-500'\n                        }\n                      `}>\n                        <Icon className=\"w-5 h-5\" />\n                      </div>\n                      <span className={`ml-3 text-sm font-medium ${\n                        isCurrent ? 'text-blue-600' : isCompleted ? 'text-gray-900' : 'text-gray-500'\n                      }`}>\n                        {step.name}\n                      </span>\n                    </div>\n                    {stepIdx !== steps.length - 1 && (\n                      <div className={`flex-1 ml-4 h-0.5 ${\n                        isCompleted ? 'bg-blue-600' : 'bg-gray-300'\n                      }`} />\n                    )}\n                  </div>\n                </li>\n              );\n            })}\n          </ol>\n        </nav>\n      </div>\n\n      {/* Form Content */}\n      <form onSubmit={handleSubmit(onSubmit)} className=\"bg-white shadow rounded-lg p-6\">\n        {/* Step 1: Personal Information */}\n        {currentStep === 1 && (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                <User className=\"w-5 h-5 mr-2\" />\n                Personal Information\n              </h3>\n            </div>\n\n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  First Name *\n                </label>\n                <input\n                  {...register('firstName')}\n                  type=\"text\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Enter your first name\"\n                />\n                {errors.firstName && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.firstName.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Last Name *\n                </label>\n                <input\n                  {...register('lastName')}\n                  type=\"text\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Enter your last name\"\n                />\n                {errors.lastName && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.lastName.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Email Address *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('email')}\n                    type=\"email\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    placeholder=\"Enter your email\"\n                  />\n                  <Mail className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.email && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Phone Number *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('phoneNumber')}\n                    type=\"tel\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    placeholder=\"10-digit mobile number\"\n                  />\n                  <Phone className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.phoneNumber && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.phoneNumber.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Date of Birth *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('dateOfBirth')}\n                    type=\"date\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  />\n                  <Calendar className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.dateOfBirth && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.dateOfBirth.message}</p>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Step 2: Address & Identity Details */}\n        {currentStep === 2 && (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                <MapPin className=\"w-5 h-5 mr-2\" />\n                Address & Identity Details\n              </h3>\n            </div>\n\n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Aadhar Number *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('aadharNumber')}\n                    type=\"text\"\n                    maxLength=\"12\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    placeholder=\"12-digit Aadhar number\"\n                  />\n                  <CreditCard className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.aadharNumber && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.aadharNumber.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  PAN Number *\n                </label>\n                <div className=\"mt-1 relative\">\n                  <input\n                    {...register('panNumber')}\n                    type=\"text\"\n                    maxLength=\"10\"\n                    className=\"block w-full pl-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    placeholder=\"**********\"\n                    style={{ textTransform: 'uppercase' }}\n                  />\n                  <CreditCard className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n                </div>\n                {errors.panNumber && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.panNumber.message}</p>\n                )}\n              </div>\n\n              <div className=\"sm:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Address *\n                </label>\n                <textarea\n                  {...register('address')}\n                  rows={3}\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Enter your complete address\"\n                />\n                {errors.address && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.address.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  City *\n                </label>\n                <input\n                  {...register('city')}\n                  type=\"text\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"Enter your city\"\n                />\n                {errors.city && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.city.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  State *\n                </label>\n                <select\n                  {...register('state')}\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                >\n                  <option value=\"\">Select State</option>\n                  <option value=\"Andhra Pradesh\">Andhra Pradesh</option>\n                  <option value=\"Arunachal Pradesh\">Arunachal Pradesh</option>\n                  <option value=\"Assam\">Assam</option>\n                  <option value=\"Bihar\">Bihar</option>\n                  <option value=\"Chhattisgarh\">Chhattisgarh</option>\n                  <option value=\"Goa\">Goa</option>\n                  <option value=\"Gujarat\">Gujarat</option>\n                  <option value=\"Haryana\">Haryana</option>\n                  <option value=\"Himachal Pradesh\">Himachal Pradesh</option>\n                  <option value=\"Jharkhand\">Jharkhand</option>\n                  <option value=\"Karnataka\">Karnataka</option>\n                  <option value=\"Kerala\">Kerala</option>\n                  <option value=\"Madhya Pradesh\">Madhya Pradesh</option>\n                  <option value=\"Maharashtra\">Maharashtra</option>\n                  <option value=\"Manipur\">Manipur</option>\n                  <option value=\"Meghalaya\">Meghalaya</option>\n                  <option value=\"Mizoram\">Mizoram</option>\n                  <option value=\"Nagaland\">Nagaland</option>\n                  <option value=\"Odisha\">Odisha</option>\n                  <option value=\"Punjab\">Punjab</option>\n                  <option value=\"Rajasthan\">Rajasthan</option>\n                  <option value=\"Sikkim\">Sikkim</option>\n                  <option value=\"Tamil Nadu\">Tamil Nadu</option>\n                  <option value=\"Telangana\">Telangana</option>\n                  <option value=\"Tripura\">Tripura</option>\n                  <option value=\"Uttar Pradesh\">Uttar Pradesh</option>\n                  <option value=\"Uttarakhand\">Uttarakhand</option>\n                  <option value=\"West Bengal\">West Bengal</option>\n                  <option value=\"Delhi\">Delhi</option>\n                </select>\n                {errors.state && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.state.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  PIN Code *\n                </label>\n                <input\n                  {...register('pinCode')}\n                  type=\"text\"\n                  maxLength=\"6\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  placeholder=\"6-digit PIN code\"\n                />\n                {errors.pinCode && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.pinCode.message}</p>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Step 3: Document Upload */}\n        {currentStep === 3 && (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                <Upload className=\"w-5 h-5 mr-2\" />\n                Document Upload\n              </h3>\n              <p className=\"text-sm text-gray-600\">\n                Please upload clear, readable copies of the following documents. Accepted formats: JPEG, PNG, PDF (Max 5MB each)\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n              {[\n                { key: 'aadharCard', label: 'Aadhar Card', required: true },\n                { key: 'panCard', label: 'PAN Card', required: true },\n                { key: 'photo', label: 'Passport Size Photo', required: true },\n                { key: 'bankPassbook', label: 'Bank Passbook/Statement', required: false },\n                { key: 'businessProof', label: 'Business Proof (if applicable)', required: false }\n              ].map((doc) => (\n                <div key={doc.key} className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 transition-colors\">\n                  <div className=\"text-center\">\n                    <Upload className=\"mx-auto h-12 w-12 text-gray-400\" />\n                    <div className=\"mt-4\">\n                      <label className=\"cursor-pointer\">\n                        <span className=\"mt-2 block text-sm font-medium text-gray-900\">\n                          {doc.label} {doc.required && '*'}\n                        </span>\n                        <input\n                          type=\"file\"\n                          className=\"hidden\"\n                          accept=\".jpg,.jpeg,.png,.pdf\"\n                          onChange={(e) => handleFileUpload(doc.key, e.target.files[0])}\n                        />\n                        <span className=\"mt-2 block text-xs text-gray-500\">\n                          Click to upload or drag and drop\n                        </span>\n                      </label>\n                    </div>\n                    {documents[doc.key] && (\n                      <div className=\"mt-2 text-sm text-green-600 flex items-center justify-center\">\n                        <FileText className=\"w-4 h-4 mr-1\" />\n                        {documents[doc.key].name}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Step 4: Review & Submit */}\n        {currentStep === 4 && (\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                <FileText className=\"w-5 h-5 mr-2\" />\n                Review & Submit\n              </h3>\n              <p className=\"text-sm text-gray-600\">\n                Please review all the information before submitting your application.\n              </p>\n            </div>\n\n            <div className=\"bg-gray-50 rounded-lg p-6 space-y-4\">\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">Personal Information</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-gray-500\">Name:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('firstName')} {getValues('lastName')}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-500\">Email:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('email')}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-500\">Phone:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('phoneNumber')}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-500\">Date of Birth:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('dateOfBirth')}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">Identity & Address</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-gray-500\">Aadhar:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('aadharNumber')}</span>\n                  </div>\n                  <div>\n                    <span className=\"text-gray-500\">PAN:</span>\n                    <span className=\"ml-2 text-gray-900\">{getValues('panNumber')}</span>\n                  </div>\n                  <div className=\"col-span-2\">\n                    <span className=\"text-gray-500\">Address:</span>\n                    <span className=\"ml-2 text-gray-900\">\n                      {getValues('address')}, {getValues('city')}, {getValues('state')} - {getValues('pinCode')}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-2\">Uploaded Documents</h4>\n                <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                  {Object.entries(documents).map(([key, file]) => (\n                    file && (\n                      <div key={key} className=\"flex items-center text-green-600\">\n                        <FileText className=\"w-4 h-4 mr-1\" />\n                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}\n                      </div>\n                    )\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0\">\n                  <FileText className=\"h-5 w-5 text-blue-400\" />\n                </div>\n                <div className=\"ml-3\">\n                  <h3 className=\"text-sm font-medium text-blue-800\">\n                    Important Information\n                  </h3>\n                  <div className=\"mt-2 text-sm text-blue-700\">\n                    <ul className=\"list-disc list-inside space-y-1\">\n                      <li>Your application will be reviewed within 3-5 business days</li>\n                      <li>You will receive email notifications about status updates</li>\n                      <li>Ensure all uploaded documents are clear and readable</li>\n                      <li>Any false information may lead to application rejection</li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Navigation Buttons */}\n        <div className=\"flex justify-between pt-6 border-t border-gray-200\">\n          <button\n            type=\"button\"\n            onClick={handlePrevious}\n            disabled={currentStep === 1}\n            className=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            Previous\n          </button>\n\n          {currentStep < 4 ? (\n            <button\n              type=\"button\"\n              onClick={handleNext}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Next\n            </button>\n          ) : (\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Submitting...\n                </>\n              ) : (\n                'Submit Application'\n              )}\n            </button>\n          )}\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default AgentRegistration;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,WAAW,QAAQ,yBAAyB;AACrD,OAAO,KAAKC,GAAG,MAAM,KAAK;AAC1B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,UAAU,QACL,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,MAAM,GAAGnB,GAAG,CAACoB,MAAM,CAAC;EACxB;EACAC,SAAS,EAAErB,GAAG,CAACsB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,wBAAwB,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC;EACzFC,QAAQ,EAAEzB,GAAG,CAACsB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,uBAAuB,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE,sBAAsB,CAAC;EACvFE,KAAK,EAAE1B,GAAG,CAACsB,MAAM,CAAC,CAAC,CAACI,KAAK,CAAC,sBAAsB,CAAC,CAACH,QAAQ,CAAC,mBAAmB,CAAC;EAC/EI,WAAW,EAAE3B,GAAG,CAACsB,MAAM,CAAC,CAAC,CACtBM,OAAO,CAAC,aAAa,EAAE,gCAAgC,CAAC,CACxDL,QAAQ,CAAC,0BAA0B,CAAC;EACvCM,YAAY,EAAE7B,GAAG,CAACsB,MAAM,CAAC,CAAC,CACvBM,OAAO,CAAC,aAAa,EAAE,iCAAiC,CAAC,CACzDL,QAAQ,CAAC,2BAA2B,CAAC;EACxCO,SAAS,EAAE9B,GAAG,CAACsB,MAAM,CAAC,CAAC,CACpBM,OAAO,CAAC,4BAA4B,EAAE,oBAAoB,CAAC,CAC3DL,QAAQ,CAAC,wBAAwB,CAAC;EACrCQ,WAAW,EAAE/B,GAAG,CAACgC,IAAI,CAAC,CAAC,CACpBC,GAAG,CAAC,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,+BAA+B,CAAC,CAC3FZ,QAAQ,CAAC,2BAA2B,CAAC;EAExC;EACAa,OAAO,EAAEpC,GAAG,CAACsB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,qBAAqB,CAAC,CAACC,GAAG,CAAC,EAAE,EAAE,mBAAmB,CAAC;EAClFa,IAAI,EAAErC,GAAG,CAACsB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,kBAAkB,CAAC;EAC/Ce,KAAK,EAAEtC,GAAG,CAACsB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,mBAAmB,CAAC;EACjDgB,OAAO,EAAEvC,GAAG,CAACsB,MAAM,CAAC,CAAC,CAClBM,OAAO,CAAC,YAAY,EAAE,2BAA2B,CAAC,CAClDL,QAAQ,CAAC,sBAAsB;AACpC,CAAC,CAAC;AAEF,MAAMiB,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM0C,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC;IACzCqD,UAAU,EAAE,IAAI;IAChBC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,IAAI;IACXC,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE;EACjB,CAAC,CAAC;EAEF,MAAM;IACJC,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO,CAAC;IACrBC,OAAO;IACPC;EACF,CAAC,GAAG9D,OAAO,CAAC;IACV+D,QAAQ,EAAE9D,WAAW,CAACoB,MAAM,CAAC;IAC7B2C,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAMC,KAAK,GAAG,CACZ;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,sBAAsB;IAAEC,IAAI,EAAE7D;EAAK,CAAC,EACnD;IAAE2D,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE1D;EAAO,CAAC,EAChD;IAAEwD,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAEvD;EAAO,CAAC,EAChD;IAAEqD,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAEtD;EAAS,CAAC,CACnD;EAED,MAAMuD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAIC,gBAAgB,GAAG,EAAE;IAEzB,QAAQtB,WAAW;MACjB,KAAK,CAAC;QACJsB,gBAAgB,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,aAAa,CAAC;QACnF;MACF,KAAK,CAAC;QACJA,gBAAgB,GAAG,CAAC,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;QACvF;MACF,KAAK,CAAC;QACJ;QACA,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,CAAC;QACvD,MAAMC,WAAW,GAAGD,YAAY,CAACE,MAAM,CAACC,GAAG,IAAI,CAACxB,SAAS,CAACwB,GAAG,CAAC,CAAC;QAC/D,IAAIF,WAAW,CAACG,MAAM,GAAG,CAAC,EAAE;UAC1BrE,KAAK,CAACsE,KAAK,CAAC,kBAAkBJ,WAAW,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;UACvD;QACF;QACA;IACJ;IAEA,IAAIP,gBAAgB,CAACK,MAAM,GAAG,CAAC,EAAE;MAC/B,MAAMG,OAAO,GAAG,MAAMjB,OAAO,CAACS,gBAAgB,CAAC;MAC/C,IAAI,CAACQ,OAAO,EAAE;IAChB;IAEA7B,cAAc,CAAC8B,IAAI,IAAIC,IAAI,CAACtD,GAAG,CAACqD,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3BhC,cAAc,CAAC8B,IAAI,IAAIC,IAAI,CAAC7C,GAAG,CAAC4C,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAACC,YAAY,EAAEC,IAAI,KAAK;IAC/C,IAAI,CAACA,IAAI,EAAE;;IAEX;IACA,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,iBAAiB,CAAC;IACnE,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;MACrCjF,KAAK,CAACsE,KAAK,CAAC,2CAA2C,CAAC;MACxD;IACF;;IAEA;IACA,IAAIQ,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/BlF,KAAK,CAACsE,KAAK,CAAC,iCAAiC,CAAC;MAC9C;IACF;IAEAzB,YAAY,CAAC4B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACI,YAAY,GAAGC;IAClB,CAAC,CAAC,CAAC;IAEH9E,KAAK,CAACmF,OAAO,CAAC,GAAGN,YAAY,wBAAwB,CAAC;EACxD,CAAC;EAED,MAAMO,QAAQ,GAAG,MAAOC,IAAI,IAAK;IAC/B5C,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF;MACA,MAAM6C,aAAa,GAAG;QACpB,GAAGD,IAAI;QACP1D,WAAW,EAAE,IAAIG,IAAI,CAACuD,IAAI,CAAC1D,WAAW,CAAC,CAAC4D,WAAW,CAAC;MACtD,CAAC;MAEDC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,aAAa,CAAC;MAC3DE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,4CAA4C,CAAC;MAE1E,MAAMC,QAAQ,GAAG,MAAM3F,QAAQ,CAACoD,QAAQ,CAACmC,aAAa,CAAC;MACvDE,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,QAAQ,CAAC;MAE/C,IAAIA,QAAQ,CAACP,OAAO,EAAE;QACpBnF,KAAK,CAACmF,OAAO,CAAC,4CAA4C,CAAC;QAC3D;QACA,IAAI5C,QAAQ,CAACoD,QAAQ,KAAK,WAAW,EAAE;UACrC;UACArD,QAAQ,CAAC,QAAQ,EAAE;YACjBJ,KAAK,EAAE;cAAE0D,OAAO,EAAE;YAA+D;UACnF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAtD,QAAQ,CAAC,SAAS,CAAC;QACrB;MACF;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MAAA,IAAAuB,eAAA,EAAAC,oBAAA;MACdN,OAAO,CAAClB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMsB,OAAO,GAAG,EAAAC,eAAA,GAAAvB,KAAK,CAACoB,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBR,IAAI,cAAAS,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,wCAAwC;MACzF5F,KAAK,CAACsE,KAAK,CAACsB,OAAO,CAAC;IACtB,CAAC,SAAS;MACRnD,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,oBACE7B,OAAA;IAAKmF,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBpF,OAAA;MAAAoF,QAAA,gBACEpF,OAAA;QAAImF,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxExF,OAAA;QAAGmF,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNxF,OAAA;MAAKmF,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7CpF,OAAA;QAAK,cAAW,UAAU;QAAAoF,QAAA,eACxBpF,OAAA;UAAImF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC9BrC,KAAK,CAAC0C,GAAG,CAAC,CAACC,IAAI,EAAEC,OAAO,KAAK;YAC5B,MAAMC,IAAI,GAAGF,IAAI,CAACxC,IAAI;YACtB,MAAM2C,WAAW,GAAG/D,WAAW,GAAG4D,IAAI,CAAC1C,EAAE;YACzC,MAAM8C,SAAS,GAAGhE,WAAW,KAAK4D,IAAI,CAAC1C,EAAE;YAEzC,oBACEhD,OAAA;cAAoBmF,SAAS,EAAE,GAAGQ,OAAO,KAAK5C,KAAK,CAACU,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAA2B,QAAA,eAC/EpF,OAAA;gBAAKmF,SAAS,EAAE,qBAAqBQ,OAAO,KAAK5C,KAAK,CAACU,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAAA2B,QAAA,gBAClFpF,OAAA;kBAAKmF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCpF,OAAA;oBAAKmF,SAAS,EAAE;AACtC;AACA,0BAA0BU,WAAW,GACT,wCAAwC,GACxCC,SAAS,GACP,+BAA+B,GAC/B,+BAA+B;AAC7D,uBACwB;oBAAAV,QAAA,eACApF,OAAA,CAAC4F,IAAI;sBAACT,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACNxF,OAAA;oBAAMmF,SAAS,EAAE,4BACfW,SAAS,GAAG,eAAe,GAAGD,WAAW,GAAG,eAAe,GAAG,eAAe,EAC5E;oBAAAT,QAAA,EACAM,IAAI,CAACzC;kBAAI;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACLG,OAAO,KAAK5C,KAAK,CAACU,MAAM,GAAG,CAAC,iBAC3BzD,OAAA;kBAAKmF,SAAS,EAAE,qBACdU,WAAW,GAAG,aAAa,GAAG,aAAa;gBAC1C;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GAzBCE,IAAI,CAACzC,IAAI;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Bd,CAAC;UAET,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxF,OAAA;MAAMwE,QAAQ,EAAEhC,YAAY,CAACgC,QAAQ,CAAE;MAACW,SAAS,EAAC,gCAAgC;MAAAC,QAAA,GAE/EtD,WAAW,KAAK,CAAC,iBAChB9B,OAAA;QAAKmF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpF,OAAA;UAAAoF,QAAA,eACEpF,OAAA;YAAImF,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtEpF,OAAA,CAACX,IAAI;cAAC8F,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDpF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAOmF,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cAAA,GACMuC,QAAQ,CAAC,WAAW,CAAC;cACzB8B,IAAI,EAAC,MAAM;cACXc,SAAS,EAAC,6GAA6G;cACvHY,WAAW,EAAC;YAAuB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,EACD9C,MAAM,CAACrC,SAAS,iBACfL,OAAA;cAAGmF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1C,MAAM,CAACrC,SAAS,CAAC2E;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACvE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAOmF,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cAAA,GACMuC,QAAQ,CAAC,UAAU,CAAC;cACxB8B,IAAI,EAAC,MAAM;cACXc,SAAS,EAAC,6GAA6G;cACvHY,WAAW,EAAC;YAAsB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,EACD9C,MAAM,CAACjC,QAAQ,iBACdT,OAAA;cAAGmF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1C,MAAM,CAACjC,QAAQ,CAACuE;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACtE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAOmF,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cAAKmF,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BpF,OAAA;gBAAA,GACMuC,QAAQ,CAAC,OAAO,CAAC;gBACrB8B,IAAI,EAAC,OAAO;gBACZc,SAAS,EAAC,8GAA8G;gBACxHY,WAAW,EAAC;cAAkB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACFxF,OAAA,CAACV,IAAI;gBAAC6F,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,EACL9C,MAAM,CAAChC,KAAK,iBACXV,OAAA;cAAGmF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1C,MAAM,CAAChC,KAAK,CAACsE;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACnE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAOmF,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cAAKmF,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BpF,OAAA;gBAAA,GACMuC,QAAQ,CAAC,aAAa,CAAC;gBAC3B8B,IAAI,EAAC,KAAK;gBACVc,SAAS,EAAC,8GAA8G;gBACxHY,WAAW,EAAC;cAAwB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACFxF,OAAA,CAACT,KAAK;gBAAC4F,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,EACL9C,MAAM,CAAC/B,WAAW,iBACjBX,OAAA;cAAGmF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1C,MAAM,CAAC/B,WAAW,CAACqE;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAOmF,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cAAKmF,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BpF,OAAA;gBAAA,GACMuC,QAAQ,CAAC,aAAa,CAAC;gBAC3B8B,IAAI,EAAC,MAAM;gBACXc,SAAS,EAAC;cAA8G;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzH,CAAC,eACFxF,OAAA,CAACP,QAAQ;gBAAC0F,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,EACL9C,MAAM,CAAC3B,WAAW,iBACjBf,OAAA;cAAGmF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1C,MAAM,CAAC3B,WAAW,CAACiE;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACzE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA1D,WAAW,KAAK,CAAC,iBAChB9B,OAAA;QAAKmF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpF,OAAA;UAAAoF,QAAA,eACEpF,OAAA;YAAImF,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtEpF,OAAA,CAACR,MAAM;cAAC2F,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8BAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDpF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAOmF,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cAAKmF,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BpF,OAAA;gBAAA,GACMuC,QAAQ,CAAC,cAAc,CAAC;gBAC5B8B,IAAI,EAAC,MAAM;gBACX2B,SAAS,EAAC,IAAI;gBACdb,SAAS,EAAC,8GAA8G;gBACxHY,WAAW,EAAC;cAAwB;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACFxF,OAAA,CAACN,UAAU;gBAACyF,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,EACL9C,MAAM,CAAC7B,YAAY,iBAClBb,OAAA;cAAGmF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1C,MAAM,CAAC7B,YAAY,CAACmE;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC1E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAOmF,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cAAKmF,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5BpF,OAAA;gBAAA,GACMuC,QAAQ,CAAC,WAAW,CAAC;gBACzB8B,IAAI,EAAC,MAAM;gBACX2B,SAAS,EAAC,IAAI;gBACdb,SAAS,EAAC,8GAA8G;gBACxHY,WAAW,EAAC,YAAY;gBACxBE,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAY;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACFxF,OAAA,CAACN,UAAU;gBAACyF,SAAS,EAAC;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,EACL9C,MAAM,CAAC5B,SAAS,iBACfd,OAAA;cAAGmF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1C,MAAM,CAAC5B,SAAS,CAACkE;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACvE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxF,OAAA;YAAKmF,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BpF,OAAA;cAAOmF,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cAAA,GACMuC,QAAQ,CAAC,SAAS,CAAC;cACvB4D,IAAI,EAAE,CAAE;cACRhB,SAAS,EAAC,6GAA6G;cACvHY,WAAW,EAAC;YAA6B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,EACD9C,MAAM,CAACtB,OAAO,iBACbpB,OAAA;cAAGmF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1C,MAAM,CAACtB,OAAO,CAAC4D;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACrE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAOmF,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cAAA,GACMuC,QAAQ,CAAC,MAAM,CAAC;cACpB8B,IAAI,EAAC,MAAM;cACXc,SAAS,EAAC,6GAA6G;cACvHY,WAAW,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACD9C,MAAM,CAACrB,IAAI,iBACVrB,OAAA;cAAGmF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1C,MAAM,CAACrB,IAAI,CAAC2D;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAClE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAOmF,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cAAA,GACMuC,QAAQ,CAAC,OAAO,CAAC;cACrB4C,SAAS,EAAC,6GAA6G;cAAAC,QAAA,gBAEvHpF,OAAA;gBAAQoG,KAAK,EAAC,EAAE;gBAAAhB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCxF,OAAA;gBAAQoG,KAAK,EAAC,gBAAgB;gBAAAhB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtDxF,OAAA;gBAAQoG,KAAK,EAAC,mBAAmB;gBAAAhB,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5DxF,OAAA;gBAAQoG,KAAK,EAAC,OAAO;gBAAAhB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCxF,OAAA;gBAAQoG,KAAK,EAAC,OAAO;gBAAAhB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCxF,OAAA;gBAAQoG,KAAK,EAAC,cAAc;gBAAAhB,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClDxF,OAAA;gBAAQoG,KAAK,EAAC,KAAK;gBAAAhB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCxF,OAAA;gBAAQoG,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCxF,OAAA;gBAAQoG,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCxF,OAAA;gBAAQoG,KAAK,EAAC,kBAAkB;gBAAAhB,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1DxF,OAAA;gBAAQoG,KAAK,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxF,OAAA;gBAAQoG,KAAK,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxF,OAAA;gBAAQoG,KAAK,EAAC,QAAQ;gBAAAhB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCxF,OAAA;gBAAQoG,KAAK,EAAC,gBAAgB;gBAAAhB,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtDxF,OAAA;gBAAQoG,KAAK,EAAC,aAAa;gBAAAhB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChDxF,OAAA;gBAAQoG,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCxF,OAAA;gBAAQoG,KAAK,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxF,OAAA;gBAAQoG,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCxF,OAAA;gBAAQoG,KAAK,EAAC,UAAU;gBAAAhB,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1CxF,OAAA;gBAAQoG,KAAK,EAAC,QAAQ;gBAAAhB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCxF,OAAA;gBAAQoG,KAAK,EAAC,QAAQ;gBAAAhB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCxF,OAAA;gBAAQoG,KAAK,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxF,OAAA;gBAAQoG,KAAK,EAAC,QAAQ;gBAAAhB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCxF,OAAA;gBAAQoG,KAAK,EAAC,YAAY;gBAAAhB,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9CxF,OAAA;gBAAQoG,KAAK,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CxF,OAAA;gBAAQoG,KAAK,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCxF,OAAA;gBAAQoG,KAAK,EAAC,eAAe;gBAAAhB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpDxF,OAAA;gBAAQoG,KAAK,EAAC,aAAa;gBAAAhB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChDxF,OAAA;gBAAQoG,KAAK,EAAC,aAAa;gBAAAhB,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChDxF,OAAA;gBAAQoG,KAAK,EAAC,OAAO;gBAAAhB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACR9C,MAAM,CAACpB,KAAK,iBACXtB,OAAA;cAAGmF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1C,MAAM,CAACpB,KAAK,CAAC0D;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACnE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAOmF,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRxF,OAAA;cAAA,GACMuC,QAAQ,CAAC,SAAS,CAAC;cACvB8B,IAAI,EAAC,MAAM;cACX2B,SAAS,EAAC,GAAG;cACbb,SAAS,EAAC,6GAA6G;cACvHY,WAAW,EAAC;YAAkB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,EACD9C,MAAM,CAACnB,OAAO,iBACbvB,OAAA;cAAGmF,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE1C,MAAM,CAACnB,OAAO,CAACyD;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACrE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA1D,WAAW,KAAK,CAAC,iBAChB9B,OAAA;QAAKmF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpF,OAAA;UAAAoF,QAAA,gBACEpF,OAAA;YAAImF,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtEpF,OAAA,CAACL,MAAM;cAACwF,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxF,OAAA;YAAGmF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnD,CACC;YAAEiB,GAAG,EAAE,YAAY;YAAEC,KAAK,EAAE,aAAa;YAAE/F,QAAQ,EAAE;UAAK,CAAC,EAC3D;YAAE8F,GAAG,EAAE,SAAS;YAAEC,KAAK,EAAE,UAAU;YAAE/F,QAAQ,EAAE;UAAK,CAAC,EACrD;YAAE8F,GAAG,EAAE,OAAO;YAAEC,KAAK,EAAE,qBAAqB;YAAE/F,QAAQ,EAAE;UAAK,CAAC,EAC9D;YAAE8F,GAAG,EAAE,cAAc;YAAEC,KAAK,EAAE,yBAAyB;YAAE/F,QAAQ,EAAE;UAAM,CAAC,EAC1E;YAAE8F,GAAG,EAAE,eAAe;YAAEC,KAAK,EAAE,gCAAgC;YAAE/F,QAAQ,EAAE;UAAM,CAAC,CACnF,CAACkF,GAAG,CAAEjC,GAAG,iBACRxD,OAAA;YAAmBmF,SAAS,EAAC,+FAA+F;YAAAC,QAAA,eAC1HpF,OAAA;cAAKmF,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BpF,OAAA,CAACL,MAAM;gBAACwF,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtDxF,OAAA;gBAAKmF,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBpF,OAAA;kBAAOmF,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC/BpF,OAAA;oBAAMmF,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,GAC3D5B,GAAG,CAAC8C,KAAK,EAAC,GAAC,EAAC9C,GAAG,CAACjD,QAAQ,IAAI,GAAG;kBAAA;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACPxF,OAAA;oBACEqE,IAAI,EAAC,MAAM;oBACXc,SAAS,EAAC,QAAQ;oBAClBoB,MAAM,EAAC,sBAAsB;oBAC7BC,QAAQ,EAAGC,CAAC,IAAKzC,gBAAgB,CAACR,GAAG,CAAC6C,GAAG,EAAEI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACFxF,OAAA;oBAAMmF,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EACLxD,SAAS,CAACwB,GAAG,CAAC6C,GAAG,CAAC,iBACjBrG,OAAA;gBAAKmF,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,gBAC3EpF,OAAA,CAACJ,QAAQ;kBAACuF,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpCxD,SAAS,CAACwB,GAAG,CAAC6C,GAAG,CAAC,CAACpD,IAAI;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GAzBEhC,GAAG,CAAC6C,GAAG;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA1D,WAAW,KAAK,CAAC,iBAChB9B,OAAA;QAAKmF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBpF,OAAA;UAAAoF,QAAA,gBACEpF,OAAA;YAAImF,SAAS,EAAC,0DAA0D;YAAAC,QAAA,gBACtEpF,OAAA,CAACJ,QAAQ;cAACuF,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxF,OAAA;YAAGmF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClDpF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAImF,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxExF,OAAA;cAAKmF,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CpF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAMmF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5CxF,OAAA;kBAAMmF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAAExC,SAAS,CAAC,WAAW,CAAC,EAAC,GAAC,EAACA,SAAS,CAAC,UAAU,CAAC;gBAAA;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC,eACNxF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAMmF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7CxF,OAAA;kBAAMmF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAExC,SAAS,CAAC,OAAO;gBAAC;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACNxF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAMmF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7CxF,OAAA;kBAAMmF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAExC,SAAS,CAAC,aAAa;gBAAC;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC,eACNxF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAMmF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrDxF,OAAA;kBAAMmF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAExC,SAAS,CAAC,aAAa;gBAAC;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAImF,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtExF,OAAA;cAAKmF,SAAS,EAAC,gCAAgC;cAAAC,QAAA,gBAC7CpF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAMmF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9CxF,OAAA;kBAAMmF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAExC,SAAS,CAAC,cAAc;gBAAC;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNxF,OAAA;gBAAAoF,QAAA,gBACEpF,OAAA;kBAAMmF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3CxF,OAAA;kBAAMmF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAExC,SAAS,CAAC,WAAW;gBAAC;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNxF,OAAA;gBAAKmF,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpF,OAAA;kBAAMmF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/CxF,OAAA;kBAAMmF,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GACjCxC,SAAS,CAAC,SAAS,CAAC,EAAC,IAAE,EAACA,SAAS,CAAC,MAAM,CAAC,EAAC,IAAE,EAACA,SAAS,CAAC,OAAO,CAAC,EAAC,KAAG,EAACA,SAAS,CAAC,SAAS,CAAC;gBAAA;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxF,OAAA;YAAAoF,QAAA,gBACEpF,OAAA;cAAImF,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtExF,OAAA;cAAKmF,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAC5CwB,MAAM,CAACC,OAAO,CAAC7E,SAAS,CAAC,CAACyD,GAAG,CAAC,CAAC,CAACY,GAAG,EAAEnC,IAAI,CAAC,KACzCA,IAAI,iBACFlE,OAAA;gBAAemF,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBACzDpF,OAAA,CAACJ,QAAQ;kBAACuF,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpCa,GAAG,CAACS,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,IAAI,EAAEC,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC;cAAA,GAF/DX,GAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGR,CAER;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxF,OAAA;UAAKmF,SAAS,EAAC,kDAAkD;UAAAC,QAAA,eAC/DpF,OAAA;YAAKmF,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBpF,OAAA;cAAKmF,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BpF,OAAA,CAACJ,QAAQ;gBAACuF,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACNxF,OAAA;cAAKmF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBpF,OAAA;gBAAImF,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAElD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxF,OAAA;gBAAKmF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,eACzCpF,OAAA;kBAAImF,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,gBAC7CpF,OAAA;oBAAAoF,QAAA,EAAI;kBAA0D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnExF,OAAA;oBAAAoF,QAAA,EAAI;kBAAyD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClExF,OAAA;oBAAAoF,QAAA,EAAI;kBAAoD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7DxF,OAAA;oBAAAoF,QAAA,EAAI;kBAAuD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDxF,OAAA;QAAKmF,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjEpF,OAAA;UACEqE,IAAI,EAAC,QAAQ;UACb4C,OAAO,EAAElD,cAAe;UACxBmD,QAAQ,EAAEpF,WAAW,KAAK,CAAE;UAC5BqD,SAAS,EAAC,oQAAoQ;UAAAC,QAAA,EAC/Q;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAER1D,WAAW,GAAG,CAAC,gBACd9B,OAAA;UACEqE,IAAI,EAAC,QAAQ;UACb4C,OAAO,EAAE9D,UAAW;UACpBgC,SAAS,EAAC,wNAAwN;UAAAC,QAAA,EACnO;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETxF,OAAA;UACEqE,IAAI,EAAC,QAAQ;UACb6C,QAAQ,EAAEtF,YAAa;UACvBuD,SAAS,EAAC,2QAA2Q;UAAAC,QAAA,EAEpRxD,YAAY,gBACX5B,OAAA,CAAAE,SAAA;YAAAkF,QAAA,gBACEpF,OAAA;cAAKmF,SAAS,EAAC;YAAgE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,iBAExF;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC/D,EAAA,CA9lBID,iBAAiB;EAAA,QACJvC,WAAW,EACXC,WAAW,EAiBxBJ,OAAO;AAAA;AAAAqI,EAAA,GAnBP3F,iBAAiB;AAgmBvB,eAAeA,iBAAiB;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}