using IDFCAgentAPI.Models;

namespace IDFCAgentAPI.Services
{
    public interface IDocumentService
    {
        Task<AgentDocument> UploadDocumentAsync(int agentId, DocumentType documentType, IFormFile file);
        Task<AgentDocument?> GetDocumentByIdAsync(int documentId);
        Task<IEnumerable<AgentDocument>> GetDocumentsByAgentIdAsync(int agentId);
        Task<IEnumerable<AgentDocument>> GetDocumentsByStatusAsync(DocumentStatus status);
        Task<bool> UpdateDocumentStatusAsync(int documentId, DocumentStatus status, string reviewedBy, string? comments = null);
        Task<bool> DeleteDocumentAsync(int documentId);
        Task<byte[]?> GetDocumentContentAsync(int documentId);
        Task<bool> DocumentExistsAsync(int documentId);
        Task<string> SaveFileAsync(IFormFile file, string directory);
        Task<bool> DeleteFileAsync(string filePath);
    }
}
