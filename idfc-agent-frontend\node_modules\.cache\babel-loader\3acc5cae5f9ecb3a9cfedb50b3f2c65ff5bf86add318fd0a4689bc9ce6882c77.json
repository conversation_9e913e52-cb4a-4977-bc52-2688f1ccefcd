{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"line\", {\n  x1: \"22\",\n  x2: \"18\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"l9bcsi\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"2\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"13hhkx\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"6\",\n  y2: \"2\",\n  key: \"10w3f3\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"22\",\n  y2: \"18\",\n  key: \"15g9kq\"\n}]];\nconst Crosshair = createLucideIcon(\"crosshair\", __iconNode);\nexport { __iconNode, Crosshair as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "x1", "x2", "y1", "y2", "<PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\node_modules\\lucide-react\\src\\icons\\crosshair.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '22', x2: '18', y1: '12', y2: '12', key: 'l9bcsi' }],\n  ['line', { x1: '6', x2: '2', y1: '12', y2: '12', key: '13hhkx' }],\n  ['line', { x1: '12', x2: '12', y1: '6', y2: '2', key: '10w3f3' }],\n  ['line', { x1: '12', x2: '12', y1: '22', y2: '18', key: '15g9kq' }],\n];\n\n/**\n * @component @name Crosshair\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMjIiIHgyPSIxOCIgeTE9IjEyIiB5Mj0iMTIiIC8+CiAgPGxpbmUgeDE9IjYiIHgyPSIyIiB5MT0iMTIiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjYiIHkyPSIyIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMjIiIHkyPSIxOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/crosshair\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Crosshair = createLucideIcon('crosshair', __iconNode);\n\nexport default Crosshair;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAMC,GAAK;AAAA,CAAU,GACzD,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAU,GACpE;AAaM,MAAAK,SAAA,GAAYC,gBAAiB,cAAaV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}