{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\IDFCAgentOnboardingAndManagementSolution\\\\idfc-agent-frontend\\\\src\\\\components\\\\WorkflowStatusUpdate.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { workflowAPI } from '../services/api';\nimport { toast } from 'react-toastify';\nimport { CheckCircle, XCircle, AlertTriangle, Clock, MessageSquare, User, FileText, Shield, Activity } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkflowStatusUpdate = ({\n  agentId,\n  currentStatus,\n  onStatusUpdated,\n  onClose\n}) => {\n  _s();\n  const [validStatuses, setValidStatuses] = useState([]);\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [comments, setComments] = useState('');\n  const [validation, setValidation] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [validating, setValidating] = useState(false);\n  useEffect(() => {\n    fetchValidStatuses();\n  }, [agentId]);\n  useEffect(() => {\n    if (selectedStatus) {\n      validateTransition();\n    }\n  }, [selectedStatus]);\n  const fetchValidStatuses = async () => {\n    try {\n      const statuses = await workflowAPI.getValidNextStatuses(agentId);\n      setValidStatuses(statuses);\n    } catch (error) {\n      console.error('Error fetching valid statuses:', error);\n      toast.error('Failed to load valid status options');\n    }\n  };\n  const validateTransition = async () => {\n    if (!selectedStatus) return;\n    try {\n      setValidating(true);\n      const result = await workflowAPI.validateStatusTransition(agentId, parseInt(selectedStatus));\n      setValidation(result);\n    } catch (error) {\n      console.error('Error validating transition:', error);\n      toast.error('Failed to validate status transition');\n    } finally {\n      setValidating(false);\n    }\n  };\n  const handleStatusUpdate = async () => {\n    if (!selectedStatus) {\n      toast.error('Please select a status');\n      return;\n    }\n    if (validation && !validation.isValid) {\n      toast.error('Cannot proceed with invalid status transition');\n      return;\n    }\n    try {\n      setLoading(true);\n      await workflowAPI.processStatusTransition(agentId, parseInt(selectedStatus), comments);\n      toast.success('Status updated successfully');\n      onStatusUpdated();\n      onClose();\n    } catch (error) {\n      console.error('Error updating status:', error);\n      toast.error('Failed to update status');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getStatusInfo = status => {\n    const statusMap = {\n      0: {\n        name: 'Pending',\n        icon: Clock,\n        color: 'text-yellow-600',\n        bgColor: 'bg-yellow-50',\n        borderColor: 'border-yellow-200'\n      },\n      1: {\n        name: 'Under Review',\n        icon: FileText,\n        color: 'text-blue-600',\n        bgColor: 'bg-blue-50',\n        borderColor: 'border-blue-200'\n      },\n      2: {\n        name: 'Approved',\n        icon: CheckCircle,\n        color: 'text-green-600',\n        bgColor: 'bg-green-50',\n        borderColor: 'border-green-200'\n      },\n      3: {\n        name: 'Rejected',\n        icon: XCircle,\n        color: 'text-red-600',\n        bgColor: 'bg-red-50',\n        borderColor: 'border-red-200'\n      },\n      4: {\n        name: 'Active',\n        icon: Activity,\n        color: 'text-green-600',\n        bgColor: 'bg-green-50',\n        borderColor: 'border-green-200'\n      },\n      5: {\n        name: 'Inactive',\n        icon: Clock,\n        color: 'text-gray-600',\n        bgColor: 'bg-gray-50',\n        borderColor: 'border-gray-200'\n      },\n      6: {\n        name: 'Suspended',\n        icon: AlertTriangle,\n        color: 'text-red-600',\n        bgColor: 'bg-red-50',\n        borderColor: 'border-red-200'\n      }\n    };\n    return statusMap[status] || statusMap[0];\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"Update Agent Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"text-gray-400 hover:text-gray-600\",\n            children: /*#__PURE__*/_jsxDEV(XCircle, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 p-4 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-gray-700 mb-2\",\n            children: \"Current Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/React.createElement(getStatusInfo(currentStatus).icon, {\n              className: `w-5 h-5 ${getStatusInfo(currentStatus).color}`\n            }), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: getStatusInfo(currentStatus).name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-3\",\n            children: \"Select New Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-3\",\n            children: validStatuses.map(status => {\n              const statusInfo = getStatusInfo(status);\n              const isSelected = selectedStatus === status.toString();\n              return /*#__PURE__*/_jsxDEV(\"label\", {\n                className: `relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${isSelected ? `${statusInfo.borderColor} ${statusInfo.bgColor}` : 'border-gray-200'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"status\",\n                  value: status,\n                  checked: isSelected,\n                  onChange: e => setSelectedStatus(e.target.value),\n                  className: \"sr-only\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/React.createElement(statusInfo.icon, {\n                    className: `w-5 h-5 ${statusInfo.color}`\n                  }), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `font-medium ${isSelected ? statusInfo.color : 'text-gray-900'}`,\n                    children: statusInfo.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this), isSelected && /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: `w-5 h-5 ml-auto ${statusInfo.color}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 23\n                }, this)]\n              }, status, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), validating && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(Clock, {\n              className: \"w-5 h-5 text-blue-500 animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-700\",\n              children: \"Validating status transition...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), validation && !validating && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `mb-6 p-4 border rounded-lg ${validation.isValid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [validation.isValid ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-5 h-5 text-green-500 mt-0.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(XCircle, {\n              className: \"w-5 h-5 text-red-500 mt-0.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: `font-medium ${validation.isValid ? 'text-green-800' : 'text-red-800'}`,\n                children: validation.isValid ? 'Transition Valid' : 'Transition Invalid'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this), validation.errorMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-red-700 mt-1\",\n                children: validation.errorMessage\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 21\n              }, this), validation.requiredActions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-red-800\",\n                  children: \"Required Actions:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"text-sm text-red-700 mt-1 space-y-1\",\n                  children: validation.requiredActions.map((action, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500 mt-1\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: action\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 29\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), validation.missingDocuments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm font-medium text-red-800\",\n                  children: \"Missing Documents:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"text-sm text-red-700 mt-1 space-y-1\",\n                  children: validation.missingDocuments.map((doc, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"flex items-start space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-red-500 mt-1\",\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: doc\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 29\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 21\n              }, this), validation.requiresManualReview && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                  className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-yellow-700\",\n                  children: \"Manual review required for this transition\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"comments\",\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: [\"Comments \", selectedStatus === '3' && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 51\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            id: \"comments\",\n            rows: 4,\n            value: comments,\n            onChange: e => setComments(e.target.value),\n            placeholder: \"Add comments about this status change...\",\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this), selectedStatus === '3' && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 mt-1\",\n            children: \"Comments are required when rejecting an application\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-end space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleStatusUpdate,\n            disabled: loading || !selectedStatus || validation && !validation.isValid || selectedStatus === '3' && !comments.trim(),\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: loading ? 'Updating...' : 'Update Status'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkflowStatusUpdate, \"aZUNDLFMXkdxw3O53iCrInHvgAA=\");\n_c = WorkflowStatusUpdate;\nexport default WorkflowStatusUpdate;\nvar _c;\n$RefreshReg$(_c, \"WorkflowStatusUpdate\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "workflowAPI", "toast", "CheckCircle", "XCircle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "MessageSquare", "User", "FileText", "Shield", "Activity", "jsxDEV", "_jsxDEV", "WorkflowStatusUpdate", "agentId", "currentStatus", "onStatusUpdated", "onClose", "_s", "validStatuses", "setValidStatuses", "selectedStatus", "setSelectedStatus", "comments", "setComments", "validation", "setValidation", "loading", "setLoading", "validating", "setValidating", "fetchValidStatuses", "validateTransition", "statuses", "getValidNextStatuses", "error", "console", "result", "validateStatusTransition", "parseInt", "handleStatusUpdate", "<PERSON><PERSON><PERSON><PERSON>", "processStatusTransition", "success", "getStatusInfo", "status", "statusMap", "name", "icon", "color", "bgColor", "borderColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "createElement", "map", "statusInfo", "isSelected", "toString", "type", "value", "checked", "onChange", "e", "target", "errorMessage", "requiredActions", "length", "action", "index", "missingDocuments", "doc", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "htmlFor", "id", "rows", "placeholder", "disabled", "trim", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/IDFCAgentOnboardingAndManagementSolution/idfc-agent-frontend/src/components/WorkflowStatusUpdate.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { workflowAPI } from '../services/api';\nimport { toast } from 'react-toastify';\nimport { \n  CheckCircle, \n  XCircle, \n  AlertTriangle, \n  Clock, \n  MessageSquare,\n  User,\n  FileText,\n  Shield,\n  Activity\n} from 'lucide-react';\n\nconst WorkflowStatusUpdate = ({ agentId, currentStatus, onStatusUpdated, onClose }) => {\n  const [validStatuses, setValidStatuses] = useState([]);\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [comments, setComments] = useState('');\n  const [validation, setValidation] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [validating, setValidating] = useState(false);\n\n  useEffect(() => {\n    fetchValidStatuses();\n  }, [agentId]);\n\n  useEffect(() => {\n    if (selectedStatus) {\n      validateTransition();\n    }\n  }, [selectedStatus]);\n\n  const fetchValidStatuses = async () => {\n    try {\n      const statuses = await workflowAPI.getValidNextStatuses(agentId);\n      setValidStatuses(statuses);\n    } catch (error) {\n      console.error('Error fetching valid statuses:', error);\n      toast.error('Failed to load valid status options');\n    }\n  };\n\n  const validateTransition = async () => {\n    if (!selectedStatus) return;\n\n    try {\n      setValidating(true);\n      const result = await workflowAPI.validateStatusTransition(agentId, parseInt(selectedStatus));\n      setValidation(result);\n    } catch (error) {\n      console.error('Error validating transition:', error);\n      toast.error('Failed to validate status transition');\n    } finally {\n      setValidating(false);\n    }\n  };\n\n  const handleStatusUpdate = async () => {\n    if (!selectedStatus) {\n      toast.error('Please select a status');\n      return;\n    }\n\n    if (validation && !validation.isValid) {\n      toast.error('Cannot proceed with invalid status transition');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      await workflowAPI.processStatusTransition(agentId, parseInt(selectedStatus), comments);\n      toast.success('Status updated successfully');\n      onStatusUpdated();\n      onClose();\n    } catch (error) {\n      console.error('Error updating status:', error);\n      toast.error('Failed to update status');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusInfo = (status) => {\n    const statusMap = {\n      0: { name: 'Pending', icon: Clock, color: 'text-yellow-600', bgColor: 'bg-yellow-50', borderColor: 'border-yellow-200' },\n      1: { name: 'Under Review', icon: FileText, color: 'text-blue-600', bgColor: 'bg-blue-50', borderColor: 'border-blue-200' },\n      2: { name: 'Approved', icon: CheckCircle, color: 'text-green-600', bgColor: 'bg-green-50', borderColor: 'border-green-200' },\n      3: { name: 'Rejected', icon: XCircle, color: 'text-red-600', bgColor: 'bg-red-50', borderColor: 'border-red-200' },\n      4: { name: 'Active', icon: Activity, color: 'text-green-600', bgColor: 'bg-green-50', borderColor: 'border-green-200' },\n      5: { name: 'Inactive', icon: Clock, color: 'text-gray-600', bgColor: 'bg-gray-50', borderColor: 'border-gray-200' },\n      6: { name: 'Suspended', icon: AlertTriangle, color: 'text-red-600', bgColor: 'bg-red-50', borderColor: 'border-red-200' }\n    };\n    return statusMap[status] || statusMap[0];\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\">\n        <div className=\"mt-3\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Update Agent Status</h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              <XCircle className=\"w-6 h-6\" />\n            </button>\n          </div>\n\n          {/* Current Status */}\n          <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n            <h4 className=\"text-sm font-medium text-gray-700 mb-2\">Current Status</h4>\n            <div className=\"flex items-center space-x-3\">\n              {React.createElement(getStatusInfo(currentStatus).icon, {\n                className: `w-5 h-5 ${getStatusInfo(currentStatus).color}`\n              })}\n              <span className=\"font-medium\">{getStatusInfo(currentStatus).name}</span>\n            </div>\n          </div>\n\n          {/* Status Selection */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n              Select New Status\n            </label>\n            <div className=\"grid grid-cols-1 gap-3\">\n              {validStatuses.map((status) => {\n                const statusInfo = getStatusInfo(status);\n                const isSelected = selectedStatus === status.toString();\n                \n                return (\n                  <label\n                    key={status}\n                    className={`relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${\n                      isSelected \n                        ? `${statusInfo.borderColor} ${statusInfo.bgColor}` \n                        : 'border-gray-200'\n                    }`}\n                  >\n                    <input\n                      type=\"radio\"\n                      name=\"status\"\n                      value={status}\n                      checked={isSelected}\n                      onChange={(e) => setSelectedStatus(e.target.value)}\n                      className=\"sr-only\"\n                    />\n                    <div className=\"flex items-center space-x-3\">\n                      {React.createElement(statusInfo.icon, {\n                        className: `w-5 h-5 ${statusInfo.color}`\n                      })}\n                      <span className={`font-medium ${isSelected ? statusInfo.color : 'text-gray-900'}`}>\n                        {statusInfo.name}\n                      </span>\n                    </div>\n                    {isSelected && (\n                      <CheckCircle className={`w-5 h-5 ml-auto ${statusInfo.color}`} />\n                    )}\n                  </label>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Validation Results */}\n          {validating && (\n            <div className=\"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n              <div className=\"flex items-center space-x-2\">\n                <Clock className=\"w-5 h-5 text-blue-500 animate-spin\" />\n                <span className=\"text-blue-700\">Validating status transition...</span>\n              </div>\n            </div>\n          )}\n\n          {validation && !validating && (\n            <div className={`mb-6 p-4 border rounded-lg ${\n              validation.isValid \n                ? 'bg-green-50 border-green-200' \n                : 'bg-red-50 border-red-200'\n            }`}>\n              <div className=\"flex items-start space-x-3\">\n                {validation.isValid ? (\n                  <CheckCircle className=\"w-5 h-5 text-green-500 mt-0.5\" />\n                ) : (\n                  <XCircle className=\"w-5 h-5 text-red-500 mt-0.5\" />\n                )}\n                <div className=\"flex-1\">\n                  <h4 className={`font-medium ${\n                    validation.isValid ? 'text-green-800' : 'text-red-800'\n                  }`}>\n                    {validation.isValid ? 'Transition Valid' : 'Transition Invalid'}\n                  </h4>\n                  \n                  {validation.errorMessage && (\n                    <p className=\"text-sm text-red-700 mt-1\">{validation.errorMessage}</p>\n                  )}\n                  \n                  {validation.requiredActions.length > 0 && (\n                    <div className=\"mt-2\">\n                      <p className=\"text-sm font-medium text-red-800\">Required Actions:</p>\n                      <ul className=\"text-sm text-red-700 mt-1 space-y-1\">\n                        {validation.requiredActions.map((action, index) => (\n                          <li key={index} className=\"flex items-start space-x-2\">\n                            <span className=\"text-red-500 mt-1\">•</span>\n                            <span>{action}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                  \n                  {validation.missingDocuments.length > 0 && (\n                    <div className=\"mt-2\">\n                      <p className=\"text-sm font-medium text-red-800\">Missing Documents:</p>\n                      <ul className=\"text-sm text-red-700 mt-1 space-y-1\">\n                        {validation.missingDocuments.map((doc, index) => (\n                          <li key={index} className=\"flex items-start space-x-2\">\n                            <span className=\"text-red-500 mt-1\">•</span>\n                            <span>{doc}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                  \n                  {validation.requiresManualReview && (\n                    <div className=\"mt-2 flex items-center space-x-2\">\n                      <MessageSquare className=\"w-4 h-4 text-yellow-500\" />\n                      <span className=\"text-sm text-yellow-700\">Manual review required for this transition</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Comments */}\n          <div className=\"mb-6\">\n            <label htmlFor=\"comments\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Comments {selectedStatus === '3' && <span className=\"text-red-500\">*</span>}\n            </label>\n            <textarea\n              id=\"comments\"\n              rows={4}\n              value={comments}\n              onChange={(e) => setComments(e.target.value)}\n              placeholder=\"Add comments about this status change...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            />\n            {selectedStatus === '3' && (\n              <p className=\"text-sm text-gray-500 mt-1\">\n                Comments are required when rejecting an application\n              </p>\n            )}\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex items-center justify-end space-x-4\">\n            <button\n              onClick={onClose}\n              className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n            >\n              Cancel\n            </button>\n            <button\n              onClick={handleStatusUpdate}\n              disabled={loading || !selectedStatus || (validation && !validation.isValid) || (selectedStatus === '3' && !comments.trim())}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? 'Updating...' : 'Update Status'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WorkflowStatusUpdate;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,WAAW,EACXC,OAAO,EACPC,aAAa,EACbC,KAAK,EACLC,aAAa,EACbC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,QAAQ,QACH,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,aAAa;EAAEC,eAAe;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACdgC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACjB,OAAO,CAAC,CAAC;EAEbf,SAAS,CAAC,MAAM;IACd,IAAIsB,cAAc,EAAE;MAClBW,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACX,cAAc,CAAC,CAAC;EAEpB,MAAMU,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMjC,WAAW,CAACkC,oBAAoB,CAACpB,OAAO,CAAC;MAChEM,gBAAgB,CAACa,QAAQ,CAAC;IAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDlC,KAAK,CAACkC,KAAK,CAAC,qCAAqC,CAAC;IACpD;EACF,CAAC;EAED,MAAMH,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACX,cAAc,EAAE;IAErB,IAAI;MACFS,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMO,MAAM,GAAG,MAAMrC,WAAW,CAACsC,wBAAwB,CAACxB,OAAO,EAAEyB,QAAQ,CAAClB,cAAc,CAAC,CAAC;MAC5FK,aAAa,CAACW,MAAM,CAAC;IACvB,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDlC,KAAK,CAACkC,KAAK,CAAC,sCAAsC,CAAC;IACrD,CAAC,SAAS;MACRL,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMU,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAACnB,cAAc,EAAE;MACnBpB,KAAK,CAACkC,KAAK,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA,IAAIV,UAAU,IAAI,CAACA,UAAU,CAACgB,OAAO,EAAE;MACrCxC,KAAK,CAACkC,KAAK,CAAC,+CAA+C,CAAC;MAC5D;IACF;IAEA,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM5B,WAAW,CAAC0C,uBAAuB,CAAC5B,OAAO,EAAEyB,QAAQ,CAAClB,cAAc,CAAC,EAAEE,QAAQ,CAAC;MACtFtB,KAAK,CAAC0C,OAAO,CAAC,6BAA6B,CAAC;MAC5C3B,eAAe,CAAC,CAAC;MACjBC,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9ClC,KAAK,CAACkC,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,aAAa,GAAIC,MAAM,IAAK;IAChC,MAAMC,SAAS,GAAG;MAChB,CAAC,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE3C,KAAK;QAAE4C,KAAK,EAAE,iBAAiB;QAAEC,OAAO,EAAE,cAAc;QAAEC,WAAW,EAAE;MAAoB,CAAC;MACxH,CAAC,EAAE;QAAEJ,IAAI,EAAE,cAAc;QAAEC,IAAI,EAAExC,QAAQ;QAAEyC,KAAK,EAAE,eAAe;QAAEC,OAAO,EAAE,YAAY;QAAEC,WAAW,EAAE;MAAkB,CAAC;MAC1H,CAAC,EAAE;QAAEJ,IAAI,EAAE,UAAU;QAAEC,IAAI,EAAE9C,WAAW;QAAE+C,KAAK,EAAE,gBAAgB;QAAEC,OAAO,EAAE,aAAa;QAAEC,WAAW,EAAE;MAAmB,CAAC;MAC5H,CAAC,EAAE;QAAEJ,IAAI,EAAE,UAAU;QAAEC,IAAI,EAAE7C,OAAO;QAAE8C,KAAK,EAAE,cAAc;QAAEC,OAAO,EAAE,WAAW;QAAEC,WAAW,EAAE;MAAiB,CAAC;MAClH,CAAC,EAAE;QAAEJ,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAEtC,QAAQ;QAAEuC,KAAK,EAAE,gBAAgB;QAAEC,OAAO,EAAE,aAAa;QAAEC,WAAW,EAAE;MAAmB,CAAC;MACvH,CAAC,EAAE;QAAEJ,IAAI,EAAE,UAAU;QAAEC,IAAI,EAAE3C,KAAK;QAAE4C,KAAK,EAAE,eAAe;QAAEC,OAAO,EAAE,YAAY;QAAEC,WAAW,EAAE;MAAkB,CAAC;MACnH,CAAC,EAAE;QAAEJ,IAAI,EAAE,WAAW;QAAEC,IAAI,EAAE5C,aAAa;QAAE6C,KAAK,EAAE,cAAc;QAAEC,OAAO,EAAE,WAAW;QAAEC,WAAW,EAAE;MAAiB;IAC1H,CAAC;IACD,OAAOL,SAAS,CAACD,MAAM,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC;EAC1C,CAAC;EAED,oBACElC,OAAA;IAAKwC,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFzC,OAAA;MAAKwC,SAAS,EAAC,mFAAmF;MAAAC,QAAA,eAChGzC,OAAA;QAAKwC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBzC,OAAA;UAAKwC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDzC,OAAA;YAAIwC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1E7C,OAAA;YACE8C,OAAO,EAAEzC,OAAQ;YACjBmC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAE7CzC,OAAA,CAACT,OAAO;cAACiD,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN7C,OAAA;UAAKwC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7CzC,OAAA;YAAIwC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1E7C,OAAA;YAAKwC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBACzCxD,KAAK,CAAC8D,aAAa,CAACf,aAAa,CAAC7B,aAAa,CAAC,CAACiC,IAAI,EAAE;cACtDI,SAAS,EAAE,WAAWR,aAAa,CAAC7B,aAAa,CAAC,CAACkC,KAAK;YAC1D,CAAC,CAAC,eACFrC,OAAA;cAAMwC,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAET,aAAa,CAAC7B,aAAa,CAAC,CAACgC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7C,OAAA;UAAKwC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBzC,OAAA;YAAOwC,SAAS,EAAC,8CAA8C;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR7C,OAAA;YAAKwC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EACpClC,aAAa,CAACyC,GAAG,CAAEf,MAAM,IAAK;cAC7B,MAAMgB,UAAU,GAAGjB,aAAa,CAACC,MAAM,CAAC;cACxC,MAAMiB,UAAU,GAAGzC,cAAc,KAAKwB,MAAM,CAACkB,QAAQ,CAAC,CAAC;cAEvD,oBACEnD,OAAA;gBAEEwC,SAAS,EAAE,oFACTU,UAAU,GACN,GAAGD,UAAU,CAACV,WAAW,IAAIU,UAAU,CAACX,OAAO,EAAE,GACjD,iBAAiB,EACpB;gBAAAG,QAAA,gBAEHzC,OAAA;kBACEoD,IAAI,EAAC,OAAO;kBACZjB,IAAI,EAAC,QAAQ;kBACbkB,KAAK,EAAEpB,MAAO;kBACdqB,OAAO,EAAEJ,UAAW;kBACpBK,QAAQ,EAAGC,CAAC,IAAK9C,iBAAiB,CAAC8C,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;kBACnDb,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACF7C,OAAA;kBAAKwC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBACzCxD,KAAK,CAAC8D,aAAa,CAACE,UAAU,CAACb,IAAI,EAAE;oBACpCI,SAAS,EAAE,WAAWS,UAAU,CAACZ,KAAK;kBACxC,CAAC,CAAC,eACFrC,OAAA;oBAAMwC,SAAS,EAAE,eAAeU,UAAU,GAAGD,UAAU,CAACZ,KAAK,GAAG,eAAe,EAAG;oBAAAI,QAAA,EAC/EQ,UAAU,CAACd;kBAAI;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EACLK,UAAU,iBACTlD,OAAA,CAACV,WAAW;kBAACkD,SAAS,EAAE,mBAAmBS,UAAU,CAACZ,KAAK;gBAAG;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACjE;cAAA,GAzBIZ,MAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BN,CAAC;YAEZ,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL5B,UAAU,iBACTjB,OAAA;UAAKwC,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eACpEzC,OAAA;YAAKwC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CzC,OAAA,CAACP,KAAK;cAAC+C,SAAS,EAAC;YAAoC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxD7C,OAAA;cAAMwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAhC,UAAU,IAAI,CAACI,UAAU,iBACxBjB,OAAA;UAAKwC,SAAS,EAAE,8BACd3B,UAAU,CAACgB,OAAO,GACd,8BAA8B,GAC9B,0BAA0B,EAC7B;UAAAY,QAAA,eACDzC,OAAA;YAAKwC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,GACxC5B,UAAU,CAACgB,OAAO,gBACjB7B,OAAA,CAACV,WAAW;cAACkD,SAAS,EAAC;YAA+B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEzD7C,OAAA,CAACT,OAAO;cAACiD,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACnD,eACD7C,OAAA;cAAKwC,SAAS,EAAC,QAAQ;cAAAC,QAAA,gBACrBzC,OAAA;gBAAIwC,SAAS,EAAE,eACb3B,UAAU,CAACgB,OAAO,GAAG,gBAAgB,GAAG,cAAc,EACrD;gBAAAY,QAAA,EACA5B,UAAU,CAACgB,OAAO,GAAG,kBAAkB,GAAG;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,EAEJhC,UAAU,CAAC6C,YAAY,iBACtB1D,OAAA;gBAAGwC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAE5B,UAAU,CAAC6C;cAAY;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACtE,EAEAhC,UAAU,CAAC8C,eAAe,CAACC,MAAM,GAAG,CAAC,iBACpC5D,OAAA;gBAAKwC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBzC,OAAA;kBAAGwC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACrE7C,OAAA;kBAAIwC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAChD5B,UAAU,CAAC8C,eAAe,CAACX,GAAG,CAAC,CAACa,MAAM,EAAEC,KAAK,kBAC5C9D,OAAA;oBAAgBwC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACpDzC,OAAA;sBAAMwC,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5C7C,OAAA;sBAAAyC,QAAA,EAAOoB;oBAAM;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,GAFdiB,KAAK;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGV,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACN,EAEAhC,UAAU,CAACkD,gBAAgB,CAACH,MAAM,GAAG,CAAC,iBACrC5D,OAAA;gBAAKwC,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBzC,OAAA;kBAAGwC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACtE7C,OAAA;kBAAIwC,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAChD5B,UAAU,CAACkD,gBAAgB,CAACf,GAAG,CAAC,CAACgB,GAAG,EAAEF,KAAK,kBAC1C9D,OAAA;oBAAgBwC,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACpDzC,OAAA;sBAAMwC,SAAS,EAAC,mBAAmB;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5C7C,OAAA;sBAAAyC,QAAA,EAAOuB;oBAAG;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,GAFXiB,KAAK;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGV,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACN,EAEAhC,UAAU,CAACoD,oBAAoB,iBAC9BjE,OAAA;gBAAKwC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CzC,OAAA,CAACN,aAAa;kBAAC8C,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrD7C,OAAA;kBAAMwC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAA0C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxF,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD7C,OAAA;UAAKwC,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBzC,OAAA;YAAOkE,OAAO,EAAC,UAAU;YAAC1B,SAAS,EAAC,8CAA8C;YAAAC,QAAA,GAAC,WACxE,EAAChC,cAAc,KAAK,GAAG,iBAAIT,OAAA;cAAMwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACR7C,OAAA;YACEmE,EAAE,EAAC,UAAU;YACbC,IAAI,EAAE,CAAE;YACRf,KAAK,EAAE1C,QAAS;YAChB4C,QAAQ,EAAGC,CAAC,IAAK5C,WAAW,CAAC4C,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;YAC7CgB,WAAW,EAAC,0CAA0C;YACtD7B,SAAS,EAAC;UAA2H;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtI,CAAC,EACDpC,cAAc,KAAK,GAAG,iBACrBT,OAAA;YAAGwC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN7C,OAAA;UAAKwC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBACtDzC,OAAA;YACE8C,OAAO,EAAEzC,OAAQ;YACjBmC,SAAS,EAAC,gGAAgG;YAAAC,QAAA,EAC3G;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7C,OAAA;YACE8C,OAAO,EAAElB,kBAAmB;YAC5B0C,QAAQ,EAAEvD,OAAO,IAAI,CAACN,cAAc,IAAKI,UAAU,IAAI,CAACA,UAAU,CAACgB,OAAQ,IAAKpB,cAAc,KAAK,GAAG,IAAI,CAACE,QAAQ,CAAC4D,IAAI,CAAC,CAAG;YAC5H/B,SAAS,EAAC,mIAAmI;YAAAC,QAAA,EAE5I1B,OAAO,GAAG,aAAa,GAAG;UAAe;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CAtQIL,oBAAoB;AAAAuE,EAAA,GAApBvE,oBAAoB;AAwQ1B,eAAeA,oBAAoB;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}