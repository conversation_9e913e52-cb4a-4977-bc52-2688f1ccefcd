import React, { useState, useEffect } from 'react';
import { workflowAPI } from '../services/api';
import { toast } from 'react-toastify';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Clock, 
  MessageSquare,
  User,
  FileText,
  Shield,
  Activity
} from 'lucide-react';

const WorkflowStatusUpdate = ({ agentId, currentStatus, onStatusUpdated, onClose }) => {
  const [validStatuses, setValidStatuses] = useState([]);
  const [selectedStatus, setSelectedStatus] = useState('');
  const [comments, setComments] = useState('');
  const [validation, setValidation] = useState(null);
  const [loading, setLoading] = useState(false);
  const [validating, setValidating] = useState(false);

  useEffect(() => {
    fetchValidStatuses();
  }, [agentId]);

  useEffect(() => {
    if (selectedStatus) {
      validateTransition();
    }
  }, [selectedStatus]);

  const fetchValidStatuses = async () => {
    try {
      const statuses = await workflowAPI.getValidNextStatuses(agentId);
      setValidStatuses(statuses);
    } catch (error) {
      console.error('Error fetching valid statuses:', error);
      toast.error('Failed to load valid status options');
    }
  };

  const validateTransition = async () => {
    if (!selectedStatus) return;

    try {
      setValidating(true);
      const result = await workflowAPI.validateStatusTransition(agentId, parseInt(selectedStatus));
      setValidation(result);
    } catch (error) {
      console.error('Error validating transition:', error);
      toast.error('Failed to validate status transition');
    } finally {
      setValidating(false);
    }
  };

  const handleStatusUpdate = async () => {
    if (!selectedStatus) {
      toast.error('Please select a status');
      return;
    }

    if (validation && !validation.isValid) {
      toast.error('Cannot proceed with invalid status transition');
      return;
    }

    try {
      setLoading(true);
      await workflowAPI.processStatusTransition(agentId, parseInt(selectedStatus), comments);
      toast.success('Status updated successfully');
      onStatusUpdated();
      onClose();
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Failed to update status');
    } finally {
      setLoading(false);
    }
  };

  const getStatusInfo = (status) => {
    const statusMap = {
      0: { name: 'Pending', icon: Clock, color: 'text-yellow-600', bgColor: 'bg-yellow-50', borderColor: 'border-yellow-200' },
      1: { name: 'Under Review', icon: FileText, color: 'text-blue-600', bgColor: 'bg-blue-50', borderColor: 'border-blue-200' },
      2: { name: 'Approved', icon: CheckCircle, color: 'text-green-600', bgColor: 'bg-green-50', borderColor: 'border-green-200' },
      3: { name: 'Rejected', icon: XCircle, color: 'text-red-600', bgColor: 'bg-red-50', borderColor: 'border-red-200' },
      4: { name: 'Active', icon: Activity, color: 'text-green-600', bgColor: 'bg-green-50', borderColor: 'border-green-200' },
      5: { name: 'Inactive', icon: Clock, color: 'text-gray-600', bgColor: 'bg-gray-50', borderColor: 'border-gray-200' },
      6: { name: 'Suspended', icon: AlertTriangle, color: 'text-red-600', bgColor: 'bg-red-50', borderColor: 'border-red-200' }
    };
    return statusMap[status] || statusMap[0];
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">Update Agent Status</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>

          {/* Current Status */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Current Status</h4>
            <div className="flex items-center space-x-3">
              {React.createElement(getStatusInfo(currentStatus).icon, {
                className: `w-5 h-5 ${getStatusInfo(currentStatus).color}`
              })}
              <span className="font-medium">{getStatusInfo(currentStatus).name}</span>
            </div>
          </div>

          {/* Status Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Select New Status
            </label>
            <div className="grid grid-cols-1 gap-3">
              {validStatuses.map((status) => {
                const statusInfo = getStatusInfo(status);
                const isSelected = selectedStatus === status.toString();
                
                return (
                  <label
                    key={status}
                    className={`relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                      isSelected 
                        ? `${statusInfo.borderColor} ${statusInfo.bgColor}` 
                        : 'border-gray-200'
                    }`}
                  >
                    <input
                      type="radio"
                      name="status"
                      value={status}
                      checked={isSelected}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="sr-only"
                    />
                    <div className="flex items-center space-x-3">
                      {React.createElement(statusInfo.icon, {
                        className: `w-5 h-5 ${statusInfo.color}`
                      })}
                      <span className={`font-medium ${isSelected ? statusInfo.color : 'text-gray-900'}`}>
                        {statusInfo.name}
                      </span>
                    </div>
                    {isSelected && (
                      <CheckCircle className={`w-5 h-5 ml-auto ${statusInfo.color}`} />
                    )}
                  </label>
                );
              })}
            </div>
          </div>

          {/* Validation Results */}
          {validating && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <Clock className="w-5 h-5 text-blue-500 animate-spin" />
                <span className="text-blue-700">Validating status transition...</span>
              </div>
            </div>
          )}

          {validation && !validating && (
            <div className={`mb-6 p-4 border rounded-lg ${
              validation.isValid 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-start space-x-3">
                {validation.isValid ? (
                  <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500 mt-0.5" />
                )}
                <div className="flex-1">
                  <h4 className={`font-medium ${
                    validation.isValid ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {validation.isValid ? 'Transition Valid' : 'Transition Invalid'}
                  </h4>
                  
                  {validation.errorMessage && (
                    <p className="text-sm text-red-700 mt-1">{validation.errorMessage}</p>
                  )}
                  
                  {validation.requiredActions.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-red-800">Required Actions:</p>
                      <ul className="text-sm text-red-700 mt-1 space-y-1">
                        {validation.requiredActions.map((action, index) => (
                          <li key={index} className="flex items-start space-x-2">
                            <span className="text-red-500 mt-1">•</span>
                            <span>{action}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {validation.missingDocuments.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-red-800">Missing Documents:</p>
                      <ul className="text-sm text-red-700 mt-1 space-y-1">
                        {validation.missingDocuments.map((doc, index) => (
                          <li key={index} className="flex items-start space-x-2">
                            <span className="text-red-500 mt-1">•</span>
                            <span>{doc}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {validation.requiresManualReview && (
                    <div className="mt-2 flex items-center space-x-2">
                      <MessageSquare className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm text-yellow-700">Manual review required for this transition</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Comments */}
          <div className="mb-6">
            <label htmlFor="comments" className="block text-sm font-medium text-gray-700 mb-2">
              Comments {selectedStatus === '3' && <span className="text-red-500">*</span>}
            </label>
            <textarea
              id="comments"
              rows={4}
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              placeholder="Add comments about this status change..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
            {selectedStatus === '3' && (
              <p className="text-sm text-gray-500 mt-1">
                Comments are required when rejecting an application
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-4">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleStatusUpdate}
              disabled={loading || !selectedStatus || (validation && !validation.isValid) || (selectedStatus === '3' && !comments.trim())}
              className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Updating...' : 'Update Status'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkflowStatusUpdate;
