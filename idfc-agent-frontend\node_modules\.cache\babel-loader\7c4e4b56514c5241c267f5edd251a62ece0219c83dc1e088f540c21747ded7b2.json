{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\IDFCAgentOnboardingAndManagementSolution\\\\idfc-agent-frontend\\\\src\\\\pages\\\\AgentList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { agentAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { toast } from 'react-toastify';\nimport { Search, Filter, Eye, CheckCircle, XCircle, Clock, AlertCircle, ChevronLeft, ChevronRight, Download, RefreshCw } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AgentList = () => {\n  _s();\n  const {\n    isAdmin,\n    isReviewer\n  } = useAuth();\n  const [agents, setAgents] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [pageSize] = useState(10);\n  const [sortBy, setSortBy] = useState('createdAt');\n  const [sortOrder, setSortOrder] = useState('desc');\n  useEffect(() => {\n    fetchAgents();\n  }, [currentPage, statusFilter, sortBy, sortOrder]);\n  const fetchAgents = async () => {\n    setLoading(true);\n    try {\n      const response = await agentAPI.getAll(currentPage, pageSize, statusFilter);\n\n      // Filter by search term on frontend for now\n      let filteredAgents = response.data || [];\n      if (searchTerm) {\n        filteredAgents = filteredAgents.filter(agent => {\n          var _agent$firstName, _agent$lastName, _agent$email, _agent$phoneNumber, _agent$agentId;\n          return ((_agent$firstName = agent.firstName) === null || _agent$firstName === void 0 ? void 0 : _agent$firstName.toLowerCase().includes(searchTerm.toLowerCase())) || ((_agent$lastName = agent.lastName) === null || _agent$lastName === void 0 ? void 0 : _agent$lastName.toLowerCase().includes(searchTerm.toLowerCase())) || ((_agent$email = agent.email) === null || _agent$email === void 0 ? void 0 : _agent$email.toLowerCase().includes(searchTerm.toLowerCase())) || ((_agent$phoneNumber = agent.phoneNumber) === null || _agent$phoneNumber === void 0 ? void 0 : _agent$phoneNumber.includes(searchTerm)) || ((_agent$agentId = agent.agentId) === null || _agent$agentId === void 0 ? void 0 : _agent$agentId.toString().includes(searchTerm));\n        });\n      }\n      setAgents(filteredAgents);\n      setTotalPages(Math.ceil((response.total || filteredAgents.length) / pageSize));\n    } catch (error) {\n      console.error('Error fetching agents:', error);\n      toast.error('Failed to fetch agents');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearch = () => {\n    setCurrentPage(1);\n    fetchAgents();\n  };\n  const handleStatusChange = async (agentId, newStatus, comments = '') => {\n    try {\n      await agentAPI.updateStatus(agentId, {\n        status: newStatus,\n        comments\n      });\n      toast.success(`Agent status updated to ${newStatus}`);\n      fetchAgents(); // Refresh the list\n    } catch (error) {\n      console.error('Error updating status:', error);\n      toast.error('Failed to update agent status');\n    }\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'Approved':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"h-5 w-5 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 16\n        }, this);\n      case 'Pending':\n        return /*#__PURE__*/_jsxDEV(Clock, {\n          className: \"h-5 w-5 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 16\n        }, this);\n      case 'Rejected':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"h-5 w-5 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 16\n        }, this);\n      case 'UnderReview':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"h-5 w-5 text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"h-5 w-5 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Approved':\n        return 'bg-green-100 text-green-800';\n      case 'Pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'Rejected':\n        return 'bg-red-100 text-red-800';\n      case 'UnderReview':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const handleSort = field => {\n    if (sortBy === field) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortBy(field);\n      setSortOrder('asc');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Agent Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-600\",\n          children: \"View and manage all agent applications and their status.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchAgents,\n          className: \"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), \"Refresh\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\",\n          children: [/*#__PURE__*/_jsxDEV(Download, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), \"Export\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 gap-4 sm:grid-cols-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sm:col-span-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Search Agents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              onKeyPress: e => e.key === 'Enter' && handleSearch(),\n              className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n              placeholder: \"Search by name, email, phone, or ID...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Search, {\n              className: \"absolute left-3 top-2.5 h-5 w-5 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Status Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: statusFilter,\n            onChange: e => setStatusFilter(e.target.value),\n            className: \"block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Pending\",\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"UnderReview\",\n              children: \"Under Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Approved\",\n              children: \"Approved\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Rejected\",\n              children: \"Rejected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Active\",\n              children: \"Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Inactive\",\n              children: \"Inactive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Suspended\",\n              children: \"Suspended\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-end\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: [/*#__PURE__*/_jsxDEV(Filter, {\n              className: \"h-4 w-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), \"Apply Filters\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900\",\n          children: [\"Agents (\", agents.length, \" of \", totalPages * pageSize, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                onClick: () => handleSort('agentId'),\n                children: \"Agent ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                onClick: () => handleSort('firstName'),\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                onClick: () => handleSort('email'),\n                children: \"Contact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                onClick: () => handleSort('status'),\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\",\n                onClick: () => handleSort('createdAt'),\n                children: \"Applied Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: agents.map(agent => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                children: [\"#\", agent.agentId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: [agent.firstName, \" \", agent.lastName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"PAN: \", agent.panNumber]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-900\",\n                    children: agent.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: agent.phoneNumber\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [getStatusIcon(agent.status), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`,\n                    children: agent.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: new Date(agent.createdAt).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: `/agents/${agent.agentId}`,\n                    className: \"text-blue-600 hover:text-blue-900 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"h-4 w-4 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 25\n                    }, this), \"View\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 23\n                  }, this), (isAdmin() || isReviewer()) && agent.status === 'Pending' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleStatusChange(agent.agentId, 'Approved'),\n                      className: \"text-green-600 hover:text-green-900 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 298,\n                        columnNumber: 29\n                      }, this), \"Approve\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleStatusChange(agent.agentId, 'Rejected'),\n                      className: \"text-red-600 hover:text-red-900 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(XCircle, {\n                        className: \"h-4 w-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 305,\n                        columnNumber: 29\n                      }, this), \"Reject\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this)]\n            }, agent.agentId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex justify-between sm:hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setCurrentPage(Math.max(currentPage - 1, 1)),\n            disabled: currentPage === 1,\n            className: \"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setCurrentPage(Math.min(currentPage + 1, totalPages)),\n            disabled: currentPage === totalPages,\n            className: \"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-700\",\n              children: [\"Showing\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: (currentPage - 1) * pageSize + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this), ' ', \"to\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: Math.min(currentPage * pageSize, totalPages * pageSize)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this), ' ', \"of\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: totalPages * pageSize\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this), ' ', \"results\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"nav\", {\n              className: \"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\",\n              \"aria-label\": \"Pagination\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCurrentPage(Math.max(currentPage - 1, 1)),\n                disabled: currentPage === 1,\n                className: \"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this), [...Array(totalPages)].map((_, index) => {\n                const page = index + 1;\n                if (page === currentPage || page === 1 || page === totalPages || page >= currentPage - 1 && page <= currentPage + 1) {\n                  return /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setCurrentPage(page),\n                    className: `relative inline-flex items-center px-4 py-2 border text-sm font-medium ${page === currentPage ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}`,\n                    children: page\n                  }, page, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 25\n                  }, this);\n                } else if (page === currentPage - 2 || page === currentPage + 2) {\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700\",\n                    children: \"...\"\n                  }, page, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 25\n                  }, this);\n                }\n                return null;\n              }), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setCurrentPage(Math.min(currentPage + 1, totalPages)),\n                disabled: currentPage === totalPages,\n                className: \"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: /*#__PURE__*/_jsxDEV(ChevronRight, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), agents.length === 0 && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg p-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"mx-auto h-12 w-12 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"mt-2 text-sm font-medium text-gray-900\",\n          children: \"No agents found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm text-gray-500\",\n          children: searchTerm || statusFilter ? 'Try adjusting your search criteria or filters.' : 'No agent applications have been submitted yet.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 127,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentList, \"IIk5/NaMB0JtpZSKMDlZmmg0+Sw=\", false, function () {\n  return [useAuth];\n});\n_c = AgentList;\nexport default AgentList;\nvar _c;\n$RefreshReg$(_c, \"AgentList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "agentAPI", "useAuth", "toast", "Search", "Filter", "Eye", "CheckCircle", "XCircle", "Clock", "AlertCircle", "ChevronLeft", "ChevronRight", "Download", "RefreshCw", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AgentList", "_s", "isAdmin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "agents", "setAgents", "loading", "setLoading", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "pageSize", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "fetchAgents", "response", "getAll", "filteredAgents", "data", "filter", "agent", "_agent$firstName", "_agent$lastName", "_agent$email", "_agent$phoneNumber", "_agent$agentId", "firstName", "toLowerCase", "includes", "lastName", "email", "phoneNumber", "agentId", "toString", "Math", "ceil", "total", "length", "error", "console", "handleSearch", "handleStatusChange", "newStatus", "comments", "updateStatus", "status", "success", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "handleSort", "field", "children", "onClick", "type", "value", "onChange", "e", "target", "onKeyPress", "key", "placeholder", "map", "panNumber", "Date", "createdAt", "toLocaleDateString", "to", "max", "disabled", "min", "Array", "_", "index", "page", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/IDFCAgentOnboardingAndManagementSolution/idfc-agent-frontend/src/pages/AgentList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { agentAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { toast } from 'react-toastify';\nimport {\n  Search,\n  Filter,\n  Eye,\n  CheckCircle,\n  XCircle,\n  Clock,\n  AlertCircle,\n  ChevronLeft,\n  ChevronRight,\n  Download,\n  RefreshCw\n} from 'lucide-react';\n\nconst AgentList = () => {\n  const { isAdmin, isReviewer } = useAuth();\n  const [agents, setAgents] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [pageSize] = useState(10);\n  const [sortBy, setSortBy] = useState('createdAt');\n  const [sortOrder, setSortOrder] = useState('desc');\n\n  useEffect(() => {\n    fetchAgents();\n  }, [currentPage, statusFilter, sortBy, sortOrder]);\n\n  const fetchAgents = async () => {\n    setLoading(true);\n    try {\n      const response = await agentAPI.getAll(currentPage, pageSize, statusFilter);\n\n      // Filter by search term on frontend for now\n      let filteredAgents = response.data || [];\n      if (searchTerm) {\n        filteredAgents = filteredAgents.filter(agent =>\n          agent.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          agent.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          agent.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          agent.phoneNumber?.includes(searchTerm) ||\n          agent.agentId?.toString().includes(searchTerm)\n        );\n      }\n\n      setAgents(filteredAgents);\n      setTotalPages(Math.ceil((response.total || filteredAgents.length) / pageSize));\n    } catch (error) {\n      console.error('Error fetching agents:', error);\n      toast.error('Failed to fetch agents');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = () => {\n    setCurrentPage(1);\n    fetchAgents();\n  };\n\n  const handleStatusChange = async (agentId, newStatus, comments = '') => {\n    try {\n      await agentAPI.updateStatus(agentId, { status: newStatus, comments });\n      toast.success(`Agent status updated to ${newStatus}`);\n      fetchAgents(); // Refresh the list\n    } catch (error) {\n      console.error('Error updating status:', error);\n      toast.error('Failed to update agent status');\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'Approved':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />;\n      case 'Pending':\n        return <Clock className=\"h-5 w-5 text-yellow-500\" />;\n      case 'Rejected':\n        return <XCircle className=\"h-5 w-5 text-red-500\" />;\n      case 'UnderReview':\n        return <AlertCircle className=\"h-5 w-5 text-blue-500\" />;\n      default:\n        return <AlertCircle className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Approved':\n        return 'bg-green-100 text-green-800';\n      case 'Pending':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'Rejected':\n        return 'bg-red-100 text-red-800';\n      case 'UnderReview':\n        return 'bg-blue-100 text-blue-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const handleSort = (field) => {\n    if (sortBy === field) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortBy(field);\n      setSortOrder('asc');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Agent Management</h1>\n          <p className=\"mt-1 text-sm text-gray-600\">\n            View and manage all agent applications and their status.\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={fetchAgents}\n            className=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\"\n          >\n            <RefreshCw className=\"h-4 w-4 mr-2\" />\n            Refresh\n          </button>\n          <button className=\"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50\">\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export\n          </button>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\n          <div className=\"sm:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Search Agents\n            </label>\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Search by name, email, phone, or ID...\"\n              />\n              <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Status Filter\n            </label>\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"\">All Status</option>\n              <option value=\"Pending\">Pending</option>\n              <option value=\"UnderReview\">Under Review</option>\n              <option value=\"Approved\">Approved</option>\n              <option value=\"Rejected\">Rejected</option>\n              <option value=\"Active\">Active</option>\n              <option value=\"Inactive\">Inactive</option>\n              <option value=\"Suspended\">Suspended</option>\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button\n              onClick={handleSearch}\n              className=\"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <Filter className=\"h-4 w-4 mr-2\" />\n              Apply Filters\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Agents Table */}\n      <div className=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h3 className=\"text-lg font-medium text-gray-900\">\n            Agents ({agents.length} of {totalPages * pageSize})\n          </h3>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('agentId')}\n                >\n                  Agent ID\n                </th>\n                <th\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('firstName')}\n                >\n                  Name\n                </th>\n                <th\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('email')}\n                >\n                  Contact\n                </th>\n                <th\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('status')}\n                >\n                  Status\n                </th>\n                <th\n                  className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                  onClick={() => handleSort('createdAt')}\n                >\n                  Applied Date\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Actions\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {agents.map((agent) => (\n                <tr key={agent.agentId} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                    #{agent.agentId}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {agent.firstName} {agent.lastName}\n                      </div>\n                      <div className=\"text-sm text-gray-500\">\n                        PAN: {agent.panNumber}\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm text-gray-900\">{agent.email}</div>\n                      <div className=\"text-sm text-gray-500\">{agent.phoneNumber}</div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      {getStatusIcon(agent.status)}\n                      <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(agent.status)}`}>\n                        {agent.status}\n                      </span>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {new Date(agent.createdAt).toLocaleDateString()}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <Link\n                        to={`/agents/${agent.agentId}`}\n                        className=\"text-blue-600 hover:text-blue-900 flex items-center\"\n                      >\n                        <Eye className=\"h-4 w-4 mr-1\" />\n                        View\n                      </Link>\n\n                      {(isAdmin() || isReviewer()) && agent.status === 'Pending' && (\n                        <>\n                          <button\n                            onClick={() => handleStatusChange(agent.agentId, 'Approved')}\n                            className=\"text-green-600 hover:text-green-900 flex items-center\"\n                          >\n                            <CheckCircle className=\"h-4 w-4 mr-1\" />\n                            Approve\n                          </button>\n                          <button\n                            onClick={() => handleStatusChange(agent.agentId, 'Rejected')}\n                            className=\"text-red-600 hover:text-red-900 flex items-center\"\n                          >\n                            <XCircle className=\"h-4 w-4 mr-1\" />\n                            Reject\n                          </button>\n                        </>\n                      )}\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {/* Pagination */}\n        {totalPages > 1 && (\n          <div className=\"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\">\n            <div className=\"flex-1 flex justify-between sm:hidden\">\n              <button\n                onClick={() => setCurrentPage(Math.max(currentPage - 1, 1))}\n                disabled={currentPage === 1}\n                className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Previous\n              </button>\n              <button\n                onClick={() => setCurrentPage(Math.min(currentPage + 1, totalPages))}\n                disabled={currentPage === totalPages}\n                className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Next\n              </button>\n            </div>\n            <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-700\">\n                  Showing{' '}\n                  <span className=\"font-medium\">{(currentPage - 1) * pageSize + 1}</span>\n                  {' '}to{' '}\n                  <span className=\"font-medium\">\n                    {Math.min(currentPage * pageSize, totalPages * pageSize)}\n                  </span>\n                  {' '}of{' '}\n                  <span className=\"font-medium\">{totalPages * pageSize}</span>\n                  {' '}results\n                </p>\n              </div>\n              <div>\n                <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">\n                  <button\n                    onClick={() => setCurrentPage(Math.max(currentPage - 1, 1))}\n                    disabled={currentPage === 1}\n                    className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    <ChevronLeft className=\"h-5 w-5\" />\n                  </button>\n\n                  {[...Array(totalPages)].map((_, index) => {\n                    const page = index + 1;\n                    if (page === currentPage || page === 1 || page === totalPages || (page >= currentPage - 1 && page <= currentPage + 1)) {\n                      return (\n                        <button\n                          key={page}\n                          onClick={() => setCurrentPage(page)}\n                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${\n                            page === currentPage\n                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'\n                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'\n                          }`}\n                        >\n                          {page}\n                        </button>\n                      );\n                    } else if (page === currentPage - 2 || page === currentPage + 2) {\n                      return (\n                        <span key={page} className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700\">\n                          ...\n                        </span>\n                      );\n                    }\n                    return null;\n                  })}\n\n                  <button\n                    onClick={() => setCurrentPage(Math.min(currentPage + 1, totalPages))}\n                    disabled={currentPage === totalPages}\n                    className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    <ChevronRight className=\"h-5 w-5\" />\n                  </button>\n                </nav>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Empty State */}\n      {agents.length === 0 && !loading && (\n        <div className=\"bg-white shadow rounded-lg p-6\">\n          <div className=\"text-center py-12\">\n            <AlertCircle className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No agents found</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchTerm || statusFilter\n                ? 'Try adjusting your search criteria or filters.'\n                : 'No agent applications have been submitted yet.'\n              }\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AgentList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SACEC,MAAM,EACNC,MAAM,EACNC,GAAG,EACHC,WAAW,EACXC,OAAO,EACPC,KAAK,EACLC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,QAAQ,EACRC,SAAS,QACJ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAGpB,OAAO,CAAC,CAAC;EACzC,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACqC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC/B,MAAM,CAACsC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAC,WAAW,CAAC;EACjD,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,MAAM,CAAC;EAElDC,SAAS,CAAC,MAAM;IACdyC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACT,WAAW,EAAEF,YAAY,EAAEO,MAAM,EAAEE,SAAS,CAAC,CAAC;EAElD,MAAME,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9Bd,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAMxC,QAAQ,CAACyC,MAAM,CAACX,WAAW,EAAEI,QAAQ,EAAEN,YAAY,CAAC;;MAE3E;MACA,IAAIc,cAAc,GAAGF,QAAQ,CAACG,IAAI,IAAI,EAAE;MACxC,IAAIjB,UAAU,EAAE;QACdgB,cAAc,GAAGA,cAAc,CAACE,MAAM,CAACC,KAAK;UAAA,IAAAC,gBAAA,EAAAC,eAAA,EAAAC,YAAA,EAAAC,kBAAA,EAAAC,cAAA;UAAA,OAC1C,EAAAJ,gBAAA,GAAAD,KAAK,CAACM,SAAS,cAAAL,gBAAA,uBAAfA,gBAAA,CAAiBM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,CAAC,CAAC,CAAC,OAAAL,eAAA,GACjEF,KAAK,CAACS,QAAQ,cAAAP,eAAA,uBAAdA,eAAA,CAAgBK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,CAAC,CAAC,CAAC,OAAAJ,YAAA,GAChEH,KAAK,CAACU,KAAK,cAAAP,YAAA,uBAAXA,YAAA,CAAaI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,CAAC,CAAC,CAAC,OAAAH,kBAAA,GAC7DJ,KAAK,CAACW,WAAW,cAAAP,kBAAA,uBAAjBA,kBAAA,CAAmBI,QAAQ,CAAC3B,UAAU,CAAC,OAAAwB,cAAA,GACvCL,KAAK,CAACY,OAAO,cAAAP,cAAA,uBAAbA,cAAA,CAAeQ,QAAQ,CAAC,CAAC,CAACL,QAAQ,CAAC3B,UAAU,CAAC;QAAA,CAChD,CAAC;MACH;MAEAH,SAAS,CAACmB,cAAc,CAAC;MACzBT,aAAa,CAAC0B,IAAI,CAACC,IAAI,CAAC,CAACpB,QAAQ,CAACqB,KAAK,IAAInB,cAAc,CAACoB,MAAM,IAAI5B,QAAQ,CAAC,CAAC;IAChF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C7D,KAAK,CAAC6D,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwC,YAAY,GAAGA,CAAA,KAAM;IACzBlC,cAAc,CAAC,CAAC,CAAC;IACjBQ,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAM2B,kBAAkB,GAAG,MAAAA,CAAOT,OAAO,EAAEU,SAAS,EAAEC,QAAQ,GAAG,EAAE,KAAK;IACtE,IAAI;MACF,MAAMpE,QAAQ,CAACqE,YAAY,CAACZ,OAAO,EAAE;QAAEa,MAAM,EAAEH,SAAS;QAAEC;MAAS,CAAC,CAAC;MACrElE,KAAK,CAACqE,OAAO,CAAC,2BAA2BJ,SAAS,EAAE,CAAC;MACrD5B,WAAW,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C7D,KAAK,CAAC6D,KAAK,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAMS,aAAa,GAAIF,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,oBAAOvD,OAAA,CAACT,WAAW;UAACmE,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,SAAS;QACZ,oBAAO9D,OAAA,CAACP,KAAK;UAACiE,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,UAAU;QACb,oBAAO9D,OAAA,CAACR,OAAO;UAACkE,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD,KAAK,aAAa;QAChB,oBAAO9D,OAAA,CAACN,WAAW;UAACgE,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1D;QACE,oBAAO9D,OAAA,CAACN,WAAW;UAACgE,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC5D;EACF,CAAC;EAED,MAAMC,cAAc,GAAIR,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,6BAA6B;MACtC,KAAK,SAAS;QACZ,OAAO,+BAA+B;MACxC,KAAK,UAAU;QACb,OAAO,yBAAyB;MAClC,KAAK,aAAa;QAChB,OAAO,2BAA2B;MACpC;QACE,OAAO,2BAA2B;IACtC;EACF,CAAC;EAED,MAAMS,UAAU,GAAIC,KAAK,IAAK;IAC5B,IAAI7C,MAAM,KAAK6C,KAAK,EAAE;MACpB1C,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IACpD,CAAC,MAAM;MACLD,SAAS,CAAC4C,KAAK,CAAC;MAChB1C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,IAAId,OAAO,EAAE;IACX,oBACET,OAAA;MAAK0D,SAAS,EAAC,uCAAuC;MAAAQ,QAAA,eACpDlE,OAAA;QAAK0D,SAAS,EAAC;MAAgE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACE9D,OAAA;IAAK0D,SAAS,EAAC,WAAW;IAAAQ,QAAA,gBACxBlE,OAAA;MAAK0D,SAAS,EAAC,mCAAmC;MAAAQ,QAAA,gBAChDlE,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAI0D,SAAS,EAAC,kCAAkC;UAAAQ,QAAA,EAAC;QAAgB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtE9D,OAAA;UAAG0D,SAAS,EAAC,4BAA4B;UAAAQ,QAAA,EAAC;QAE1C;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN9D,OAAA;QAAK0D,SAAS,EAAC,gBAAgB;QAAAQ,QAAA,gBAC7BlE,OAAA;UACEmE,OAAO,EAAE3C,WAAY;UACrBkC,SAAS,EAAC,4IAA4I;UAAAQ,QAAA,gBAEtJlE,OAAA,CAACF,SAAS;YAAC4D,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9D,OAAA;UAAQ0D,SAAS,EAAC,4IAA4I;UAAAQ,QAAA,gBAC5JlE,OAAA,CAACH,QAAQ;YAAC6D,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAK0D,SAAS,EAAC,gCAAgC;MAAAQ,QAAA,eAC7ClE,OAAA;QAAK0D,SAAS,EAAC,uCAAuC;QAAAQ,QAAA,gBACpDlE,OAAA;UAAK0D,SAAS,EAAC,eAAe;UAAAQ,QAAA,gBAC5BlE,OAAA;YAAO0D,SAAS,EAAC,8CAA8C;YAAAQ,QAAA,EAAC;UAEhE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9D,OAAA;YAAK0D,SAAS,EAAC,UAAU;YAAAQ,QAAA,gBACvBlE,OAAA;cACEoE,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE1D,UAAW;cAClB2D,QAAQ,EAAGC,CAAC,IAAK3D,aAAa,CAAC2D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CI,UAAU,EAAGF,CAAC,IAAKA,CAAC,CAACG,GAAG,KAAK,OAAO,IAAIxB,YAAY,CAAC,CAAE;cACvDQ,SAAS,EAAC,6MAA6M;cACvNiB,WAAW,EAAC;YAAwC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACF9D,OAAA,CAACZ,MAAM;cAACsE,SAAS,EAAC;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9D,OAAA;UAAAkE,QAAA,gBACElE,OAAA;YAAO0D,SAAS,EAAC,8CAA8C;YAAAQ,QAAA,EAAC;UAEhE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9D,OAAA;YACEqE,KAAK,EAAExD,YAAa;YACpByD,QAAQ,EAAGC,CAAC,IAAKzD,eAAe,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YACjDX,SAAS,EAAC,iIAAiI;YAAAQ,QAAA,gBAE3IlE,OAAA;cAAQqE,KAAK,EAAC,EAAE;cAAAH,QAAA,EAAC;YAAU;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpC9D,OAAA;cAAQqE,KAAK,EAAC,SAAS;cAAAH,QAAA,EAAC;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC9D,OAAA;cAAQqE,KAAK,EAAC,aAAa;cAAAH,QAAA,EAAC;YAAY;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjD9D,OAAA;cAAQqE,KAAK,EAAC,UAAU;cAAAH,QAAA,EAAC;YAAQ;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C9D,OAAA;cAAQqE,KAAK,EAAC,UAAU;cAAAH,QAAA,EAAC;YAAQ;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C9D,OAAA;cAAQqE,KAAK,EAAC,QAAQ;cAAAH,QAAA,EAAC;YAAM;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC9D,OAAA;cAAQqE,KAAK,EAAC,UAAU;cAAAH,QAAA,EAAC;YAAQ;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C9D,OAAA;cAAQqE,KAAK,EAAC,WAAW;cAAAH,QAAA,EAAC;YAAS;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN9D,OAAA;UAAK0D,SAAS,EAAC,gBAAgB;UAAAQ,QAAA,eAC7BlE,OAAA;YACEmE,OAAO,EAAEjB,YAAa;YACtBQ,SAAS,EAAC,oOAAoO;YAAAQ,QAAA,gBAE9OlE,OAAA,CAACX,MAAM;cAACqE,SAAS,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAK0D,SAAS,EAAC,4CAA4C;MAAAQ,QAAA,gBACzDlE,OAAA;QAAK0D,SAAS,EAAC,oCAAoC;QAAAQ,QAAA,eACjDlE,OAAA;UAAI0D,SAAS,EAAC,mCAAmC;UAAAQ,QAAA,GAAC,UACxC,EAAC3D,MAAM,CAACwC,MAAM,EAAC,MAAI,EAAC9B,UAAU,GAAGE,QAAQ,EAAC,GACpD;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN9D,OAAA;QAAK0D,SAAS,EAAC,iBAAiB;QAAAQ,QAAA,eAC9BlE,OAAA;UAAO0D,SAAS,EAAC,qCAAqC;UAAAQ,QAAA,gBACpDlE,OAAA;YAAO0D,SAAS,EAAC,YAAY;YAAAQ,QAAA,eAC3BlE,OAAA;cAAAkE,QAAA,gBACElE,OAAA;gBACE0D,SAAS,EAAC,iHAAiH;gBAC3HS,OAAO,EAAEA,CAAA,KAAMH,UAAU,CAAC,SAAS,CAAE;gBAAAE,QAAA,EACtC;cAED;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9D,OAAA;gBACE0D,SAAS,EAAC,iHAAiH;gBAC3HS,OAAO,EAAEA,CAAA,KAAMH,UAAU,CAAC,WAAW,CAAE;gBAAAE,QAAA,EACxC;cAED;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9D,OAAA;gBACE0D,SAAS,EAAC,iHAAiH;gBAC3HS,OAAO,EAAEA,CAAA,KAAMH,UAAU,CAAC,OAAO,CAAE;gBAAAE,QAAA,EACpC;cAED;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9D,OAAA;gBACE0D,SAAS,EAAC,iHAAiH;gBAC3HS,OAAO,EAAEA,CAAA,KAAMH,UAAU,CAAC,QAAQ,CAAE;gBAAAE,QAAA,EACrC;cAED;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9D,OAAA;gBACE0D,SAAS,EAAC,iHAAiH;gBAC3HS,OAAO,EAAEA,CAAA,KAAMH,UAAU,CAAC,WAAW,CAAE;gBAAAE,QAAA,EACxC;cAED;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9D,OAAA;gBAAI0D,SAAS,EAAC,gFAAgF;gBAAAQ,QAAA,EAAC;cAE/F;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9D,OAAA;YAAO0D,SAAS,EAAC,mCAAmC;YAAAQ,QAAA,EACjD3D,MAAM,CAACqE,GAAG,CAAE9C,KAAK,iBAChB9B,OAAA;cAAwB0D,SAAS,EAAC,kBAAkB;cAAAQ,QAAA,gBAClDlE,OAAA;gBAAI0D,SAAS,EAAC,+DAA+D;gBAAAQ,QAAA,GAAC,GAC3E,EAACpC,KAAK,CAACY,OAAO;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACL9D,OAAA;gBAAI0D,SAAS,EAAC,6BAA6B;gBAAAQ,QAAA,eACzClE,OAAA;kBAAAkE,QAAA,gBACElE,OAAA;oBAAK0D,SAAS,EAAC,mCAAmC;oBAAAQ,QAAA,GAC/CpC,KAAK,CAACM,SAAS,EAAC,GAAC,EAACN,KAAK,CAACS,QAAQ;kBAAA;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACN9D,OAAA;oBAAK0D,SAAS,EAAC,uBAAuB;oBAAAQ,QAAA,GAAC,OAChC,EAACpC,KAAK,CAAC+C,SAAS;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9D,OAAA;gBAAI0D,SAAS,EAAC,6BAA6B;gBAAAQ,QAAA,eACzClE,OAAA;kBAAAkE,QAAA,gBACElE,OAAA;oBAAK0D,SAAS,EAAC,uBAAuB;oBAAAQ,QAAA,EAAEpC,KAAK,CAACU;kBAAK;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1D9D,OAAA;oBAAK0D,SAAS,EAAC,uBAAuB;oBAAAQ,QAAA,EAAEpC,KAAK,CAACW;kBAAW;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9D,OAAA;gBAAI0D,SAAS,EAAC,6BAA6B;gBAAAQ,QAAA,eACzClE,OAAA;kBAAK0D,SAAS,EAAC,mBAAmB;kBAAAQ,QAAA,GAC/BT,aAAa,CAAC3B,KAAK,CAACyB,MAAM,CAAC,eAC5BvD,OAAA;oBAAM0D,SAAS,EAAE,gFAAgFK,cAAc,CAACjC,KAAK,CAACyB,MAAM,CAAC,EAAG;oBAAAW,QAAA,EAC7HpC,KAAK,CAACyB;kBAAM;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL9D,OAAA;gBAAI0D,SAAS,EAAC,mDAAmD;gBAAAQ,QAAA,EAC9D,IAAIY,IAAI,CAAChD,KAAK,CAACiD,SAAS,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACL9D,OAAA;gBAAI0D,SAAS,EAAC,iDAAiD;gBAAAQ,QAAA,eAC7DlE,OAAA;kBAAK0D,SAAS,EAAC,gBAAgB;kBAAAQ,QAAA,gBAC7BlE,OAAA,CAAChB,IAAI;oBACHiG,EAAE,EAAE,WAAWnD,KAAK,CAACY,OAAO,EAAG;oBAC/BgB,SAAS,EAAC,qDAAqD;oBAAAQ,QAAA,gBAE/DlE,OAAA,CAACV,GAAG;sBAACoE,SAAS,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAElC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAEN,CAACzD,OAAO,CAAC,CAAC,IAAIC,UAAU,CAAC,CAAC,KAAKwB,KAAK,CAACyB,MAAM,KAAK,SAAS,iBACxDvD,OAAA,CAAAE,SAAA;oBAAAgE,QAAA,gBACElE,OAAA;sBACEmE,OAAO,EAAEA,CAAA,KAAMhB,kBAAkB,CAACrB,KAAK,CAACY,OAAO,EAAE,UAAU,CAAE;sBAC7DgB,SAAS,EAAC,uDAAuD;sBAAAQ,QAAA,gBAEjElE,OAAA,CAACT,WAAW;wBAACmE,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,WAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT9D,OAAA;sBACEmE,OAAO,EAAEA,CAAA,KAAMhB,kBAAkB,CAACrB,KAAK,CAACY,OAAO,EAAE,UAAU,CAAE;sBAC7DgB,SAAS,EAAC,mDAAmD;sBAAAQ,QAAA,gBAE7DlE,OAAA,CAACR,OAAO;wBAACkE,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEtC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACT,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA5DEhC,KAAK,CAACY,OAAO;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6DlB,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGL7C,UAAU,GAAG,CAAC,iBACbjB,OAAA;QAAK0D,SAAS,EAAC,uFAAuF;QAAAQ,QAAA,gBACpGlE,OAAA;UAAK0D,SAAS,EAAC,uCAAuC;UAAAQ,QAAA,gBACpDlE,OAAA;YACEmE,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAAC4B,IAAI,CAACsC,GAAG,CAACnE,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAE;YAC5DoE,QAAQ,EAAEpE,WAAW,KAAK,CAAE;YAC5B2C,SAAS,EAAC,2LAA2L;YAAAQ,QAAA,EACtM;UAED;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9D,OAAA;YACEmE,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAAC4B,IAAI,CAACwC,GAAG,CAACrE,WAAW,GAAG,CAAC,EAAEE,UAAU,CAAC,CAAE;YACrEkE,QAAQ,EAAEpE,WAAW,KAAKE,UAAW;YACrCyC,SAAS,EAAC,gMAAgM;YAAAQ,QAAA,EAC3M;UAED;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN9D,OAAA;UAAK0D,SAAS,EAAC,6DAA6D;UAAAQ,QAAA,gBAC1ElE,OAAA;YAAAkE,QAAA,eACElE,OAAA;cAAG0D,SAAS,EAAC,uBAAuB;cAAAQ,QAAA,GAAC,SAC5B,EAAC,GAAG,eACXlE,OAAA;gBAAM0D,SAAS,EAAC,aAAa;gBAAAQ,QAAA,EAAE,CAACnD,WAAW,GAAG,CAAC,IAAII,QAAQ,GAAG;cAAC;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACtE,GAAG,EAAC,IAAE,EAAC,GAAG,eACX9D,OAAA;gBAAM0D,SAAS,EAAC,aAAa;gBAAAQ,QAAA,EAC1BtB,IAAI,CAACwC,GAAG,CAACrE,WAAW,GAAGI,QAAQ,EAAEF,UAAU,GAAGE,QAAQ;cAAC;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,EACN,GAAG,EAAC,IAAE,EAAC,GAAG,eACX9D,OAAA;gBAAM0D,SAAS,EAAC,aAAa;gBAAAQ,QAAA,EAAEjD,UAAU,GAAGE;cAAQ;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC3D,GAAG,EAAC,SACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9D,OAAA;YAAAkE,QAAA,eACElE,OAAA;cAAK0D,SAAS,EAAC,2DAA2D;cAAC,cAAW,YAAY;cAAAQ,QAAA,gBAChGlE,OAAA;gBACEmE,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAAC4B,IAAI,CAACsC,GAAG,CAACnE,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAE;gBAC5DoE,QAAQ,EAAEpE,WAAW,KAAK,CAAE;gBAC5B2C,SAAS,EAAC,6LAA6L;gBAAAQ,QAAA,eAEvMlE,OAAA,CAACL,WAAW;kBAAC+D,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,EAER,CAAC,GAAGuB,KAAK,CAACpE,UAAU,CAAC,CAAC,CAAC2D,GAAG,CAAC,CAACU,CAAC,EAAEC,KAAK,KAAK;gBACxC,MAAMC,IAAI,GAAGD,KAAK,GAAG,CAAC;gBACtB,IAAIC,IAAI,KAAKzE,WAAW,IAAIyE,IAAI,KAAK,CAAC,IAAIA,IAAI,KAAKvE,UAAU,IAAKuE,IAAI,IAAIzE,WAAW,GAAG,CAAC,IAAIyE,IAAI,IAAIzE,WAAW,GAAG,CAAE,EAAE;kBACrH,oBACEf,OAAA;oBAEEmE,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAACwE,IAAI,CAAE;oBACpC9B,SAAS,EAAE,0EACT8B,IAAI,KAAKzE,WAAW,GAChB,+CAA+C,GAC/C,yDAAyD,EAC5D;oBAAAmD,QAAA,EAEFsB;kBAAI,GARAA,IAAI;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OASH,CAAC;gBAEb,CAAC,MAAM,IAAI0B,IAAI,KAAKzE,WAAW,GAAG,CAAC,IAAIyE,IAAI,KAAKzE,WAAW,GAAG,CAAC,EAAE;kBAC/D,oBACEf,OAAA;oBAAiB0D,SAAS,EAAC,+GAA+G;oBAAAQ,QAAA,EAAC;kBAE3I,GAFWsB,IAAI;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAET,CAAC;gBAEX;gBACA,OAAO,IAAI;cACb,CAAC,CAAC,eAEF9D,OAAA;gBACEmE,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAAC4B,IAAI,CAACwC,GAAG,CAACrE,WAAW,GAAG,CAAC,EAAEE,UAAU,CAAC,CAAE;gBACrEkE,QAAQ,EAAEpE,WAAW,KAAKE,UAAW;gBACrCyC,SAAS,EAAC,6LAA6L;gBAAAQ,QAAA,eAEvMlE,OAAA,CAACJ,YAAY;kBAAC8D,SAAS,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLvD,MAAM,CAACwC,MAAM,KAAK,CAAC,IAAI,CAACtC,OAAO,iBAC9BT,OAAA;MAAK0D,SAAS,EAAC,gCAAgC;MAAAQ,QAAA,eAC7ClE,OAAA;QAAK0D,SAAS,EAAC,mBAAmB;QAAAQ,QAAA,gBAChClE,OAAA,CAACN,WAAW;UAACgE,SAAS,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3D9D,OAAA;UAAI0D,SAAS,EAAC,wCAAwC;UAAAQ,QAAA,EAAC;QAAe;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E9D,OAAA;UAAG0D,SAAS,EAAC,4BAA4B;UAAAQ,QAAA,EACtCvD,UAAU,IAAIE,YAAY,GACvB,gDAAgD,GAChD;QAAgD;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1D,EAAA,CA9YID,SAAS;EAAA,QACmBjB,OAAO;AAAA;AAAAuG,EAAA,GADnCtF,SAAS;AAgZf,eAAeA,SAAS;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}