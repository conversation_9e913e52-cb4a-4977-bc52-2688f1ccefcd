{"ast": null, "code": "var _jsxFileName = \"D:\\\\Augment-projects\\\\IDFCAgentOnboardingAndManagementSolution\\\\idfc-agent-frontend\\\\src\\\\pages\\\\AgentDetails.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { agentAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { toast } from 'react-toastify';\nimport WorkflowTimeline from '../components/WorkflowTimeline';\nimport WorkflowStatusUpdate from '../components/WorkflowStatusUpdate';\nimport { ArrowLeft, User, Mail, Phone, MapPin, Calendar, CreditCard, FileText, CheckCircle, XCircle, Clock, AlertCircle, Download, Eye, MessageSquare, History } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AgentDetails = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    isAdmin,\n    isReviewer\n  } = useAuth();\n  const [agent, setAgent] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [statusHistory, setStatusHistory] = useState([]);\n  const [documents, setDocuments] = useState([]);\n  const [showStatusModal, setShowStatusModal] = useState(false);\n  const [newStatus, setNewStatus] = useState('');\n  const [comments, setComments] = useState('');\n  const [activeTab, setActiveTab] = useState('details');\n  useEffect(() => {\n    fetchAgentDetails();\n  }, [id]);\n  const fetchAgentDetails = async () => {\n    setLoading(true);\n    try {\n      const agentResponse = await agentAPI.getById(id);\n      setAgent(agentResponse);\n\n      // Fetch status history and documents if available\n      // These would be separate API calls in a real implementation\n      setStatusHistory([{\n        id: 1,\n        status: 'Pending',\n        comments: 'Application submitted',\n        createdAt: agentResponse.createdAt,\n        updatedBy: 'System'\n      }]);\n      setDocuments([{\n        type: 'aadharCard',\n        name: 'Aadhar Card',\n        status: 'Uploaded',\n        url: '#'\n      }, {\n        type: 'panCard',\n        name: 'PAN Card',\n        status: 'Uploaded',\n        url: '#'\n      }, {\n        type: 'photo',\n        name: 'Photo',\n        status: 'Uploaded',\n        url: '#'\n      }]);\n    } catch (error) {\n      console.error('Error fetching agent details:', error);\n      toast.error('Failed to fetch agent details');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getStatusValue = statusString => {\n    const statusMap = {\n      'Pending': 0,\n      'UnderReview': 1,\n      'Approved': 2,\n      'Rejected': 3,\n      'Active': 4,\n      'Inactive': 5,\n      'Suspended': 6\n    };\n    return statusMap[statusString] || 0;\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'Approved':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"h-6 w-6 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 16\n        }, this);\n      case 'Pending':\n        return /*#__PURE__*/_jsxDEV(Clock, {\n          className: \"h-6 w-6 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 16\n        }, this);\n      case 'Rejected':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"h-6 w-6 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 16\n        }, this);\n      case 'UnderReview':\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"h-6 w-6 text-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"h-6 w-6 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'Approved':\n        return 'bg-green-100 text-green-800 border-green-200';\n      case 'Pending':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'Rejected':\n        return 'bg-red-100 text-red-800 border-red-200';\n      case 'UnderReview':\n        return 'bg-blue-100 text-blue-800 border-blue-200';\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this);\n  }\n  if (!agent) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n        className: \"mx-auto h-12 w-12 text-gray-400\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"mt-2 text-sm font-medium text-gray-900\",\n        children: \"Agent not found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-500\",\n        children: [\"The agent with ID \", id, \" could not be found.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/agents'),\n        className: \"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\",\n        children: \"Back to Agents\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/agents'),\n          className: \"inline-flex items-center text-gray-500 hover:text-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n            className: \"h-5 w-5 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), \"Back to Agents\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: [agent.firstName, \" \", agent.lastName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-gray-600\",\n            children: [\"Agent ID: #\", agent.agentId, \" \\u2022 Applied on \", new Date(agent.createdAt).toLocaleDateString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex items-center px-3 py-2 rounded-lg border ${getStatusColor(agent.status)}`,\n          children: [getStatusIcon(agent.status), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 font-medium\",\n            children: agent.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this), (isAdmin() || isReviewer()) && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowStatusModal(true),\n          className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\",\n          children: \"Update Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"-mb-px flex space-x-8 px-6\",\n          \"aria-label\": \"Tabs\",\n          children: [{\n            id: 'details',\n            name: 'Personal Details',\n            icon: User\n          }, {\n            id: 'workflow',\n            name: 'Workflow Progress',\n            icon: Clock\n          }, {\n            id: 'documents',\n            name: 'Documents',\n            icon: FileText\n          }, {\n            id: 'history',\n            name: 'Status History',\n            icon: History\n          }].map(tab => {\n            const Icon = tab.icon;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab(tab.id),\n              className: `${activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`,\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                className: \"h-4 w-4 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), tab.name]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [activeTab === 'details' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-6 lg:grid-cols-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(User, {\n                  className: \"h-5 w-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this), \"Personal Information\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"dl\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Full Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-sm text-gray-900\",\n                    children: [agent.firstName, \" \", agent.lastName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Date of Birth\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-sm text-gray-900\",\n                    children: new Date(agent.dateOfBirth).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-sm text-gray-900 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Mail, {\n                      className: \"h-4 w-4 mr-2 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 25\n                    }, this), agent.email]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Phone Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-sm text-gray-900 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(Phone, {\n                      className: \"h-4 w-4 mr-2 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 25\n                    }, this), agent.phoneNumber]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 rounded-lg p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n                  className: \"h-5 w-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this), \"Identity Information\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"dl\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"Aadhar Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-sm text-gray-900\",\n                    children: agent.aadharNumber\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                    className: \"text-sm font-medium text-gray-500\",\n                    children: \"PAN Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                    className: \"text-sm text-gray-900\",\n                    children: agent.panNumber\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                className: \"h-5 w-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), \"Address Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"dl\", {\n              className: \"grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sm:col-span-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-sm text-gray-900\",\n                  children: agent.address\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"City\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-sm text-gray-900\",\n                  children: agent.city\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"State\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-sm text-gray-900\",\n                  children: agent.state\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"dt\", {\n                  className: \"text-sm font-medium text-gray-500\",\n                  children: \"PIN Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"dd\", {\n                  className: \"text-sm text-gray-900\",\n                  children: agent.pinCode\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this), activeTab === 'workflow' && /*#__PURE__*/_jsxDEV(WorkflowTimeline, {\n          agentId: agent.agentId,\n          showActions: isAdmin() || isReviewer()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this), activeTab === 'documents' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Uploaded Documents\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\",\n              children: documents.map((doc, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(FileText, {\n                      className: \"h-8 w-8 text-blue-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: doc.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 319,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: doc.type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\",\n                    children: doc.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 27\n                    }, this), \"View\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50\",\n                    children: [/*#__PURE__*/_jsxDEV(Download, {\n                      className: \"h-3 w-3 mr-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 27\n                    }, this), \"Download\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this), activeTab === 'history' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-gray-900 mb-4\",\n              children: \"Status History\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flow-root\",\n              children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"-mb-8\",\n                children: statusHistory.map((entry, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative pb-8\",\n                    children: [index !== statusHistory.length - 1 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"relative flex space-x-3\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: getStatusIcon(entry.status)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 358,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: [\"Status changed to \", /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"font-medium text-gray-900\",\n                              children: entry.status\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 364,\n                              columnNumber: 53\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 363,\n                            columnNumber: 33\n                          }, this), entry.comments && /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"mt-1 text-sm text-gray-600\",\n                            children: entry.comments\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 367,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 362,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-right text-sm whitespace-nowrap text-gray-500\",\n                          children: [/*#__PURE__*/_jsxDEV(\"time\", {\n                            dateTime: entry.createdAt,\n                            children: new Date(entry.createdAt).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 371,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\"by \", entry.updatedBy]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 374,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 370,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 357,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 25\n                  }, this)\n                }, entry.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), showStatusModal && /*#__PURE__*/_jsxDEV(WorkflowStatusUpdate, {\n      agentId: agent.agentId,\n      currentStatus: getStatusValue(agent.status),\n      onStatusUpdated: fetchAgentDetails,\n      onClose: () => setShowStatusModal(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentDetails, \"9l2zEtjLGjG2aodTjlXyxU3dmOk=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = AgentDetails;\nexport default AgentDetails;\nvar _c;\n$RefreshReg$(_c, \"AgentDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "agentAPI", "useAuth", "toast", "WorkflowTimeline", "WorkflowStatusUpdate", "ArrowLeft", "User", "Mail", "Phone", "MapPin", "Calendar", "CreditCard", "FileText", "CheckCircle", "XCircle", "Clock", "AlertCircle", "Download", "Eye", "MessageSquare", "History", "jsxDEV", "_jsxDEV", "AgentDetails", "_s", "id", "navigate", "isAdmin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "agent", "setAgent", "loading", "setLoading", "statusHistory", "setStatusHistory", "documents", "setDocuments", "showStatusModal", "setShowStatusModal", "newStatus", "setNewStatus", "comments", "setComments", "activeTab", "setActiveTab", "fetchAgentDetails", "agentResponse", "getById", "status", "createdAt", "updatedBy", "type", "name", "url", "error", "console", "getStatusValue", "statusString", "statusMap", "getStatusIcon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "children", "onClick", "firstName", "lastName", "agentId", "Date", "toLocaleDateString", "icon", "map", "tab", "Icon", "dateOfBirth", "email", "phoneNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "panNumber", "address", "city", "state", "pinCode", "showActions", "doc", "index", "entry", "length", "dateTime", "currentStatus", "onStatusUpdated", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Augment-projects/IDFCAgentOnboardingAndManagementSolution/idfc-agent-frontend/src/pages/AgentDetails.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { agentAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { toast } from 'react-toastify';\nimport WorkflowTimeline from '../components/WorkflowTimeline';\nimport WorkflowStatusUpdate from '../components/WorkflowStatusUpdate';\nimport {\n  ArrowLeft,\n  User,\n  Mail,\n  Phone,\n  MapPin,\n  Calendar,\n  CreditCard,\n  FileText,\n  CheckCircle,\n  XCircle,\n  Clock,\n  AlertCircle,\n  Download,\n  Eye,\n  MessageSquare,\n  History\n} from 'lucide-react';\n\nconst AgentDetails = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { isAdmin, isReviewer } = useAuth();\n  const [agent, setAgent] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [statusHistory, setStatusHistory] = useState([]);\n  const [documents, setDocuments] = useState([]);\n  const [showStatusModal, setShowStatusModal] = useState(false);\n  const [newStatus, setNewStatus] = useState('');\n  const [comments, setComments] = useState('');\n  const [activeTab, setActiveTab] = useState('details');\n\n  useEffect(() => {\n    fetchAgentDetails();\n  }, [id]);\n\n  const fetchAgentDetails = async () => {\n    setLoading(true);\n    try {\n      const agentResponse = await agentAPI.getById(id);\n      setAgent(agentResponse);\n\n      // Fetch status history and documents if available\n      // These would be separate API calls in a real implementation\n      setStatusHistory([\n        {\n          id: 1,\n          status: 'Pending',\n          comments: 'Application submitted',\n          createdAt: agentResponse.createdAt,\n          updatedBy: 'System'\n        }\n      ]);\n\n      setDocuments([\n        { type: 'aadharCard', name: 'Aadhar Card', status: 'Uploaded', url: '#' },\n        { type: 'panCard', name: 'PAN Card', status: 'Uploaded', url: '#' },\n        { type: 'photo', name: 'Photo', status: 'Uploaded', url: '#' }\n      ]);\n\n    } catch (error) {\n      console.error('Error fetching agent details:', error);\n      toast.error('Failed to fetch agent details');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusValue = (statusString) => {\n    const statusMap = {\n      'Pending': 0,\n      'UnderReview': 1,\n      'Approved': 2,\n      'Rejected': 3,\n      'Active': 4,\n      'Inactive': 5,\n      'Suspended': 6\n    };\n    return statusMap[statusString] || 0;\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'Approved':\n        return <CheckCircle className=\"h-6 w-6 text-green-500\" />;\n      case 'Pending':\n        return <Clock className=\"h-6 w-6 text-yellow-500\" />;\n      case 'Rejected':\n        return <XCircle className=\"h-6 w-6 text-red-500\" />;\n      case 'UnderReview':\n        return <AlertCircle className=\"h-6 w-6 text-blue-500\" />;\n      default:\n        return <AlertCircle className=\"h-6 w-6 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Approved':\n        return 'bg-green-100 text-green-800 border-green-200';\n      case 'Pending':\n        return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'Rejected':\n        return 'bg-red-100 text-red-800 border-red-200';\n      case 'UnderReview':\n        return 'bg-blue-100 text-blue-800 border-blue-200';\n      default:\n        return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!agent) {\n    return (\n      <div className=\"text-center py-12\">\n        <AlertCircle className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">Agent not found</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          The agent with ID {id} could not be found.\n        </p>\n        <button\n          onClick={() => navigate('/agents')}\n          className=\"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n        >\n          Back to Agents\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={() => navigate('/agents')}\n            className=\"inline-flex items-center text-gray-500 hover:text-gray-700\"\n          >\n            <ArrowLeft className=\"h-5 w-5 mr-1\" />\n            Back to Agents\n          </button>\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">\n              {agent.firstName} {agent.lastName}\n            </h1>\n            <p className=\"mt-1 text-sm text-gray-600\">\n              Agent ID: #{agent.agentId} • Applied on {new Date(agent.createdAt).toLocaleDateString()}\n            </p>\n          </div>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <div className={`flex items-center px-3 py-2 rounded-lg border ${getStatusColor(agent.status)}`}>\n            {getStatusIcon(agent.status)}\n            <span className=\"ml-2 font-medium\">{agent.status}</span>\n          </div>\n\n          {(isAdmin() || isReviewer()) && (\n            <button\n              onClick={() => setShowStatusModal(true)}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\"\n            >\n              Update Status\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"bg-white shadow rounded-lg\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"-mb-px flex space-x-8 px-6\" aria-label=\"Tabs\">\n            {[\n              { id: 'details', name: 'Personal Details', icon: User },\n              { id: 'workflow', name: 'Workflow Progress', icon: Clock },\n              { id: 'documents', name: 'Documents', icon: FileText },\n              { id: 'history', name: 'Status History', icon: History }\n            ].map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}\n                >\n                  <Icon className=\"h-4 w-4 mr-2\" />\n                  {tab.name}\n                </button>\n              );\n            })}\n          </nav>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Personal Details Tab */}\n          {activeTab === 'details' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-2\">\n                {/* Personal Information */}\n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                    <User className=\"h-5 w-5 mr-2\" />\n                    Personal Information\n                  </h3>\n                  <dl className=\"space-y-3\">\n                    <div>\n                      <dt className=\"text-sm font-medium text-gray-500\">Full Name</dt>\n                      <dd className=\"text-sm text-gray-900\">{agent.firstName} {agent.lastName}</dd>\n                    </div>\n                    <div>\n                      <dt className=\"text-sm font-medium text-gray-500\">Date of Birth</dt>\n                      <dd className=\"text-sm text-gray-900\">\n                        {new Date(agent.dateOfBirth).toLocaleDateString()}\n                      </dd>\n                    </div>\n                    <div>\n                      <dt className=\"text-sm font-medium text-gray-500\">Email</dt>\n                      <dd className=\"text-sm text-gray-900 flex items-center\">\n                        <Mail className=\"h-4 w-4 mr-2 text-gray-400\" />\n                        {agent.email}\n                      </dd>\n                    </div>\n                    <div>\n                      <dt className=\"text-sm font-medium text-gray-500\">Phone Number</dt>\n                      <dd className=\"text-sm text-gray-900 flex items-center\">\n                        <Phone className=\"h-4 w-4 mr-2 text-gray-400\" />\n                        {agent.phoneNumber}\n                      </dd>\n                    </div>\n                  </dl>\n                </div>\n\n                {/* Identity Information */}\n                <div className=\"bg-gray-50 rounded-lg p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                    <CreditCard className=\"h-5 w-5 mr-2\" />\n                    Identity Information\n                  </h3>\n                  <dl className=\"space-y-3\">\n                    <div>\n                      <dt className=\"text-sm font-medium text-gray-500\">Aadhar Number</dt>\n                      <dd className=\"text-sm text-gray-900\">{agent.aadharNumber}</dd>\n                    </div>\n                    <div>\n                      <dt className=\"text-sm font-medium text-gray-500\">PAN Number</dt>\n                      <dd className=\"text-sm text-gray-900\">{agent.panNumber}</dd>\n                    </div>\n                  </dl>\n                </div>\n              </div>\n\n              {/* Address Information */}\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4 flex items-center\">\n                  <MapPin className=\"h-5 w-5 mr-2\" />\n                  Address Information\n                </h3>\n                <dl className=\"grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-4\">\n                  <div className=\"sm:col-span-2\">\n                    <dt className=\"text-sm font-medium text-gray-500\">Address</dt>\n                    <dd className=\"text-sm text-gray-900\">{agent.address}</dd>\n                  </div>\n                  <div>\n                    <dt className=\"text-sm font-medium text-gray-500\">City</dt>\n                    <dd className=\"text-sm text-gray-900\">{agent.city}</dd>\n                  </div>\n                  <div>\n                    <dt className=\"text-sm font-medium text-gray-500\">State</dt>\n                    <dd className=\"text-sm text-gray-900\">{agent.state}</dd>\n                  </div>\n                  <div>\n                    <dt className=\"text-sm font-medium text-gray-500\">PIN Code</dt>\n                    <dd className=\"text-sm text-gray-900\">{agent.pinCode}</dd>\n                  </div>\n                </dl>\n              </div>\n            </div>\n          )}\n\n          {/* Workflow Progress Tab */}\n          {activeTab === 'workflow' && (\n            <WorkflowTimeline\n              agentId={agent.agentId}\n              showActions={isAdmin() || isReviewer()}\n            />\n          )}\n\n          {/* Documents Tab */}\n          {activeTab === 'documents' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Uploaded Documents</h3>\n                <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\">\n                  {documents.map((doc, index) => (\n                    <div key={index} className=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <div className=\"flex items-center\">\n                          <FileText className=\"h-8 w-8 text-blue-500\" />\n                          <div className=\"ml-3\">\n                            <h4 className=\"text-sm font-medium text-gray-900\">{doc.name}</h4>\n                            <p className=\"text-xs text-gray-500\">{doc.type}</p>\n                          </div>\n                        </div>\n                        <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                          {doc.status}\n                        </span>\n                      </div>\n                      <div className=\"flex space-x-2\">\n                        <button className=\"flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50\">\n                          <Eye className=\"h-3 w-3 mr-1\" />\n                          View\n                        </button>\n                        <button className=\"flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50\">\n                          <Download className=\"h-3 w-3 mr-1\" />\n                          Download\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Status History Tab */}\n          {activeTab === 'history' && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Status History</h3>\n                <div className=\"flow-root\">\n                  <ul className=\"-mb-8\">\n                    {statusHistory.map((entry, index) => (\n                      <li key={entry.id}>\n                        <div className=\"relative pb-8\">\n                          {index !== statusHistory.length - 1 && (\n                            <span className=\"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200\" aria-hidden=\"true\" />\n                          )}\n                          <div className=\"relative flex space-x-3\">\n                            <div>\n                              {getStatusIcon(entry.status)}\n                            </div>\n                            <div className=\"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4\">\n                              <div>\n                                <p className=\"text-sm text-gray-500\">\n                                  Status changed to <span className=\"font-medium text-gray-900\">{entry.status}</span>\n                                </p>\n                                {entry.comments && (\n                                  <p className=\"mt-1 text-sm text-gray-600\">{entry.comments}</p>\n                                )}\n                              </div>\n                              <div className=\"text-right text-sm whitespace-nowrap text-gray-500\">\n                                <time dateTime={entry.createdAt}>\n                                  {new Date(entry.createdAt).toLocaleDateString()}\n                                </time>\n                                <p className=\"text-xs text-gray-400\">by {entry.updatedBy}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Status Update Modal */}\n      {showStatusModal && (\n        <WorkflowStatusUpdate\n          agentId={agent.agentId}\n          currentStatus={getStatusValue(agent.status)}\n          onStatusUpdated={fetchAgentDetails}\n          onClose={() => setShowStatusModal(false)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default AgentDetails;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,oBAAoB,MAAM,oCAAoC;AACrE,SACEC,SAAS,EACTC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,OAAO,EACPC,KAAK,EACLC,WAAW,EACXC,QAAQ,EACRC,GAAG,EACHC,aAAa,EACbC,OAAO,QACF,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAG,CAAC,GAAG3B,SAAS,CAAC,CAAC;EAC1B,MAAM4B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4B,OAAO;IAAEC;EAAW,CAAC,GAAG3B,OAAO,CAAC,CAAC;EACzC,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,SAAS,CAAC;EAErDC,SAAS,CAAC,MAAM;IACdgD,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACpB,EAAE,CAAC,CAAC;EAER,MAAMoB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMc,aAAa,GAAG,MAAM9C,QAAQ,CAAC+C,OAAO,CAACtB,EAAE,CAAC;MAChDK,QAAQ,CAACgB,aAAa,CAAC;;MAEvB;MACA;MACAZ,gBAAgB,CAAC,CACf;QACET,EAAE,EAAE,CAAC;QACLuB,MAAM,EAAE,SAAS;QACjBP,QAAQ,EAAE,uBAAuB;QACjCQ,SAAS,EAAEH,aAAa,CAACG,SAAS;QAClCC,SAAS,EAAE;MACb,CAAC,CACF,CAAC;MAEFd,YAAY,CAAC,CACX;QAAEe,IAAI,EAAE,YAAY;QAAEC,IAAI,EAAE,aAAa;QAAEJ,MAAM,EAAE,UAAU;QAAEK,GAAG,EAAE;MAAI,CAAC,EACzE;QAAEF,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE,UAAU;QAAEJ,MAAM,EAAE,UAAU;QAAEK,GAAG,EAAE;MAAI,CAAC,EACnE;QAAEF,IAAI,EAAE,OAAO;QAAEC,IAAI,EAAE,OAAO;QAAEJ,MAAM,EAAE,UAAU;QAAEK,GAAG,EAAE;MAAI,CAAC,CAC/D,CAAC;IAEJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDpD,KAAK,CAACoD,KAAK,CAAC,+BAA+B,CAAC;IAC9C,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,cAAc,GAAIC,YAAY,IAAK;IACvC,MAAMC,SAAS,GAAG;MAChB,SAAS,EAAE,CAAC;MACZ,aAAa,EAAE,CAAC;MAChB,UAAU,EAAE,CAAC;MACb,UAAU,EAAE,CAAC;MACb,QAAQ,EAAE,CAAC;MACX,UAAU,EAAE,CAAC;MACb,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,SAAS,CAACD,YAAY,CAAC,IAAI,CAAC;EACrC,CAAC;EAED,MAAME,aAAa,GAAIX,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,oBAAO1B,OAAA,CAACT,WAAW;UAAC+C,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,SAAS;QACZ,oBAAO1C,OAAA,CAACP,KAAK;UAAC6C,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD,KAAK,UAAU;QACb,oBAAO1C,OAAA,CAACR,OAAO;UAAC8C,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD,KAAK,aAAa;QAChB,oBAAO1C,OAAA,CAACN,WAAW;UAAC4C,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1D;QACE,oBAAO1C,OAAA,CAACN,WAAW;UAAC4C,SAAS,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC5D;EACF,CAAC;EAED,MAAMC,cAAc,GAAIjB,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,8CAA8C;MACvD,KAAK,SAAS;QACZ,OAAO,iDAAiD;MAC1D,KAAK,UAAU;QACb,OAAO,wCAAwC;MACjD,KAAK,aAAa;QAChB,OAAO,2CAA2C;MACpD;QACE,OAAO,2CAA2C;IACtD;EACF,CAAC;EAED,IAAIjB,OAAO,EAAE;IACX,oBACET,OAAA;MAAKsC,SAAS,EAAC,uCAAuC;MAAAM,QAAA,eACpD5C,OAAA;QAAKsC,SAAS,EAAC;MAAgE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,IAAI,CAACnC,KAAK,EAAE;IACV,oBACEP,OAAA;MAAKsC,SAAS,EAAC,mBAAmB;MAAAM,QAAA,gBAChC5C,OAAA,CAACN,WAAW;QAAC4C,SAAS,EAAC;MAAiC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3D1C,OAAA;QAAIsC,SAAS,EAAC,wCAAwC;QAAAM,QAAA,EAAC;MAAe;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3E1C,OAAA;QAAGsC,SAAS,EAAC,4BAA4B;QAAAM,QAAA,GAAC,oBACtB,EAACzC,EAAE,EAAC,sBACxB;MAAA;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ1C,OAAA;QACE6C,OAAO,EAAEA,CAAA,KAAMzC,QAAQ,CAAC,SAAS,CAAE;QACnCkC,SAAS,EAAC,2IAA2I;QAAAM,QAAA,EACtJ;MAED;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE1C,OAAA;IAAKsC,SAAS,EAAC,WAAW;IAAAM,QAAA,gBAExB5C,OAAA;MAAKsC,SAAS,EAAC,mCAAmC;MAAAM,QAAA,gBAChD5C,OAAA;QAAKsC,SAAS,EAAC,6BAA6B;QAAAM,QAAA,gBAC1C5C,OAAA;UACE6C,OAAO,EAAEA,CAAA,KAAMzC,QAAQ,CAAC,SAAS,CAAE;UACnCkC,SAAS,EAAC,4DAA4D;UAAAM,QAAA,gBAEtE5C,OAAA,CAACjB,SAAS;YAACuD,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1C,OAAA;UAAA4C,QAAA,gBACE5C,OAAA;YAAIsC,SAAS,EAAC,kCAAkC;YAAAM,QAAA,GAC7CrC,KAAK,CAACuC,SAAS,EAAC,GAAC,EAACvC,KAAK,CAACwC,QAAQ;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACL1C,OAAA;YAAGsC,SAAS,EAAC,4BAA4B;YAAAM,QAAA,GAAC,aAC7B,EAACrC,KAAK,CAACyC,OAAO,EAAC,qBAAc,EAAC,IAAIC,IAAI,CAAC1C,KAAK,CAACoB,SAAS,CAAC,CAACuB,kBAAkB,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1C,OAAA;QAAKsC,SAAS,EAAC,6BAA6B;QAAAM,QAAA,gBAC1C5C,OAAA;UAAKsC,SAAS,EAAE,iDAAiDK,cAAc,CAACpC,KAAK,CAACmB,MAAM,CAAC,EAAG;UAAAkB,QAAA,GAC7FP,aAAa,CAAC9B,KAAK,CAACmB,MAAM,CAAC,eAC5B1B,OAAA;YAAMsC,SAAS,EAAC,kBAAkB;YAAAM,QAAA,EAAErC,KAAK,CAACmB;UAAM;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,EAEL,CAACrC,OAAO,CAAC,CAAC,IAAIC,UAAU,CAAC,CAAC,kBACzBN,OAAA;UACE6C,OAAO,EAAEA,CAAA,KAAM7B,kBAAkB,CAAC,IAAI,CAAE;UACxCsB,SAAS,EAAC,sIAAsI;UAAAM,QAAA,EACjJ;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA;MAAKsC,SAAS,EAAC,4BAA4B;MAAAM,QAAA,gBACzC5C,OAAA;QAAKsC,SAAS,EAAC,0BAA0B;QAAAM,QAAA,eACvC5C,OAAA;UAAKsC,SAAS,EAAC,4BAA4B;UAAC,cAAW,MAAM;UAAAM,QAAA,EAC1D,CACC;YAAEzC,EAAE,EAAE,SAAS;YAAE2B,IAAI,EAAE,kBAAkB;YAAEqB,IAAI,EAAEnE;UAAK,CAAC,EACvD;YAAEmB,EAAE,EAAE,UAAU;YAAE2B,IAAI,EAAE,mBAAmB;YAAEqB,IAAI,EAAE1D;UAAM,CAAC,EAC1D;YAAEU,EAAE,EAAE,WAAW;YAAE2B,IAAI,EAAE,WAAW;YAAEqB,IAAI,EAAE7D;UAAS,CAAC,EACtD;YAAEa,EAAE,EAAE,SAAS;YAAE2B,IAAI,EAAE,gBAAgB;YAAEqB,IAAI,EAAErD;UAAQ,CAAC,CACzD,CAACsD,GAAG,CAAEC,GAAG,IAAK;YACb,MAAMC,IAAI,GAAGD,GAAG,CAACF,IAAI;YACrB,oBACEnD,OAAA;cAEE6C,OAAO,EAAEA,CAAA,KAAMvB,YAAY,CAAC+B,GAAG,CAAClD,EAAE,CAAE;cACpCmC,SAAS,EAAE,GACTjB,SAAS,KAAKgC,GAAG,CAAClD,EAAE,GAChB,+BAA+B,GAC/B,4EAA4E,+EACF;cAAAyC,QAAA,gBAEhF5C,OAAA,CAACsD,IAAI;gBAAChB,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAChCW,GAAG,CAACvB,IAAI;YAAA,GATJuB,GAAG,CAAClD,EAAE;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUL,CAAC;UAEb,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1C,OAAA;QAAKsC,SAAS,EAAC,KAAK;QAAAM,QAAA,GAEjBvB,SAAS,KAAK,SAAS,iBACtBrB,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAM,QAAA,gBACxB5C,OAAA;YAAKsC,SAAS,EAAC,uCAAuC;YAAAM,QAAA,gBAEpD5C,OAAA;cAAKsC,SAAS,EAAC,2BAA2B;cAAAM,QAAA,gBACxC5C,OAAA;gBAAIsC,SAAS,EAAC,0DAA0D;gBAAAM,QAAA,gBACtE5C,OAAA,CAAChB,IAAI;kBAACsD,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wBAEnC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1C,OAAA;gBAAIsC,SAAS,EAAC,WAAW;gBAAAM,QAAA,gBACvB5C,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAIsC,SAAS,EAAC,mCAAmC;oBAAAM,QAAA,EAAC;kBAAS;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChE1C,OAAA;oBAAIsC,SAAS,EAAC,uBAAuB;oBAAAM,QAAA,GAAErC,KAAK,CAACuC,SAAS,EAAC,GAAC,EAACvC,KAAK,CAACwC,QAAQ;kBAAA;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACN1C,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAIsC,SAAS,EAAC,mCAAmC;oBAAAM,QAAA,EAAC;kBAAa;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpE1C,OAAA;oBAAIsC,SAAS,EAAC,uBAAuB;oBAAAM,QAAA,EAClC,IAAIK,IAAI,CAAC1C,KAAK,CAACgD,WAAW,CAAC,CAACL,kBAAkB,CAAC;kBAAC;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACN1C,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAIsC,SAAS,EAAC,mCAAmC;oBAAAM,QAAA,EAAC;kBAAK;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5D1C,OAAA;oBAAIsC,SAAS,EAAC,yCAAyC;oBAAAM,QAAA,gBACrD5C,OAAA,CAACf,IAAI;sBAACqD,SAAS,EAAC;oBAA4B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC9CnC,KAAK,CAACiD,KAAK;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACN1C,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAIsC,SAAS,EAAC,mCAAmC;oBAAAM,QAAA,EAAC;kBAAY;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnE1C,OAAA;oBAAIsC,SAAS,EAAC,yCAAyC;oBAAAM,QAAA,gBACrD5C,OAAA,CAACd,KAAK;sBAACoD,SAAS,EAAC;oBAA4B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC/CnC,KAAK,CAACkD,WAAW;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGN1C,OAAA;cAAKsC,SAAS,EAAC,2BAA2B;cAAAM,QAAA,gBACxC5C,OAAA;gBAAIsC,SAAS,EAAC,0DAA0D;gBAAAM,QAAA,gBACtE5C,OAAA,CAACX,UAAU;kBAACiD,SAAS,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wBAEzC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1C,OAAA;gBAAIsC,SAAS,EAAC,WAAW;gBAAAM,QAAA,gBACvB5C,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAIsC,SAAS,EAAC,mCAAmC;oBAAAM,QAAA,EAAC;kBAAa;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpE1C,OAAA;oBAAIsC,SAAS,EAAC,uBAAuB;oBAAAM,QAAA,EAAErC,KAAK,CAACmD;kBAAY;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC,eACN1C,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAIsC,SAAS,EAAC,mCAAmC;oBAAAM,QAAA,EAAC;kBAAU;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjE1C,OAAA;oBAAIsC,SAAS,EAAC,uBAAuB;oBAAAM,QAAA,EAAErC,KAAK,CAACoD;kBAAS;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1C,OAAA;YAAKsC,SAAS,EAAC,2BAA2B;YAAAM,QAAA,gBACxC5C,OAAA;cAAIsC,SAAS,EAAC,0DAA0D;cAAAM,QAAA,gBACtE5C,OAAA,CAACb,MAAM;gBAACmD,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1C,OAAA;cAAIsC,SAAS,EAAC,sDAAsD;cAAAM,QAAA,gBAClE5C,OAAA;gBAAKsC,SAAS,EAAC,eAAe;gBAAAM,QAAA,gBAC5B5C,OAAA;kBAAIsC,SAAS,EAAC,mCAAmC;kBAAAM,QAAA,EAAC;gBAAO;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9D1C,OAAA;kBAAIsC,SAAS,EAAC,uBAAuB;kBAAAM,QAAA,EAAErC,KAAK,CAACqD;gBAAO;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACN1C,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAIsC,SAAS,EAAC,mCAAmC;kBAAAM,QAAA,EAAC;gBAAI;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3D1C,OAAA;kBAAIsC,SAAS,EAAC,uBAAuB;kBAAAM,QAAA,EAAErC,KAAK,CAACsD;gBAAI;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACN1C,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAIsC,SAAS,EAAC,mCAAmC;kBAAAM,QAAA,EAAC;gBAAK;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5D1C,OAAA;kBAAIsC,SAAS,EAAC,uBAAuB;kBAAAM,QAAA,EAAErC,KAAK,CAACuD;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACN1C,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAIsC,SAAS,EAAC,mCAAmC;kBAAAM,QAAA,EAAC;gBAAQ;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/D1C,OAAA;kBAAIsC,SAAS,EAAC,uBAAuB;kBAAAM,QAAA,EAAErC,KAAK,CAACwD;gBAAO;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGArB,SAAS,KAAK,UAAU,iBACvBrB,OAAA,CAACnB,gBAAgB;UACfmE,OAAO,EAAEzC,KAAK,CAACyC,OAAQ;UACvBgB,WAAW,EAAE3D,OAAO,CAAC,CAAC,IAAIC,UAAU,CAAC;QAAE;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACF,EAGArB,SAAS,KAAK,WAAW,iBACxBrB,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAM,QAAA,eACxB5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAIsC,SAAS,EAAC,wCAAwC;cAAAM,QAAA,EAAC;YAAkB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9E1C,OAAA;cAAKsC,SAAS,EAAC,sDAAsD;cAAAM,QAAA,EAClE/B,SAAS,CAACuC,GAAG,CAAC,CAACa,GAAG,EAAEC,KAAK,kBACxBlE,OAAA;gBAAiBsC,SAAS,EAAC,yEAAyE;gBAAAM,QAAA,gBAClG5C,OAAA;kBAAKsC,SAAS,EAAC,wCAAwC;kBAAAM,QAAA,gBACrD5C,OAAA;oBAAKsC,SAAS,EAAC,mBAAmB;oBAAAM,QAAA,gBAChC5C,OAAA,CAACV,QAAQ;sBAACgD,SAAS,EAAC;oBAAuB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9C1C,OAAA;sBAAKsC,SAAS,EAAC,MAAM;sBAAAM,QAAA,gBACnB5C,OAAA;wBAAIsC,SAAS,EAAC,mCAAmC;wBAAAM,QAAA,EAAEqB,GAAG,CAACnC;sBAAI;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACjE1C,OAAA;wBAAGsC,SAAS,EAAC,uBAAuB;wBAAAM,QAAA,EAAEqB,GAAG,CAACpC;sBAAI;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1C,OAAA;oBAAMsC,SAAS,EAAC,qGAAqG;oBAAAM,QAAA,EAClHqB,GAAG,CAACvC;kBAAM;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN1C,OAAA;kBAAKsC,SAAS,EAAC,gBAAgB;kBAAAM,QAAA,gBAC7B5C,OAAA;oBAAQsC,SAAS,EAAC,+JAA+J;oBAAAM,QAAA,gBAC/K5C,OAAA,CAACJ,GAAG;sBAAC0C,SAAS,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,QAElC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT1C,OAAA;oBAAQsC,SAAS,EAAC,+JAA+J;oBAAAM,QAAA,gBAC/K5C,OAAA,CAACL,QAAQ;sBAAC2C,SAAS,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,YAEvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GAtBEwB,KAAK;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGArB,SAAS,KAAK,SAAS,iBACtBrB,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAM,QAAA,eACxB5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAIsC,SAAS,EAAC,wCAAwC;cAAAM,QAAA,EAAC;YAAc;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1E1C,OAAA;cAAKsC,SAAS,EAAC,WAAW;cAAAM,QAAA,eACxB5C,OAAA;gBAAIsC,SAAS,EAAC,OAAO;gBAAAM,QAAA,EAClBjC,aAAa,CAACyC,GAAG,CAAC,CAACe,KAAK,EAAED,KAAK,kBAC9BlE,OAAA;kBAAA4C,QAAA,eACE5C,OAAA;oBAAKsC,SAAS,EAAC,eAAe;oBAAAM,QAAA,GAC3BsB,KAAK,KAAKvD,aAAa,CAACyD,MAAM,GAAG,CAAC,iBACjCpE,OAAA;sBAAMsC,SAAS,EAAC,uDAAuD;sBAAC,eAAY;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC7F,eACD1C,OAAA;sBAAKsC,SAAS,EAAC,yBAAyB;sBAAAM,QAAA,gBACtC5C,OAAA;wBAAA4C,QAAA,EACGP,aAAa,CAAC8B,KAAK,CAACzC,MAAM;sBAAC;wBAAAa,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CAAC,eACN1C,OAAA;wBAAKsC,SAAS,EAAC,sDAAsD;wBAAAM,QAAA,gBACnE5C,OAAA;0BAAA4C,QAAA,gBACE5C,OAAA;4BAAGsC,SAAS,EAAC,uBAAuB;4BAAAM,QAAA,GAAC,oBACjB,eAAA5C,OAAA;8BAAMsC,SAAS,EAAC,2BAA2B;8BAAAM,QAAA,EAAEuB,KAAK,CAACzC;4BAAM;8BAAAa,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClF,CAAC,EACHyB,KAAK,CAAChD,QAAQ,iBACbnB,OAAA;4BAAGsC,SAAS,EAAC,4BAA4B;4BAAAM,QAAA,EAAEuB,KAAK,CAAChD;0BAAQ;4BAAAoB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAC9D;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACN1C,OAAA;0BAAKsC,SAAS,EAAC,oDAAoD;0BAAAM,QAAA,gBACjE5C,OAAA;4BAAMqE,QAAQ,EAAEF,KAAK,CAACxC,SAAU;4BAAAiB,QAAA,EAC7B,IAAIK,IAAI,CAACkB,KAAK,CAACxC,SAAS,CAAC,CAACuB,kBAAkB,CAAC;0BAAC;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC3C,CAAC,eACP1C,OAAA;4BAAGsC,SAAS,EAAC,uBAAuB;4BAAAM,QAAA,GAAC,KAAG,EAACuB,KAAK,CAACvC,SAAS;0BAAA;4BAAAW,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GA1BCyB,KAAK,CAAChE,EAAE;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2Bb,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3B,eAAe,iBACdf,OAAA,CAAClB,oBAAoB;MACnBkE,OAAO,EAAEzC,KAAK,CAACyC,OAAQ;MACvBsB,aAAa,EAAEpC,cAAc,CAAC3B,KAAK,CAACmB,MAAM,CAAE;MAC5C6C,eAAe,EAAEhD,iBAAkB;MACnCiD,OAAO,EAAEA,CAAA,KAAMxD,kBAAkB,CAAC,KAAK;IAAE;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxC,EAAA,CArXID,YAAY;EAAA,QACDzB,SAAS,EACPC,WAAW,EACIE,OAAO;AAAA;AAAA8F,EAAA,GAHnCxE,YAAY;AAuXlB,eAAeA,YAAY;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}