{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"3\",\n  height: \"8\",\n  x: \"13\",\n  y: \"2\",\n  rx: \"1.5\",\n  key: \"diqz80\"\n}], [\"path\", {\n  d: \"M19 8.5V10h1.5A1.5 1.5 0 1 0 19 8.5\",\n  key: \"183iwg\"\n}], [\"rect\", {\n  width: \"3\",\n  height: \"8\",\n  x: \"8\",\n  y: \"14\",\n  rx: \"1.5\",\n  key: \"hqg7r1\"\n}], [\"path\", {\n  d: \"M5 15.5V14H3.5A1.5 1.5 0 1 0 5 15.5\",\n  key: \"76g71w\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"3\",\n  x: \"14\",\n  y: \"13\",\n  rx: \"1.5\",\n  key: \"1kmz0a\"\n}], [\"path\", {\n  d: \"M15.5 19H14v1.5a1.5 1.5 0 1 0 1.5-1.5\",\n  key: \"jc4sz0\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"3\",\n  x: \"2\",\n  y: \"8\",\n  rx: \"1.5\",\n  key: \"1omvl4\"\n}], [\"path\", {\n  d: \"M8.5 5H10V3.5A1.5 1.5 0 1 0 8.5 5\",\n  key: \"16f3cl\"\n}]];\nconst Slack = createLucideIcon(\"slack\", __iconNode);\nexport { __iconNode, Slack as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "<PERSON><PERSON>ck", "createLucideIcon"], "sources": ["D:\\Augment-projects\\IDFCAgentOnboardingAndManagementSolution\\idfc-agent-frontend\\node_modules\\lucide-react\\src\\icons\\slack.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '3', height: '8', x: '13', y: '2', rx: '1.5', key: 'diqz80' }],\n  ['path', { d: 'M19 8.5V10h1.5A1.5 1.5 0 1 0 19 8.5', key: '183iwg' }],\n  ['rect', { width: '3', height: '8', x: '8', y: '14', rx: '1.5', key: 'hqg7r1' }],\n  ['path', { d: 'M5 15.5V14H3.5A1.5 1.5 0 1 0 5 15.5', key: '76g71w' }],\n  ['rect', { width: '8', height: '3', x: '14', y: '13', rx: '1.5', key: '1kmz0a' }],\n  ['path', { d: 'M15.5 19H14v1.5a1.5 1.5 0 1 0 1.5-1.5', key: 'jc4sz0' }],\n  ['rect', { width: '8', height: '3', x: '2', y: '8', rx: '1.5', key: '1omvl4' }],\n  ['path', { d: 'M8.5 5H10V3.5A1.5 1.5 0 1 0 8.5 5', key: '16f3cl' }],\n];\n\n/**\n * @component @name Slack\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMyIgaGVpZ2h0PSI4IiB4PSIxMyIgeT0iMiIgcng9IjEuNSIgLz4KICA8cGF0aCBkPSJNMTkgOC41VjEwaDEuNUExLjUgMS41IDAgMSAwIDE5IDguNSIgLz4KICA8cmVjdCB3aWR0aD0iMyIgaGVpZ2h0PSI4IiB4PSI4IiB5PSIxNCIgcng9IjEuNSIgLz4KICA8cGF0aCBkPSJNNSAxNS41VjE0SDMuNUExLjUgMS41IDAgMSAwIDUgMTUuNSIgLz4KICA8cmVjdCB3aWR0aD0iOCIgaGVpZ2h0PSIzIiB4PSIxNCIgeT0iMTMiIHJ4PSIxLjUiIC8+CiAgPHBhdGggZD0iTTE1LjUgMTlIMTR2MS41YTEuNSAxLjUgMCAxIDAgMS41LTEuNSIgLz4KICA8cmVjdCB3aWR0aD0iOCIgaGVpZ2h0PSIzIiB4PSIyIiB5PSI4IiByeD0iMS41IiAvPgogIDxwYXRoIGQ9Ik04LjUgNUgxMFYzLjVBMS41IDEuNSAwIDEgMCA4LjUgNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/slack\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n * @deprecated Brand icons have been deprecated and are due to be removed, please refer to https://github.com/lucide-icons/lucide/issues/670. We recommend using https://simpleicons.org/?q=slack instead. This icon will be removed in v1.0\n */\nconst Slack = createLucideIcon('slack', __iconNode);\n\nexport default Slack;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAMC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,KAAO;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/E,CAAC,MAAQ;EAAEC,CAAA,EAAG,qCAAuC;EAAAD,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,KAAO;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/E,CAAC,MAAQ;EAAEC,CAAA,EAAG,qCAAuC;EAAAD,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAMC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,KAAO;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChF,CAAC,MAAQ;EAAEC,CAAA,EAAG,uCAAyC;EAAAD,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,KAAO;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,mCAAqC;EAAAD,GAAA,EAAK;AAAU,GACpE;AAaM,MAAAE,KAAA,GAAQC,gBAAiB,UAAST,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}